// https://www.typescriptlang.org/docs/handbook/compiler-options.html
{
  "compilerOptions": {
    "target": "ES6",
    "module": "commonjs",
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "esModuleInterop": true,
    "baseUrl": "./",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@/api": [
        "src/api/*"
      ],
      "components/*": ["src/components/*"],
      "pages/*": ["src/pages/*"],
      "assets/*": ["src/assets/*"]
    },
  },
  "include": [
    "src/**/*"
, "todo/react-plan"  ],
  "exclude": [
    "node_modules",
    "dist",
    "build",
  ]
}
