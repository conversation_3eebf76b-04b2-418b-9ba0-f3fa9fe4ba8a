## 开发

```bash
# 克隆项目
git clone https://gitee.com/KonBAI-Q/carbon-flowable.git

# 进入项目目录
cd ruoyi-ui

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

# git submodule 使用
```bash
# 关联子模块
git submodule add  $submodule_url $submodule_dir

# 拉取代码
git submodule update --init --recursive

# 更新子模块
git submodule update

```