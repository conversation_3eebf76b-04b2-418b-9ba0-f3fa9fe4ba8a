// 项目优化 https://blog.csdn.net/susanliy/article/details/132104582

'use strict';
const path = require('path');
const webpack = require('webpack');
const ProgressBarPlugin = require('progress-bar-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
// 显示文件构建速度
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
// 缓存文件，提升构建速度
const HardSourceWebpackPlugin = require('hard-source-webpack-plugin');
// 使用 DllPlugin, 将第三方依赖库抽出，不编译
const DllReferencePlugin = require('webpack/lib/DllReferencePlugin');
// 更好的视觉输出
const webpackDashboard = require('webpack-dashboard/plugin');

const { codeInspectorPlugin } = require('code-inspector-plugin');

const chalk = require('chalk');

const BASE = require('./src/utils/base');
const { version } = require('./script/version.json');
const Timestamp = new Date().getTime();

const packageName = require('./package.json').name;

const getPublicPath = () => {
  return process.env.NODE_ENV === 'production'
    ? process.env.VUE_APP_PUBLIC_PATH
    : 'http://localhost:1024/';
};

function resolve(dir) {
  return path.join(__dirname, dir);
}

const CompressionPlugin = require('compression-webpack-plugin');

const name = process.env.VUE_APP_TITLE || '碳效物模型'; // 网页标题

const port = process.env.port || process.env.npm_config_port || 80; // 端口

const isProd = process.env.NODE_ENV === 'production';

// 环境信息打印
console.table([
  {
    envText: process.env.NODE_ENV,
    publicPath: process.env.VUE_APP_PUBLIC_PATH,
    baseApi: process.env.VUE_APP_BASE_API,
  },
]);

// 添加包分析工具
const addBundleAnalyzer = webpackConfig => {
  if (process.env.NPM_CONFIG_ANALYZER) {
    const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
    webpackConfig.plugin('webpack-bundle-analyzer').use(BundleAnalyzerPlugin);
  }
};

// 优化打包行为 —— 移除打包后的 js.map 文件和 console 输出
// 注意，只能移除默认形式的 console; 无法移除形如 let log = console.log; log(111) 这种形式的输出
const upgradeMinimizer = webpackConfig => {
  webpackConfig.optimization.minimizer('terser').tap(args => {
    args[0].terserOptions.compress.drop_console = true;
    args[0].terserOptions.compress.drop_debugger = true;
    args[0].terserOptions.compress.pure_funcs = ['console.log'];
    return args;
  });
};

const setSpeedMeasurePlugins = () => {
  if (!isProd) {
    return [];
  }
  return [new SpeedMeasurePlugin()];
};

const setDllReferencePlugin = () => {
  return [
    new DllReferencePlugin({
      context: process.cwd(),
      scope: getPublicPath(),
      manifest: require('./public/vendor/vue-manifest.json'),
    }),
    new DllReferencePlugin({
      context: process.cwd(),
      scope: getPublicPath(),
      manifest: require('./public/vendor/plugin-manifest.json'),
    }),
    new DllReferencePlugin({
      context: process.cwd(),
      scope: getPublicPath(),
      manifest: require('./public/vendor/ui-manifest.json'),
    }),
  ];
};

const setDevServer = () => {
  if (process.env.VUE_APP_PACK_ENV !== 'live') return {};
  return {
    public: '************:9090',
  };
};

// vue.config.js 配置说明
// 官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: getPublicPath(),
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: 'dist',
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: 'static',
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: process.env.ENV !== 'production',
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: process.env.ENV !== 'production',
  // webpack-dev-server 相关配置
  devServer: {
    before(app) {},
    host: '0.0.0.0',
    ...setDevServer(),
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    port: port,
    open: true,
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxyc
      [process.env.VUE_APP_BASE_API]: {
        // target: `http://*************:8080/`,
        // target: `http://************/dev-api`,
        target: `http://**************:48080/admin-api`,
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: '',
        },
      },
    },
    disableHostCheck: true,
  },
  css: {
    loaderOptions: {
      scss: {
        /* 自动引入全局scss文件 */
        additionalData: `
          @import "./src/components/FormDesigner/src/styles/global.scss";
        `,
      },
    },
    // 忽略css文件打包后的顺序
    extract: isProd
      ? {
        ignoreOrder: false,
        filename: `static/css/[name].[contenthash:8].css`,
        chunkFilename: `static/css/[name].[contenthash:8].css`,
      }
      : false,
  },

  configureWebpack: {
    module: {
      // 如果一些第三方模块没有AMD/CommonJS规范版本, 则不进行转化和解析
      noParse: /^(lodash|moment)$/,
    },
    name: name,
    devtool: 'source-map',
    resolve: {
      alias: {
        '@': resolve('src'),
        '@@': resolve('src/components/FormDesigner/src'),
        vue$: 'vue/dist/vue.esm.js',
        // 保持父子项目 vue 统一
        vue: resolve('node_modules/vue'),
      },
    },
    output: isProd
      ? {
        filename: `static/js/[name].[contenthash:8].js`,
        chunkFilename: `static/js/[name].[contenthash:8].js`,
        library: `${packageName}-[name]`,
        libraryTarget: 'umd',
        jsonpFunction: `webpackJsonp_${packageName}`,
      }
      : {
        filename: `static/js/[name].[hash].js`,
        chunkFilename: `static/js/[name].[hash].js`,
        library: `${packageName}-[name]`,
        libraryTarget: 'umd',
        jsonpFunction: `webpackJsonp_${packageName}`,
      },
    plugins: [
      // 开发时快速定位到代码位置
      codeInspectorPlugin({
        bundler: 'webpack',
        editor: 'webstorm',
        hideDomPathAttr: true,
      }),
      new webpackDashboard(), // add
      // 将指定文件移入 dist
      new CopyWebpackPlugin([
        {
          from: path.join(__dirname, './script/version.json'),
          to: path.join(__dirname, './dist/version.json'),
        },
      ]),
      new ProgressBarPlugin({
        format: ' build [:bar] ' + chalk.green.bold(':percent') + ' (:elapsed seconds)',
        clear: false,
      }),
      new CompressionPlugin({
        test: /\.js$|\.html$|\.css$|\.jpg$|\.jpeg$|\.png/, // 需要压缩的文件类型
        threshold: 10240, // 归档需要进行压缩的文件大小最小值，我这个是10K以上的进行压缩
        minRatio: 0.8,
        deleteOriginalAssets: false, // 是否删除原文件
      }),
      // 缓存文件
      new HardSourceWebpackPlugin(),
      // 开启dll
      ...setDllReferencePlugin(),
      // 文件打包速度
      ...setSpeedMeasurePlugins(),
    ],
  },
  chainWebpack(config) {
    config.plugins.delete('preload'); // TODO: need test
    config.plugins.delete('prefetch'); // TODO: need test
    upgradeMinimizer(config);
    addBundleAnalyzer(config);
    // fileContenthash(config);
    // set svg-sprite-loader
    //  开启多线程编译
    config.module
      .rule('js')
      .use('thread-loader')
      .loader('thread-loader')
      .tap(options => {
        // 设置 thread-loader 的选项
        options = {
          workers: 3,
        };
        return options;
      });
    config.module.rule('svg').exclude.add(resolve('src/assets/icons')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })
      .end();
    // bpmn 流程图规则校验
    config.module
      .rule('bpmnlintrc')
      .test(/\.bpmnlintrc$/)
      .use('bpmnlint-loader')
      .loader('bpmnlint-loader')
      .end();
    config.module.rule('svg').exclude.add(resolve('src/components/FormDesigner/src/icons')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/components/FormDesigner/src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })
      .end();
    config.plugin('html').tap(args => {
      args[0].title = name;
      // 如果变更了 webpack.dll.conf 内配置的依赖文件，需要将文件路径后加上时间戳参数，防止缓存
      args[0].vueDll = `${getPublicPath()}/vendor/vue.dll.js`;
      args[0].pluginDll = `${getPublicPath()}/vendor/plugin.dll.js`;
      args[0].uiDll = `${getPublicPath()}/vendor/ui.dll.js`;
      return args;
    });
    config.when(process.env.NODE_ENV !== 'staging', config => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();
      // config.optimization.splitChunks({
      //   chunks: 'all',
      //   cacheGroups: {
      //     libs: {
      //       name: 'chunk-libs',
      //       test: /[\\/]node_modules[\\/]/,
      //       priority: 10,
      //       chunks: 'initial', // only package third parties that are initially dependent
      //     },
      //     commons: {
      //       name: 'chunk-commons',
      //       test: resolve('src/components'), // can customize your rules
      //       minChunks: 3, //  minimum common number
      //       priority: 5,
      //       reuseExistingChunk: true,
      //     },
      //   },
      // });
      config.optimization.splitChunks({
        chunks: 'all',
        minSize: 20000, // 最小拆分块大小（字节），避免过度拆分
        maxSize: 240000, // 单块最大体积（字节），超出会强制拆分
        minChunks: 1, // 最少被引用次数
        maxAsyncRequests: 30, // 异步加载最大请求数
        maxInitialRequests: 15, // 初始加载最大请求数
        automaticNameDelimiter: '~',
        cacheGroups: {
          // 优先级：vendors > axios > libs > commons
          // visactor: {
          //   test: /[\\/]node_modules[\\/]@visactor[\\/]vtable-gantt[\\/]/,
          //   name: 'chunk-visactor',
          //   priority: 25,
          //   chunks: 'all',
          //   enforce: true,
          // },
          // vueOffice: {
          //   test: /[\\/]node_modules[\\/@]vue-office[\\/]/,
          //   name: 'chunk-vue-office',
          //   priority: 15,
          //   chunks: 'all',
          //   enforce: true,
          // },
          bpmnJs: {
            test: /[\\/]node_modules[\\/]bpmn-js[\\/]/,
            name: 'chunk-bpmn-js',
            priority: 25,
            chunks: 'all',
            enforce: true,
          },
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            name: 'chunk-vendors',
            priority: 15,
            chunks: 'initial',
            enforce: true,
          },
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial',
            reuseExistingChunk: true,
          },
          commons: {
            name: 'chunk-components',
            test: resolve('src/components'),
            minChunks: 3,
            priority: 5,
            reuseExistingChunk: true,
          },
          // 单独拆分大型依赖（示例）
          // bigjs: {
          //   test: /[\\/]node_modules[\\/]big.js[\\/]/,
          //   name: 'chunk-bigjs',
          //   priority: 8,
          //   chunks: 'all',
          // },
        },
      });
      config.optimization.runtimeChunk('single'),
      {
        from: path.resolve(__dirname, './public/robots.txt'), // 防爬虫文件
        to: './', // 到根目录下
      };
    });
  },
};
