<template>
  <div
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor:
        settings.sideTheme === 'theme-dark'
          ? variables.menuBackground
          : variables.menuLightBackground,
    }"
  >
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :active-text-color="settings.theme"
        :background-color="
          settings.sideTheme === 'theme-dark'
            ? variables.menuBackground
            : variables.menuLightBackground
        "
        :collapse="isCollapse"
        :collapse-transition="false"
        :default-active="activeMenu"
        :text-color="
          settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor
        "
        :unique-opened="true"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :base-path="route.path"
          :item="route"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import Logo from './Logo';
import SidebarItem from './SidebarItem';
import variables from '@/assets/styles/variables.scss';

// 需要高亮父页面的路由
const activeMenuRouter = {
  '/iot/packages/classes': '/iot/packages',
  '/iot/packages/classes/detail': '/iot/packages',
  '/form/custom/create': '/form/custom',
  '/form/custom/info': '/form/custom',
  '/process/model/create': '/process/model',
  '/process/form/create': '/process/form',
};
// 需要收起左侧菜单的路由
const hideMenuRouter = [
  '/process/model/create',
  '/process/form/create'
];

export default {
  components: { SidebarItem, Logo },
  watch: {
    $route(currentRoute) {
      // 收起菜单栏
      if (hideMenuRouter.includes(currentRoute.path)) {
        this.$store.dispatch('app/closeSideBar', { withoutAnimation: false });
      } else {
        this.$store.dispatch('app/openSideBar');
      }
    }
  },
  computed: {
    ...mapState(['settings']),
    ...mapGetters(['sidebarRouters', 'sidebar']),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }

      if (activeMenuRouter[route.path]) {
        return activeMenuRouter[route.path];
      }

      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
};
</script>
