import { getDevApiIotClassesList } from '@/api/iot/classes-controller.js';

export const fields = {
  meetingName: {
    label: '文件名称',
    prop: 'fileName',
  },

  fileNo: {
    label: '文件编号',
    prop: 'fileNo',
  },

  fileType: {
    label: '文件类型',
    prop: 'fileType',
  },

  createDate: {
    label: '发起日期',
    prop: 'createDate',
  },

  creatorName: {
    label: '发起人',
    prop: 'creatorName',
  },

  createTime: {
    label: '当前执行人',
    prop: 'createTime',
  },

  viewStyle: {
    label: '流程状态',
    prop: 'viewStyle',
  },

  status: {
    label: '回复状态',
    prop: 'status',
  },
  remark: {
    label: '关联文件名称',
    prop: 'remark',
  },
};

export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: 'auto',
    labelPosition: 'left',
    size: 'small',
  },
  formItemJson: [
    {
      label: fields.fileNo.label,
      prop: fields.fileNo.prop,
      type: 'Input',
      isEnter: true,
      options: {
        clearable: true,
        placeholder: '请输入' + fields.fileNo.label,
      },
    },
    {
      label: fields.meetingName.label,
      prop: fields.meetingName.prop,
      type: 'Input',
      isEnter: true,
      options: {
        clearable: true,
        placeholder: '请输入' + fields.meetingName.label,
      },
    },
    // {
    //   label: fields.status.label,
    //   prop: fields.status.prop,
    //   type: 'Select',
    //   isEnter: true,
    //   option: [
    //     {
    //       value: '123',
    //       label: '123',
    //     },
    //   ],
    //   options: {
    //     clearable: true,
    //     placeholder: '请选择' + fields.status.label,
    //   },
    // },
    // {
    //   label: fields.viewStyle.label,
    //   prop: fields.viewStyle.prop,
    //   type: 'Select',
    //   isEnter: true,
    //   option: [
    //     {
    //       value: '123',
    //       label: '123',
    //     },
    //   ],
    //   options: {
    //     clearable: true,
    //     placeholder: '请选择' + fields.viewStyle.label,
    //   },
    // },
    // {
    //   label: fields.creatorName.label,
    //   prop: fields.creatorName.prop,
    //   type: 'Input',
    //   isEnter: true,
    //   options: {
    //     clearable: true,
    //     placeholder: '请输入' + fields.creatorName.label,
    //   },
    // },
    // {
    //   label: fields.createTime.label,
    //   prop: fields.createTime.prop,
    //   type: 'Input',
    //   isEnter: true,
    //   options: {
    //     clearable: true,
    //     placeholder: '请输入' + fields.createTime.label,
    //   },
    // },
    {
      label: fields.createDate.label,
      prop: fields.createDate.prop,
      type: 'DatePicker',
      isEnter: true,
      options: {
        type: 'daterange',
        valueFormat: 'yyyy-MM-dd',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        rangeSeparator: '至'
      },
    },
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary',
        },
      },
      {
        label: '重置',
        type: 'Reset',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini',
        },
      },
    ],
  },
};

export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '发起',
      permi: '',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};

export const TableJson = {
  options: {
    rowKey: 'classId',
    defaultExpandAll: true,
    treeProps: { children: 'children', hasChildren: 'hasChildren' },
  },
  columnJson: {
    showSelect: false,
    data: [
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.meetingName.label,
        prop: fields.meetingName.prop,
        showOverflowTooltip: true,
        fixed: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fileNo.label,
        prop: fields.fileNo.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fileType.label,
        prop: fields.fileType.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.createDate.label,
        prop: fields.createDate.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.creatorName.label,
        prop: fields.creatorName.prop,
        showOverflowTooltip: true,
      },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   label: fields.createTime.label,
      //   prop: fields.createTime.prop,
      //   showOverflowTooltip: true,
      // },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   label: fields.viewStyle.label,
      //   prop: fields.viewStyle.prop,
      //   showOverflowTooltip: true,
      // },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   label: fields.status.label,
      //   prop: fields.status.prop,
      //   showOverflowTooltip: true,
      // },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   label: fields.remark.label,
      //   prop: fields.remark.prop,
      //   showOverflowTooltip: true,
      // },
      {
        align: 'center',
        headerAlign: 'center',
        label: '操作',
        type: 'func',
        fixed: 'right',
        buttonList: [
          {
            label: '查看',
            type: 'Info',
            permi: '',
            options: {
              icon: 'el-icon-view',
              size: 'mini',
              type: 'text',
            },
          },
          // {
          //   label: '删除',
          //   type: 'Delete',
          //   permi: '',
          //   options: {
          //     icon: 'el-icon-delete',
          //     size: 'mini',
          //     type: 'text',
          //   },
          // },
        ],
      },
    ],
  },
};

export const InputDialogDialogJson = {
  type: 'Add',
  title: '类拓扑',
  api: getDevApiIotClassesList,
  options: {
    width: '980px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  tableConfig: {
    queryFormJson: QueryFormJson,
    pageButtonsJson: {},
    tableJson: {
      columnJson: {
        showSelect: false,
        showIndex: false,
        selectIdKey: 'fileNo',
        data: [
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.meetingName.label,
            prop: fields.meetingName.prop,
            showOverflowTooltip: true,
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.fileNo.label,
            prop: fields.fileNo.prop,
            showOverflowTooltip: true,
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.fileType.label,
            prop: fields.fileType.prop,
            showOverflowTooltip: true,
            dictType: 'iot_class_type',
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.creatorName.label,
            prop: fields.creatorName.prop,
            showOverflowTooltip: true,
            dictType: 'iot_access_type',
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.createTime.label,
            prop: fields.createTime.prop,
            showOverflowTooltip: true,
          },
        ],
      },
    },
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary',
      },
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: '',
      },
    },
  ],
};

// TOTO: 假数据接入接口后删除
export const domeData = {
  total: 5,
  rows: [
    {
      classId: 1,
      packageName: 'cs_bao',
      className: 'cs_12',
      meetingName: '测试12',
      descriptions: null,
      classesType: '继承',
      creatorName: 'default',
      createDate: '21',
      defineJson: null,
      viewStyle: '视图样式',
      solidworks: null,
      parentId: null,
      objTableName: '12',
      createBy: '',
      createTime: null,
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
    {
      classId: 2,
      packageName: 'cs_bao',
      className: 'cs_1211',
      meetingName: '测试——12',
      descriptions: null,
      classesType: 'NOT',
      creatorName: 'public',
      createDate: null,
      defineJson: null,
      viewStyle: '12',
      solidworks: null,
      objTableName: null,
      parentId: 1,
      createBy: '',
      createTime: null,
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
    {
      classId: 3,
      packageName: 'cs_bao',
      className: 'cw_123',
      meetingName: '测试',
      descriptions: null,
      classesType: 'EXTENDS',
      creatorName: 'public',
      createDate: 'cs_1211',
      defineJson: null,
      parentId: 1,
      viewStyle: '123123',
      solidworks: null,
      objTableName: null,
      createBy: '',
      createTime: null,
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
    {
      classId: 4,
      packageName: 'cs_bao',
      className: 'cs_new',
      meetingName: '测试类-新版',
      descriptions: null,
      classesType: 'EXTENDS',
      creatorName: 'private',
      createDate: 'cw_123',
      defineJson: null,
      viewStyle: '11',
      parentId: 1,
      solidworks: null,
      objTableName: null,
      createBy: '',
      createTime: null,
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
    {
      classId: 5,
      packageName: 'cs_bao',
      className: 'sc_123',
      meetingName: '测试111',
      descriptions: null,
      classesType: 'EXTENDS',
      creatorName: 'private',
      createDate: 'cs_12',
      defineJson: null,
      parentId: 4,
      viewStyle: null,
      solidworks: null,
      objTableName: null,
      createBy: 'admin',
      createTime: '2023-07-21 20:44:14',
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
  ],
  code: 200,
  msg: '查询成功',
};
