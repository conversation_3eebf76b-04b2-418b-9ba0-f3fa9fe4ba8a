<template>
  <div class="table-container">
    <el-table v-loading="loading" :data="tableData" height="80vh" style="width: 100%">
      <el-table-column align="center" label="工程编号">
        <el-table-column
          prop="unitProj"
          align="center"
          label="单位工程"
          width="40"
        ></el-table-column>
        <el-table-column
          prop="subUnitProj"
          align="center"
          label="子单位工程"
          width="40"
        ></el-table-column>
        <el-table-column
          prop="divisionProj"
          align="center"
          label="分部工程"
          width="40"
        ></el-table-column>
        <el-table-column
          prop="subDivisionProj"
          align="center"
          label="子分部工程"
          width="40"
        ></el-table-column>
        <el-table-column
          prop="subItemProj"
          align="center"
          label="分项工程"
          width="40"
        ></el-table-column>
        <el-table-column
          prop="inspectionProj"
          align="center"
          label="检验批"
          width="40"
        ></el-table-column>
      </el-table-column>
      <el-table-column prop="projName" align="left" label="工程名称"></el-table-column>
      <el-table-column align="center" label="验收单位">
        <el-table-column prop="c01" align="center" label="施工单位" width="40">
          <template slot-scope="scope">
            <i class="el-icon-check" style="font-size: 14px" v-if="+scope.row.c01 === 1"></i>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column prop="c02" align="center" label="总承包单位" width="40">
          <template slot-scope="scope">
            <i class="el-icon-check" style="font-size: 14px" v-if="+scope.row.c02 === 1"></i>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column prop="c03" align="center" label="勘察单位" width="40">
          <template slot-scope="scope">
            <i class="el-icon-check" style="font-size: 14px" v-if="+scope.row.c03 === 1"></i>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column prop="c04" align="center" label="设计单位" width="40">
          <template slot-scope="scope">
            <i class="el-icon-check" style="font-size: 14px" v-if="+scope.row.c04 === 1"></i>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column prop="c05" align="center" label="监理单位" width="40">
          <template slot-scope="scope">
            <i class="el-icon-check" style="font-size: 14px" v-if="+scope.row.c05 === 1"></i>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column prop="c06" align="center" label="建设单位" width="40">
          <template slot-scope="scope">
            <i class="el-icon-check" style="font-size: 14px" v-if="+scope.row.c06 === 1"></i>
            <span v-else></span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column prop="no" fixed="right" align="center" label="标准编号">
        <template slot-scope="{ row, $index }">
          <el-button
            style="white-space: break-spaces; line-height: 1.4"
            type="text"
            @click="handleStart(row.procdef)"
            >{{ row.procdef ? row.procdef.formName : '' }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-tabs class="project-tabs" v-model="activeProject" type="card" @tab-click="handleTabClick">
      <el-tab-pane
        v-for="(item, index) in projectData"
        :key="item.divisionId"
        :label="item.projName"
        :name="`${item.divisionId}-${index}`"
      >
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { postBpmQaDivisionListAll } from '@/api/v2/qa/project-division-controller.js';

export default {
  data() {
    return {
      tableData: [],
      projectData: [],
      activeProject: '',
      loading: false,
    };
  },
  mounted() {
    this.getProjectData();
  },
  methods: {
    handleTabClick(tab) {
      const { name } = tab;
      const index = name.split('-')[1];
      this.getList(this.projectData[index]);
    },
    handleStart(procdef) {
      const { deploymentId, processService, formDataPath, fdId, procdefId } = procdef || {};
      this.$router.push({
        path: '/carbon-flowable/work/start',
        query: {
          deployId: deploymentId,
          definitionId: procdefId,
          processService,
          formDataPath,
          fdId,
        },
      });
    },

    async getProjectData() {
      const { data, code } = await postBpmQaDivisionListAll({
        parentId: 0,
      });
      if (code === 200) {
        this.projectData = data || [];
        this.activeProject = `${data[0].divisionId}-0`;
        this.getList(data[0]);
      }
    },
    flattenTree(tree) {
      const result = [];
      const flatten = (nodes) => {
        nodes.forEach(node => {
          result.push(node);
          if (node.children && node.children.length) {
            flatten(node.children);
          }
        });
      };
      flatten(tree);
      return result;
    },
    async getList(item) {
      this.loading = true;
      const { data, code } = await postBpmQaDivisionListAll({
        ancestors: `0,${item.divisionId},`,
      });
      const formatData = data.map(item => {
        const { unitProj, subUnitProj, divisionProj, subDivisionProj, subItemProj, inspectionProj } = item
        if (subUnitProj) {
          item.projName = `    ${item.projName}`
        } else if (divisionProj) {
           item.projName = `            ${item.projName}`
        } else if (subDivisionProj) {
           item.projName = `                  ${item.projName}`
        } else if (subItemProj) {
           item.projName = `                        ${item.projName}`
        } else if (inspectionProj) {
           item.projName = `                                    ${item.projName}`
        }

        return item
      })

      console.log('object :>>formatData ', formatData);
      if (code === 200) {
        let tree = [{
          children:  this.handleTree(formatData, 'divisionId', 'parentId') || [],
          ...item,
        }]
        this.tableData = this.flattenTree(tree);
      }
      this.loading = false;
    },
  },
};
</script>
<style scoped lang="scss">
.table-container {
  width: 100%;
  height: 82vh;
  display: flex;
  flex-direction: column;
  position: relative;
  padding-bottom: 40px;

  .project-tabs {
    position: absolute;
    left: 0;
    bottom: -15px;
    width: 100%;
  }

  ::v-deep {
    .el-tabs--card > .el-tabs__header {
      border-right: 1px solid #dfe4ed;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__item {
      border-bottom: 1px solid #dfe4ed;
    }
    .el-table .cell {
      white-space: pre-wrap;
    }
  }
}
</style>
