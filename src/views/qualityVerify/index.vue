<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="按单元工程" name="unitProject">
          <UnitProject></UnitProject>
      </el-tab-pane>
      <el-tab-pane label="按标准规范" name="standards">按标准规范</el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import UnitProject from "./components/unitProject";
export default {
  components: {
    UnitProject
  },
  data() {
    return {
      activeName: 'unitProject'
    };
  },
  created() {},
  mounted() {},
  computed: {},
  methods: {
    handleClick(tab) {
      console.log('object :>> ', tab);
    },
  },
};
</script>
<style scoped lang="scss"></style>
