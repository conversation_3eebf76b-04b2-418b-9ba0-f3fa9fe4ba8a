<template>
  <div class="base-tree">
    <el-tree
      ref="treeRef"
      :data="treeData"
      :props="treeProps"
      :node-key="nodeKey"
      :default-expanded-keys="defaultExpandedKeys"
      :show-checkbox="showCheckbox"
      :default-checked-keys="defaultCheckedKeys"
      :filter-node-method="filterNode"
      :draggable="draggable"
      :allow-drop="allowDrop"
      :allow-drag="allowDrag"
      @node-click="handleNodeClick"
      @check="handleCheck"
      @node-drag-start="handleDragStart"
      @node-drag-enter="handleDragEnter"
      @node-drag-leave="handleDragLeave"
      @node-drag-over="handleDragOver"
      @node-drag-end="handleDragEnd"
      @node-drop="handleDrop"
    >
      <template #default="{ node, data }">
        <slot name="custom" :node="node" :data="data">
          <span>{{ node.label }}</span>
        </slot>
      </template>
    </el-tree>
  </div>
</template>

<script>
export default {
  name: 'BaseTree',

  props: {
    // 树的数据
    treeData: {
      type: Array,
      required: true,
      default: () => []
    },
    // 配置选项
    treeProps: {
      type: Object,
      default: () => ({
        children: 'children',
        label: 'label'
      })
    },
    // 每个树节点用来作为唯一标识的属性
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 默认展开的节点的 key 的数组
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    // 是否显示勾选框
    showCheckbox: {
      type: Boolean,
      default: false
    },
    // 默认勾选的节点的 key 的数组
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    // 是否开启拖拽
    draggable: {
      type: Boolean,
      default: false
    },
    // 判断节点能否被拖拽
    allowDrag: {
      type: Function,
      default: () => true
    },
    // 拖拽时判定目标节点能否被放置
    allowDrop: {
      type: Function,
      default: () => true
    }
  },

  methods: {
    // 过滤节点的方法
    filterNode(value, data) {
      if (!value) return true
      return data[this.treeProps.label].includes(value)
    },

    // 对外暴露的方法
    getTree() {
      return this.$refs.treeRef
    },

    setChecked(key, checked) {
      this.$refs.treeRef?.setChecked(key, checked)
    },

    setCheckedKeys(keys) {
      this.$refs.treeRef?.setCheckedKeys(keys)
    },

    getCheckedKeys() {
      return this.$refs.treeRef?.getCheckedKeys()
    },

    filter(val) {
      this.$refs.treeRef?.filter(val)
    },

    // 事件处理
    handleNodeClick(data, node) {
      this.$emit('node-click', data, node)
    },

    handleCheck(data, { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes }) {
      this.$emit('check', data, { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes })
    },

    handleDragStart(node, ev) {
      this.$emit('node-drag-start', node, ev)
    },

    handleDragEnter(draggingNode, dropNode, ev) {
      this.$emit('node-drag-enter', draggingNode, dropNode, ev)
    },

    handleDragLeave(draggingNode, dropNode, ev) {
      this.$emit('node-drag-leave', draggingNode, dropNode, ev)
    },

    handleDragOver(draggingNode, dropNode, ev) {
      this.$emit('node-drag-over', draggingNode, dropNode, ev)
    },

    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      this.$emit('node-drag-end', draggingNode, dropNode, dropType, ev)
    },

    handleDrop(draggingNode, dropNode, dropType, ev) {
      this.$emit('node-drop', draggingNode, dropNode, dropType, ev)
    }
  }
}
</script>

<style scoped>
.base-tree {
  width: 100%;
}
</style>
