<template>
  <div class="app-container">
    <CoustomTable
      :page-buttons-json="pageButtonsJson"
      :paginations="defaultQueryParams"
      :query-form-json="queryFormJson"
      :table-data="tableData"
      :table-json="tableJson"
      :total="total"
      @onButton="handlerButton"
      @onPageButton="handlerPageButton"
      @onPagination="handlerPagination"
      @onSearch="handlerSearch"
      @onTableRowClick="handlerTableRowClick"
    />

    <CoustomTableDialog
      ref="formDialog"
      :config="dialogJson"
      :init-data="{ defaultSelectId: '' }"
      :visible.sync="dialogVisible"
      @onSubmit="handlerSubmit"
    />
  </div>
</template>
<script>
import CoustomTable from '@/components/CoustomTable';
import CoustomFormDialog from '@/components/CoustomFormDialog';
import CoustomTableDialog from '@/components/CoustomTableDialog';
import { Table<PERSON>son, QueryForm<PERSON>son, PageButtonsJson, DialogJson, InputDialogDialogJson, domeData } from './constants';

import {
  getDevApiIotTopologyList,
  postDevApiIotTopology,
  getDevApiIotTopologyTopologyId,
  deleteDevApiIotTopologyTopologyId,
  putDevApiIotTopology,
} from '@/api/iot/ClassesTopology';
import pageMixin from '@/mixin/page';
export default {
  name: 'ClassesTopologyMap',
  components: {
    CoustomTable,
    CoustomTableDialog
  },
  mixins: [pageMixin],
  dicts: ['iot_class_type', 'iot_access_type'],
  provide() {
    return {
      dictData: this.dict,
    };
  },
  data() {
    return {
      queryFormJson: QueryFormJson,
      pageButtonsJson: PageButtonsJson,
      tableJson: TableJson,
      dialogJson: InputDialogDialogJson,
      associationFields: ['packageName'],
      // 不需要分页
      hiddenPagination: true,
      // 父节点 ID
      parentId: 0,
    };
  },
  computed: {
    packageName() {
      const { query } = this.$route;
      return query.packageName;
    },
  },
  created() { },
  mounted() {
    this.getList({ packageName: this.packageName }, getDevApiIotTopologyList);
  },

  methods: {
    handleResponse(rows = []) {
      return this.handleTree(rows, 'topologyId');
      // TODO: 假数据联调后需要删除
      // return this.handleTree(domeData.rows, 'classId');
    },

    async handlerSubmit(formData) {
      const { packageName, classId, className } = formData;
      if (this.parentId === classId) {
        this.$message.error('选中的类不能与父类相同！');
        return;
      }

      this.handlerSave({
        formData: {
          parentId: this.parentId,
          packageName,
          classId,
          className
        },
        apis: {
          addApi: postDevApiIotTopology,
          editApi: putDevApiIotTopology,
        },
        editParams: ['topologyId'],
      });
    },

    handlerTableRowClick(item) {
      this.goToPage({
        path: '/carbon-iot/packages/classes/detail',
        query: {
          className: item.className,
        },
      });
    },

    // 新增按钮事件
    handlerPageButton(item) {
      if (item.type === 'Add') {
        this.parentId = 0;
      }
      this.pageButton({ item });
    },

    async getInfo(id) {
      this.getInfoData({ id, api: getDevApiIotTopologyList });
    },

    deleteItem(id) {
      this.deleteTableItem({ id, api: deleteDevApiIotTopologyTopologyId });
    },

    handlerButton(btnItem) {
      if (btnItem.item.type === 'Add') {
        this.parentId = btnItem.row.classId;
      }
      this.tableButton({ btnItem, idKey: 'topologyId', });
    },
  },
};
</script>
<style scoped lang="scss"></style>
