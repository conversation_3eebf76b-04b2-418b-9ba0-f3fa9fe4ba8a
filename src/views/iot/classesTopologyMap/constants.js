import { getDevApiIotClassesList } from '@/api/iot/classes-controller.js';

export const fields = {
  classDispname: {
    label: '类中文名称',
    prop: 'classDispname',
  },

  className: {
    label: '类英文名称',
    prop: 'className',
  },

  classesType: {
    label: '类类型',
    prop: 'classesType',
  },

  classesExtends: {
    label: '继承父类code',
    prop: 'classesExtends',
  },

  accessType: {
    label: '访问类型',
    prop: 'accessType',
  },

  createTime: {
    label: '创建时间',
    prop: 'createTime',
  },

  viewStyle: {
    label: '视图样式',
    prop: 'viewStyle',
  },

  remark: {
    label: '描述',
    prop: 'remark',
  },
};

export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: 'auto',
    labelPosition: 'left',
    size: 'small',
  },
  formItemJson: [
    {
      label: fields.classDispname.label,
      prop: fields.classDispname.prop,
      type: 'Input',
      isEnter: true,
      options: {
        clearable: true,
        placeholder: '请输入' + fields.classDispname.label,
      },
    },
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary',
        },
      },
      {
        label: '重置',
        type: 'Reset',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini',
        },
      },
    ],
  },
};

export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: '',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};

export const TableJson = {
  options: {
    rowKey: 'classId',
    defaultExpandAll: true,
    treeProps: { children: 'children', hasChildren: 'hasChildren' },
  },
  columnJson: {
    showSelect: false,
    data: [
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.classDispname.label,
        prop: fields.classDispname.prop,
        showOverflowTooltip: true,
        isLink: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.className.label,
        prop: fields.className.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.classesType.label,
        prop: fields.classesType.prop,
        showOverflowTooltip: true,
        dictType: 'iot_class_type',
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.accessType.label,
        prop: fields.accessType.prop,
        showOverflowTooltip: true,
        dictType: 'iot_access_type',
      },
      // {
      //   align: "left",
      //   headerAlign: "left",
      //   label: "描述",
      //   prop: "remark",
      //   showOverflowTooltip: true,
      // },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.createTime.label,
        prop: fields.createTime.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'center',
        headerAlign: 'center',
        label: '操作',
        type: 'func',
        buttonList: [
          {
            label: '新增',
            type: 'Add',
            permi: '',
            options: {
              icon: 'el-icon-plus',
              size: 'mini',
              type: 'text',
            },
          },
          {
            label: '删除',
            type: 'Delete',
            permi: '',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text',
            },
          },
        ],
      },
    ],
  },
};

export const InputDialogDialogJson = {
  type: 'Add',
  title: '类拓扑',
  api: getDevApiIotClassesList,
  options: {
    width: '980px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  tableConfig: {
    queryFormJson: QueryFormJson,
    pageButtonsJson: {},
    tableJson: {
      columnJson: {
        showSelect: false,
        showIndex: false,
        selectIdKey: 'className',
        data: [
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.classDispname.label,
            prop: fields.classDispname.prop,
            showOverflowTooltip: true,
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.className.label,
            prop: fields.className.prop,
            showOverflowTooltip: true,
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.classesType.label,
            prop: fields.classesType.prop,
            showOverflowTooltip: true,
            dictType: 'iot_class_type',
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.accessType.label,
            prop: fields.accessType.prop,
            showOverflowTooltip: true,
            dictType: 'iot_access_type',
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.createTime.label,
            prop: fields.createTime.prop,
            showOverflowTooltip: true,
          },
        ],
      },
    },
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary',
      },
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: '',
      },
    },
  ],
};

// TOTO: 假数据接入接口后删除
export const domeData = {
  total: 5,
  rows: [
    {
      classId: 1,
      packageName: 'cs_bao',
      className: 'cs_12',
      classDispname: '测试12',
      descriptions: null,
      classesType: '继承',
      accessType: 'default',
      classesExtends: '21',
      defineJson: null,
      viewStyle: '视图样式',
      solidworks: null,
      parentId: null,
      objTableName: '12',
      createBy: '',
      createTime: null,
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
    {
      classId: 2,
      packageName: 'cs_bao',
      className: 'cs_1211',
      classDispname: '测试——12',
      descriptions: null,
      classesType: 'NOT',
      accessType: 'public',
      classesExtends: null,
      defineJson: null,
      viewStyle: '12',
      solidworks: null,
      objTableName: null,
      parentId: 1,
      createBy: '',
      createTime: null,
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
    {
      classId: 3,
      packageName: 'cs_bao',
      className: 'cw_123',
      classDispname: '测试',
      descriptions: null,
      classesType: 'EXTENDS',
      accessType: 'public',
      classesExtends: 'cs_1211',
      defineJson: null,
      parentId: 1,
      viewStyle: '123123',
      solidworks: null,
      objTableName: null,
      createBy: '',
      createTime: null,
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
    {
      classId: 4,
      packageName: 'cs_bao',
      className: 'cs_new',
      classDispname: '测试类-新版',
      descriptions: null,
      classesType: 'EXTENDS',
      accessType: 'private',
      classesExtends: 'cw_123',
      defineJson: null,
      viewStyle: '11',
      parentId: 1,
      solidworks: null,
      objTableName: null,
      createBy: '',
      createTime: null,
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
    {
      classId: 5,
      packageName: 'cs_bao',
      className: 'sc_123',
      classDispname: '测试111',
      descriptions: null,
      classesType: 'EXTENDS',
      accessType: 'private',
      classesExtends: 'cs_12',
      defineJson: null,
      parentId: 4,
      viewStyle: null,
      solidworks: null,
      objTableName: null,
      createBy: 'admin',
      createTime: '2023-07-21 20:44:14',
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
    },
  ],
  code: 200,
  msg: '查询成功',
};
