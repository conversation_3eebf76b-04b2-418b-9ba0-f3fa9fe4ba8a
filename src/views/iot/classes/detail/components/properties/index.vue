<template>
  <div class="properties">
    <div class="properties-layout">
      <div class="properties-layout-left">
        <ModuleMenu :class-name="className" @onMenuChange="handlerMenuChange" />
      </div>
      <div class="properties-layout-right">
        <el-tabs v-model="activeTab" @tab-click="handleClick">
          <el-tab-pane
            v-for="item in headTabs"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>

        <CoustomTable
          :page-buttons-json="pageButtonsJson"
          :query-form-json="queryFormJson[activeTab]"
          :table-data="tableData"
          :table-json="tableJson[activeTab]"
          :total="total"
          :loading="loading"
          :paginations="defaultQueryParams"
          @onButton="handlerButton"
          @onPageButton="handlerPageButton"
          @onSearch="handlerSearch"
          @onTableRowClick="handlerTableRowClick"
          @onPagination="handlerPagination"
        />
      </div>
    </div>

    <CoustomFormDialog
      ref="formDialog"
      :dialog-json="dialogJson[activeTab]"
      :init-data="initFormData"
      :visible.sync="dialogVisible"
      @onSubmit="handlerSubmit"
      @onDialogClose="handlerDialogClose"
    />
  </div>
</template>
<script>
import CoustomTable from '@/components/CoustomTable';
import CoustomFormDialog from '@/components/CoustomFormDialog';
import ModuleMenu from './components/ModuleMenu';
import {
  TableJson,
  QueryFormJson,
  PageButtonsJson,
  DialogJson,
  HeadMap,
  InoutType,
  HeadTabs,
  ServerApis,
  IdKeys,
} from './constants';
import pageMixin from '@/mixin/page';
export default {
  name: 'Properties',
  components: {
    CoustomTable,
    CoustomFormDialog,
    ModuleMenu,
  },
  mixins: [pageMixin],
  dicts: [
    'iot_rw_flag',
    'iot_property_type',
    'iot_property_unit',
    'iot_class_type',
    'iot_access_type',
    'iot_call_method_type',
  ],
  provide() {
    return {
      dictData: this.dict,
    };
  },
  props: {
    classesInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      queryFormJson: QueryFormJson,
      pageButtonsJson: PageButtonsJson,
      tableJson: TableJson,
      dialogJson: DialogJson,
      moduleName: null,
      // 关联字段，重置或搜索时，不清空
      associationFields: ['moduleName', 'className'],

      activeTab: HeadMap.ATTRIBUTE,
      headTabs: HeadTabs,
    };
  },

  computed: {
    className() {
      const { query } = this.$route;
      return query.className;
    },
  },

  created() {},
  mounted() {
    this.getList({}, ServerApis[this.activeTab].listApi);
  },

  methods: {
    handleClick() {
      this.getList({}, ServerApis[this.activeTab].listApi);
    },

    handlerMenuChange(menuItem) {
      this.$eventBus.$emit('resetForm');
      this.moduleName = menuItem.moduleName;
      this.handlerSearch({});
    },

    handlerTableRowClick(item) {},

    getInoutType(list = [], type) {
      return list.map(item => {
        const { dataSpecs, propertyDispname, propertyName, propertyType } = item;
        return {
          dataSpecs,
          propertyDispname,
          propertyName,
          propertyType,
          inoutType: type,
        };
      });
    },

    getEventData(formData) {
      const { inoutTypeIn, eventDispname, eventName, remark, eventType } = formData || {};
      return {
        eventDispname,
        eventName,
        eventType,
        remark,
        properties: [...this.getInoutType(inoutTypeIn, InoutType.IN)],
      };
    },

    getServerData(formData) {
      const { inoutTypeIn, inoutTypeOut, callMethod, remark, serviceDispname, serviceName } =
        formData || {};
      return {
        callMethod,
        remark,
        serviceDispname,
        serviceName,
        properties: [
          ...this.getInoutType(inoutTypeIn, InoutType.IN),
          ...this.getInoutType(inoutTypeOut, InoutType.OUT),
        ],
      };
    },

    async handlerSubmit(formData) {
      let params = {};
      if (this.activeTab === HeadMap.ATTRIBUTE) {
        params = {
          formData,
          editParams: [IdKeys[this.activeTab]],
        };
      }

      if (this.activeTab === HeadMap.SERVER) {
        params = {
          formData: this.getServerData(formData),
          editParams: [IdKeys[this.activeTab]],
        };
      }

      if (this.activeTab === HeadMap.EVENT) {
        params = {
          formData: this.getEventData(formData),
          editParams: [IdKeys[this.activeTab]],
        };
      }

      this.handlerSave({
        queryParams: { className: this.className, moduleName: this.moduleName },
        apis: {
          addApi: ServerApis[this.activeTab].addApi,
          editApi: ServerApis[this.activeTab].editApi,
        },
        ...params,
      });
    },

    // 新增按钮事件
    handlerPageButton(item) {
      this.pageButton({ item });
    },

    async getInfo(id) {
      this.getInfoData({ id, api: ServerApis[this.activeTab].infoApi });
    },

    deleteItem(id) {
      this.deleteTableItem({ id, api: ServerApis[this.activeTab].delApi });
    },

    handlerButton(btnItem) {
      this.tableButton({ btnItem, idKey: IdKeys[this.activeTab] });
    },
  },
};
</script>
<style scoped lang="scss">
.properties {
  width: 100%;
  padding: 12px 0;

  .properties-layout {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: center;

    &-left {
      width: 204px;
      border-right: 1px solid #ebebeb;
      margin-right: 16px;
      flex-shrink: 0;
    }

    &-right {
      flex: auto;
      min-width: 600px;
      height: 100%;
    }
  }
}
</style>
