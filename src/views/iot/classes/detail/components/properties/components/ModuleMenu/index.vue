<template>
  <div class="module-menu">
    <div class="module-menu-search">
      <el-input
        v-model="searchModule"
        clearable
        placeholder="请输入模块名称"
        @keyup.enter.native="handlerBulur"
        @bulur="handlerBulur"
        @clear="handlerBulur"
      >
        <i slot="suffix" class="el-icon-search module-menu-search-icon" @click="handlerBulur" />
      </el-input>
      <el-button
        icon="el-icon-plus"
        class="module-menu-add"
        type="text"
        @click="addItem"
      />
    </div>
    <div class="module-menu-layout">
      <!-- 默认模块 -->
      <div
        v-for="item in moduleData"
        :key="item.moduleId"
        class="module-menu-item"
        :class="item.moduleId === activeModule ? 'item-active' : ''"
        @click="handlerItem(item)"
      >
        {{ item.moduleDispname }}
      </div>
      <!-- 接口获取模块 -->
      <div
        v-for="item in tableData"
        :key="item.moduleId"
        class="module-menu-item"
        :class="item.moduleId === activeModule ? 'item-active' : ''"
        @click="handlerItem(item)"
      >
        <span class="module-menu-item-text">{{ item.moduleDispname }}</span>
        <span class="module-menu-item-text describe-text">标识符：{{ item.moduleName }}</span>
        <div class="module-menu-func hasFunc">
          <el-button
            icon="el-icon-edit"
            style="margin-right: 6px"
            type="text"
            @click="editItem(item)"
          />
          <el-button
            icon="el-icon-delete"
            style="color: #f56c6c"
            type="text"
            @click="removeItem(item)"
          />
        </div>
      </div>
      <el-button icon="el-icon-plus" type="text" @click="addItem">添加模块</el-button>
    </div>

    <CoustomFormDialog
      ref="ModuleMenuFormDialog"
      :dialog-json="dialogJson"
      :init-data="initFormData"
      :visible.sync="dialogVisible"
      @onSubmit="handlerSubmit"
      @onDialogClose="handlerDialogClose"
    />
  </div>
</template>
<script>
import CoustomFormDialog from '@/components/CoustomFormDialog';
import {
  putDevApiIotModule,
  postDevApiIotModule,
  getDevApiIotModuleList,
  getDevApiIotModuleModuleId,
  deleteDevApiIotModuleModuleId,
} from '@/api/iot/classes-module-controller.js';
import { DialogJson } from './constants';
import pageMixin from '@/mixin/page';
export default {
  name: 'ModuleMenu',
  components: {
    CoustomFormDialog,
  },
  mixins: [pageMixin],
  props: {
    className: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      dialogJson: DialogJson,
      dialogVisible: false,
      initFormData: {},
      searchModule: '',
      activeModule: -1,
      moduleData: [
        {
          moduleDispname: '默认模块',
          moduleId: -1,
        },
      ],
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.getList(
      {
        className: this.className,
        // TODO: 模块分页暂时取消
        pageSize: null,
      },
      getDevApiIotModuleList,
    );
  },
  methods: {
    handlerBulur() {
      this.handlerSearch({
        searchParams: {
          moduleDispname: this.searchModule,
          className: this.className,
          pageSize: null,
        },
      });
    },

    handlerItem(item) {
      this.activeModule = item.moduleId;
      this.$emit('onMenuChange', item);
    },
    handlerSubmit(formData) {
      this.handlerSave({
        formData,
        queryParams: { className: this.className },
        apis: {
          addApi: postDevApiIotModule,
          editApi: putDevApiIotModule,
        },
        editParams: ['moduleId'],
      });
    },
    editItem(item) {
      this.getInfoData({ id: item.moduleId, api: getDevApiIotModuleModuleId });
    },
    removeItem(item) {
      this.deleteTableItem({
        id: item.moduleId,
        api: deleteDevApiIotModuleModuleId,
      });
    },
    addItem() {
      this.dialogVisible = true;
    },
  },
};
</script>
<style scoped lang="scss">
.module-menu {
  width: 100%;

  &-layout {
    height: calc(100vh - 250px);
    overflow-y: auto;
  }

  &-search {
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    &-icon {
      width: 40px;
      height: 100%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &-add {
    margin: 0 12px;
    color: #333;
  }

  &-func {
    width: 60px;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    text-align: right;
    padding-right: 12px;
    opacity: 0;
    line-height: 60px;
  }

  &-item {
    width: 100%;
    height: 60px;
    font-size: 14px;
    padding: 0 12px;
    color: #333;
    cursor: pointer;
    position: relative;
    display: flex;
    align-content: flex-start;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;

    &:hover {
      .hasFunc {
        opacity: 1;
      }
    }

    &-text {
      display: inline-block;
      width: 70%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .describe-text {
      font-size: 12px;
      color: #888;
      opacity: 0.6;
    }
  }

  .item-active {
    color: #0070cc;
    border-right: 2px solid #0070cc;
    background: #f5f5f5;

    .describe-text {
      color: #0070cc;
    }
  }
}
</style>
