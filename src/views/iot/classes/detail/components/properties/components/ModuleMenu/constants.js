import { formItemJsonTemplate } from '@/constants/jsonTemplate.js';

const fields = {
  moduleDispname: {
    label: '模块名称',
    prop: 'moduleDispname',
  },
  moduleName: {
    label: '模块标识符',
    prop: 'moduleName',
  },
  remark: {
    label: '模块标描述',
    prop: 'remark',
  },
};

export const DialogJson = {
  type: 'Add',
  title: '模块',
  options: {
    width: '600px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary',
      },
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: '',
      },
    },
  ],
  formJson: {
    // 表单设置
    formOption: {
      inline: false,
      labelWidth: '130px',
      size: 'small',
      labelPosition: 'top',
    },
    // 默认表单值
    defaultFormData: {},
    // 表单项
    formItemJson: [
      formItemJsonTemplate({
        label: fields.moduleDispname.label,
        prop: fields.moduleDispname.prop,
        rules: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const reg = /^(?!_)(?!.*?_$)[a-zA-Z0-9_\u4e00-\u9fa5]+$/;
              if (!reg.test(value)) {
                callback(
                  new Error('请输入以中文、字母(不区分大小写)、数字、下划线(_)随意组合的字符串'),
                );
              }
              callback();
            },
            trigger: ['blur'],
          },
        ],
      }),
      formItemJsonTemplate({
        label: fields.moduleName.label,
        prop: fields.moduleName.prop,
        rules: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const reg = /^[a-zA-Z]{1}\w*$/;
              if (!reg.test(value)) {
                callback(new Error('请输入以字母(不区分大小写)、数字、下划线(_)随意组合的字符串'));
              }
              callback();
            },
            trigger: ['blur'],
          },
        ],
      }),
      formItemJsonTemplate({
        label: fields.remark.label,
        prop: fields.remark.prop,
        requiredRule: {
          required: false,
        },
        options: {
          type: 'textarea',
          maxlength: '4096',
          showWordLimit: true,
          rows: 4,
        },
      }),
    ],
  },
};
