import {
  getDevApiIotPropertiesList,
  postDevApiIotProperties,
  getDevApiIotPropertiesPropertyId,
  deleteDevApiIotPropertiesPropertyId,
  putDevApiIotProperties,
} from '@/api/iot/properties-controller';
import {
  getDevApiIotServiceList,
  postDevApiIotService,
  getDevApiIotServiceServiceId,
  deleteDevApiIotServiceServiceId,
  putDevApiIotService,
} from '@/api/iot/service-controller';
import {
  getDevApiIotEventList,
  postDevApiIotEvent,
  getDevApiIotEventEventId,
  deleteDevApiIotEventEventId,
  putDevApiIotEvent,
} from '@/api/iot/event-controller';
import { formItemJsonTemplate } from '@/constants/jsonTemplate.js';

import { InputDialogDialogJson } from '@/views/iot/classes/constants';

const fields = {
  propertyDispname: {
    label: '属性中文名称',
    prop: 'propertyDispname',
  },
  propertyName: {
    label: '属性英文名称',
    prop: 'propertyName',
  },
  propertyType: {
    label: '属性类型',
    prop: 'propertyType',
  },
  rwFlag: {
    label: '读写标识',
    prop: 'rwFlag',
  },
  createTime: {
    label: '创建时间',
    prop: 'createTime',
  },
  viewStyle: {
    label: '视图样式',
    prop: 'viewStyle',
  },
  range: {
    label: '取值范围',
    prop: 'range',
  },
  min: {
    label: '最小值',
    prop: 'min',
  },
  bool: {
    label: '布尔值',
    prop: 'bool',
  },
  enmu: {
    label: '枚举项',
    prop: 'enmu',
  },
  remark: {
    label: '描述',
    prop: 'remark',
  },
  step: {
    label: '步长',
    prop: 'step',
  },
  unit: {
    label: '单位',
    prop: 'unit',
  },
  length: {
    label: '数据长度',
    prop: 'length',
  },
  itemType: {
    label: '元素类型',
    prop: 'itemType',
  },
  size: {
    label: '元素个数',
    prop: 'size',
  },
  className: {
    label: '选择类',
    prop: 'className',
  },

  serviceDispname: {
    label: '服务名称',
    prop: 'serviceDispname',
  },
  serviceName: {
    label: '标识符',
    prop: 'serviceName',
  },
  callMethod: {
    label: '调用方式',
    prop: 'callMethod',
  },
  inoutTypeIn: {
    label: '输入参数',
    prop: 'inoutTypeIn',
  },
  inoutTypeOut: {
    label: '输出参数',
    prop: 'inoutTypeOut',
  },
  eventDispname: {
    label: '事件名称',
    prop: 'eventDispname',
  },
  eventName: {
    label: '标识符',
    prop: 'eventName',
  },
  eventType: {
    label: '事件类型',
    prop: 'eventType',
  },
};

export const defaultQuery = {
  formOption: {
    inline: true,
    labelWidth: 'auto',
    labelPosition: 'left',
    size: 'small',
  },
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary',
        },
      },
      {
        label: '重置',
        type: 'Reset',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini',
        },
      },
    ],
  },
};

export const attributeQueryFormJson = {
  formItemJson: [
    formItemJsonTemplate({
      label: fields.propertyDispname.label,
      prop: fields.propertyDispname.prop,
      isEnter: true,
      requiredRule: { required: false },
    }),
  ],
  ...defaultQuery,
};
export const serverQueryFormJson = {
  formItemJson: [
    formItemJsonTemplate({
      label: fields.serviceDispname.label,
      prop: fields.serviceDispname.prop,
      isEnter: true,
      requiredRule: { required: false },
    }),
  ],
  ...defaultQuery,
};

export const eventQueryFormJson = {
  formItemJson: [
    formItemJsonTemplate({
      label: fields.eventDispname.label,
      prop: fields.eventDispname.prop,
      isEnter: true,
      requiredRule: { required: false },
    }),
  ],
  ...defaultQuery,
};

export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: '',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};

export const attributeTableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号',
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.propertyDispname.label,
        prop: fields.propertyDispname.prop,
        showOverflowTooltip: true,
        isLink: false,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.propertyName.label,
        prop: fields.propertyName.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.propertyType.label,
        prop: fields.propertyType.prop,
        showOverflowTooltip: true,
        dictType: 'iot_property_type',
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.rwFlag.label,
        prop: fields.rwFlag.prop,
        showOverflowTooltip: true,
        dictType: 'iot_rw_flag',
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.createTime.label,
        prop: fields.createTime.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'center',
        headerAlign: 'center',
        label: '操作',
        type: 'func',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: '',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text',
            },
          },
          {
            label: '删除',
            type: 'Delete',
            permi: '',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text',
            },
          },
        ],
      },
    ],
  },
};
export const serverTableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号',
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.serviceDispname.label,
        prop: fields.serviceDispname.prop,
        showOverflowTooltip: true,
        isLink: false,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.serviceName.label,
        prop: fields.serviceName.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.callMethod.label,
        prop: fields.callMethod.prop,
        showOverflowTooltip: true,
        dictType: 'iot_call_method_type',
      },
      {
        align: 'center',
        headerAlign: 'center',
        label: '操作',
        type: 'func',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: '',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text',
            },
          },
          {
            label: '删除',
            type: 'Delete',
            permi: '',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text',
            },
          },
        ],
      },
    ],
  },
};
export const eventTableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号',
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.eventDispname.label,
        prop: fields.eventDispname.prop,
        showOverflowTooltip: true,
        isLink: false,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.eventName.label,
        prop: fields.eventName.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.eventType.label,
        prop: fields.eventType.prop,
        showOverflowTooltip: true,
        // dictType: 'iot_property_type',
      },
      {
        align: 'center',
        headerAlign: 'center',
        label: '操作',
        type: 'func',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: '',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text',
            },
          },
          {
            label: '删除',
            type: 'Delete',
            permi: '',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text',
            },
          },
        ],
      },
    ],
  },
};

const formConfig = {
  // 表单设置
  formOption: {
    inline: false,
    labelWidth: '130px',
    size: 'small',
    labelPosition: 'top',
  },
};

const attributeformJson = {
  ...formConfig,
  // 默认表单值
  defaultFormData: {
    rwFlag: 'READ_WRITE',
    itemType: 'INT',
  },
  // 表单项
  formItemJson: [
    formItemJsonTemplate({
      label: fields.propertyDispname.label,
      prop: fields.propertyDispname.prop,
    }),
    formItemJsonTemplate({
      label: fields.propertyName.label,
      prop: fields.propertyName.prop,
      rules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            const reg = /^[a-zA-Z]{1}\w*$/;
            if (!reg.test(value)) {
              callback(new Error('请输入以字母(不区分大小写)、数字、下划线(_)随意组合的字符串'));
            }
            callback();
          },
          trigger: ['blur'],
        },
      ],
    }),
    formItemJsonTemplate({
      label: fields.propertyType.label,
      prop: fields.propertyType.prop,
      type: 'Select',
      dictType: 'iot_property_type',
      association: [
        {
          whiteValue: ['INT', 'FLOAT', 'DOUBLE'], // 当前字段满足此值时，显示关联字段配置
          jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
          childItems: [
            formItemJsonTemplate({
              label: fields.range.label,
              type: 'InputRange',
              required: true,
              itemOptions: [
                {
                  prop: 'min',
                  style: 'width: 198px',
                  rules: [
                    {
                      required: true,
                      validator: (rule, value, callback) => {
                        if (!value) {
                          callback(new Error('请输入最小值'));
                        }
                        callback();
                      },
                      trigger: ['blur'],
                    },
                  ],
                  options: {
                    type: 'number',
                    clearable: false,
                    placeholder: '请输入最小值',
                  },
                },
                {
                  prop: 'max',
                  style: 'width: 198px',
                  rules: [
                    {
                      required: true,
                      validator: (rule, value, callback) => {
                        if (!value) {
                          callback(new Error('请输入最大值'));
                        }
                        callback();
                      },
                      trigger: ['blur'],
                    },
                  ],
                  options: {
                    type: 'number',
                    clearable: false,
                    placeholder: '请输入最大值',
                  },
                },
              ],
            }),

            formItemJsonTemplate({
              label: fields.step.label,
              prop: fields.step.prop,
              options: {
                type: 'number',
              },
            }),
            formItemJsonTemplate({
              label: fields.unit.label,
              prop: fields.unit.prop,
              type: 'Select',
              associationProp: 'unitName', // 关联字段，如果需要将名称也保存的话
              options: {
                filterable: true,
              },
              dictType: 'iot_property_unit',
            }),
          ],
        },
        {
          whiteValue: ['ENUM'], // 当前字段满足此值时，显示关联字段配置
          jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
          childItems: [
            formItemJsonTemplate({
              label: fields.enmu.label,
              associationProp: fields.enmu.prop,
              type: 'InputEnum',
              required: true,
            }),
          ],
        },
        {
          whiteValue: ['BOOL'], // 当前字段满足此值时，显示关联字段配置
          jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
          childItems: [
            formItemJsonTemplate({
              label: fields.bool.label,
              associationProp: fields.bool.prop,
              type: 'InputBoole',
              required: true,
              itemOptions: [
                {
                  prop: '0',
                },
                {
                  prop: '1',
                },
              ],
            }),
          ],
        },
        {
          whiteValue: ['TEXT'], // 当前字段满足此值时，显示关联字段配置
          jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
          childItems: [
            formItemJsonTemplate({
              label: fields.length.label,
              prop: fields.length.prop,
              options: {
                type: 'number',
                max: 10240,
                min: 1,
                slotAppendName: '字节',
              },
              rules: [
                {
                  required: true,
                  validator: (rule, value, callback) => {
                    if (!Number.isInteger(Number(value))) {
                      callback(new Error('请输入整型'));
                    }
                    if (Number(value) > 10240) {
                      callback(new Error('请输入1-10240内的数字'));
                    }
                    callback();
                  },
                  trigger: ['blur'],
                },
              ],
            }),
          ],
        },
        {
          whiteValue: ['STRUCT'], // 当前字段满足此值时，显示关联字段配置
          jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
          childItems: [
            formItemJsonTemplate({
              label: fields.className.label,
              prop: fields.className.prop,
              type: 'InputDialog',
              dialogJson: InputDialogDialogJson,
              associationProp: 'packageName',
            }),
          ],
        },
        {
          whiteValue: ['ARRAY'], // 当前字段满足此值时，显示关联字段配置
          jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
          childItems: [
            formItemJsonTemplate({
              label: fields.itemType.label,
              prop: fields.itemType.prop,
              type: 'Radio',
              options: {
                type: 'number',
              },
              option: [
                {
                  value: 'INT',
                  label: 'int32',
                },
                {
                  value: 'FLOAT',
                  label: 'float',
                },
                {
                  value: 'DOUBLE',
                  label: 'double',
                },
                {
                  value: 'TEXT',
                  label: 'text',
                },
                {
                  value: 'STRUCT',
                  label: 'struct',
                },
              ],
            }),
            formItemJsonTemplate({
              label: fields.size.label,
              prop: fields.size.prop,
              options: {
                type: 'number',
              },
              rules: [
                {
                  required: true,
                  validator: (rule, value, callback) => {
                    if (!Number.isInteger(Number(value))) {
                      callback(new Error('请输入整数'));
                    }
                    if (Number(value) > 512) {
                      callback(new Error('请输入1-512内的数字'));
                    }
                    callback();
                  },
                  trigger: ['blur'],
                },
              ],
            }),
            formItemJsonTemplate({
              label: fields.className.label,
              prop: fields.className.prop,
              type: 'InputDialog',
              associationProp: 'packageName',
              showConfig: {
                field: 'itemType',
                value: 'STRUCT',
              },
              dialogJson: InputDialogDialogJson,
            }),
          ],
        },
      ],
    }),
    formItemJsonTemplate({
      label: fields.rwFlag.label,
      prop: fields.rwFlag.prop,
      type: 'Select',
      dictType: 'iot_rw_flag',
    }),
    formItemJsonTemplate({
      label: fields.viewStyle.label,
      prop: fields.viewStyle.prop,
      requiredRule: {
        required: false,
      },
    }),
    formItemJsonTemplate({
      label: fields.remark.label,
      prop: fields.remark.prop,
      requiredRule: {
        required: false,
      },
      options: {
        type: 'textarea',
        maxlength: '100',
        showWordLimit: true,
        rows: 4,
      },
    }),
  ],
};

export const serverformJson = {
  ...formConfig,
  // 默认表单值
  defaultFormData: {
    callMethod: 1,
  },
  // 表单项
  formItemJson: [
    formItemJsonTemplate({
      label: fields.serviceDispname.label,
      prop: fields.serviceDispname.prop,
    }),
    formItemJsonTemplate({
      label: fields.serviceName.label,
      prop: fields.serviceName.prop,
      rules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            const reg = /^[a-zA-Z]{1}\w*$/;
            if (!reg.test(value)) {
              callback(new Error('请输入以字母(不区分大小写)、数字、下划线(_)随意组合的字符串'));
            }
            callback();
          },
          trigger: ['blur'],
        },
      ],
    }),
    formItemJsonTemplate({
      label: fields.callMethod.label,
      prop: fields.callMethod.prop,
      type: 'Radio',
      dictType: 'iot_call_method_type',
    }),
    formItemJsonTemplate({
      label: fields.inoutTypeIn.label,
      prop: fields.inoutTypeIn.prop,
      // 回显配置， 用于非 prop 回显时场景
      echoConfig: {
        field: 'properties',
        basis: {
          field: 'inoutType',
          value: 'IN',
        },
      },
      type: 'IotParame',
    }),
    formItemJsonTemplate({
      label: fields.inoutTypeOut.label,
      prop: fields.inoutTypeOut.prop,
      echoConfig: {
        field: 'properties',
        basis: {
          field: 'inoutType',
          value: 'OUT',
        },
      },
      type: 'IotParame',
    }),
    formItemJsonTemplate({
      label: fields.remark.label,
      prop: fields.remark.prop,
      requiredRule: {
        required: false,
      },
      options: {
        type: 'textarea',
        maxlength: '100',
        showWordLimit: true,
        rows: 4,
      },
    }),
  ],
};

export const eventformJson = {
  ...formConfig,
  // 默认表单值
  defaultFormData: {
    eventType: 1,
  },
  // 表单项
  formItemJson: [
    formItemJsonTemplate({
      label: fields.eventDispname.label,
      prop: fields.eventDispname.prop,
    }),
    formItemJsonTemplate({
      label: fields.eventName.label,
      prop: fields.eventName.prop,
      rules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            const reg = /^[a-zA-Z]{1}\w*$/;
            if (!reg.test(value)) {
              callback(new Error('请输入以字母(不区分大小写)、数字、下划线(_)随意组合的字符串'));
            }
            callback();
          },
          trigger: ['blur'],
        },
      ],
    }),
    formItemJsonTemplate({
      label: fields.eventType.label,
      prop: fields.eventType.prop,
      type: 'Radio',
      options: {
        type: 'number',
      },
      option: [
        {
          value: 1,
          label: '信息',
        },
        {
          value: 2,
          label: '告警',
        },
        {
          value: 3,
          label: '故障',
        },
      ],
    }),
    formItemJsonTemplate({
      label: fields.inoutTypeIn.label,
      prop: fields.inoutTypeIn.prop,
      // 回显配置， 用于非 prop 回显时场景
      echoConfig: {
        field: 'properties',
        basis: {
          field: 'inoutType',
          value: 'IN',
        },
      },
      type: 'IotParame',
    }),
    formItemJsonTemplate({
      label: fields.remark.label,
      prop: fields.remark.prop,
      requiredRule: {
        required: false,
      },
      options: {
        type: 'textarea',
        maxlength: '100',
        showWordLimit: true,
        rows: 4,
      },
    }),
  ],
};

export const InoutType = {
  IN: 'IN',
  OUT: 'OUT',
};

export const HeadMap = {
  ATTRIBUTE: 'attribute',
  SERVER: 'server',
  EVENT: 'event',
};

export const HeadTabs = [
  {
    value: HeadMap.ATTRIBUTE,
    label: '属性',
  },
  {
    value: HeadMap.SERVER,
    label: '服务',
  },
  {
    value: HeadMap.EVENT,
    label: '事件',
  },
];

const defaultDialogJson = {
  type: 'Add',
  options: {
    width: '600px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary',
      },
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: '',
      },
    },
  ],

  // 表格 head 类型选择，可切换表单
  // defaultHeadOptions: HeadMap.ATTRIBUTE,
  // headOptions: [
  //   {
  //     value: HeadMap.ATTRIBUTE,
  //     label: '属性',
  //   },
  //   {
  //     value: HeadMap.SERVER,
  //     label: '服务',
  //   },
  //   {
  //     value: HeadMap.EVENT,
  //     label: '事件',
  //   },
  // ],
};

export const attributeDialogJson = {
  ...defaultDialogJson,
  title: '属性',
  formJson: attributeformJson,
};

export const serverDialogJson = {
  ...defaultDialogJson,
  title: '服务',
  formJson: serverformJson,
};

export const eventDialogJson = {
  ...defaultDialogJson,
  title: '事件',
  formJson: eventformJson,
};

export const QueryFormJson = {
  [HeadMap.ATTRIBUTE]: attributeQueryFormJson,
  [HeadMap.SERVER]: serverQueryFormJson,
  [HeadMap.EVENT]: eventQueryFormJson,
};

export const TableJson = {
  [HeadMap.ATTRIBUTE]: attributeTableJson,
  [HeadMap.SERVER]: serverTableJson,
  [HeadMap.EVENT]: eventTableJson,
};

export const DialogJson = {
  [HeadMap.ATTRIBUTE]: attributeDialogJson,
  [HeadMap.SERVER]: serverDialogJson,
  [HeadMap.EVENT]: eventDialogJson,
};

export const ServerApis = {
  [HeadMap.ATTRIBUTE]: {
    listApi: getDevApiIotPropertiesList,
    addApi: postDevApiIotProperties,
    editApi: putDevApiIotProperties,
    infoApi: getDevApiIotPropertiesPropertyId,
    delApi: deleteDevApiIotPropertiesPropertyId,
  },
  [HeadMap.SERVER]: {
    listApi: getDevApiIotServiceList,
    addApi: postDevApiIotService,
    editApi: putDevApiIotService,
    infoApi: getDevApiIotServiceServiceId,
    delApi: deleteDevApiIotServiceServiceId,
  },
  [HeadMap.EVENT]: {
    listApi: getDevApiIotEventList,
    addApi: postDevApiIotEvent,
    editApi: putDevApiIotEvent,
    infoApi: getDevApiIotEventEventId,
    delApi: deleteDevApiIotEventEventId,
  },
};

export const IdKeys = {
  [HeadMap.ATTRIBUTE]: 'propertyId',
  [HeadMap.SERVER]: ['serviceId'],
  [HeadMap.EVENT]: ['eventId'],
};
