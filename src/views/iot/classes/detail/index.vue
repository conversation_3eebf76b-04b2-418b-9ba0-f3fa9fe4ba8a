<template>
  <div class="app-container">
    <div class="classtitle">{{ classesInfo.classDispname }}</div>
    <el-tabs v-model="activeTabName" type="card" @tab-click="handleTabsClick">
      <el-tab-pane v-for="(item, index) in tabOptions" :key="index" v-bind="item">
        <Properties :classes-info="classesInfo" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import Properties from './components/properties';
export default {
  name: 'ClassesDetail',
  components: {
    Properties,
  },
  data() {
    return {
      activeTabName: 'properties',
      tabOptions: [
        {
          label: '功能定义',
          name: 'properties',
        },
      ],
      classesInfo: {},
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    handleTabsClick() {},
  },
};
</script>
<style scoped lang="scss">
.classtitle {
  font-size: 28px;
  font-weight: 800;
  margin-bottom: 14px;
}
</style>
