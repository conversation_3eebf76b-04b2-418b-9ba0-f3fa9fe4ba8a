<template>
  <div class="app-container">
    <CoustomTable
      :page-buttons-json="pageButtonsJson"
      :query-form-json="queryFormJson"
      :table-data="tableData"
      :table-json="tableJson"
      :total="total"
      :paginations="defaultQueryParams"
      @onButton="handlerButton"
      @onPageButton="handlerPageButton"
      @onSearch="handlerSearch"
      @onTableRowClick="handlerTableRowClick"
      @onPagination="handlerPagination"
    />

    <CoustomFormDialog
      ref="formDialog"
      :dialog-json="dialogJson"
      :init-data="initFormData"
      :visible.sync="dialogVisible"
      @onSubmit="handlerSubmit"
      @onDialogClose="handlerDialogClose"
    />
  </div>
</template>
<script>
import CoustomTable from '@/components/CoustomTable';
import CoustomFormDialog from '@/components/CoustomFormDialog';
import { TableJ<PERSON>, Query<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>utt<PERSON><PERSON><PERSON>, Dialog<PERSON><PERSON> } from './constants';
import {
  getDevApiIotClassesList,
  postDevApiIotClasses,
  getDevApiIotClassesClassId,
  deleteDevApiIotClassesClassId,
  putDevApiIotClasses,
} from '@/api/iot/classes-controller.js';
import pageMixin from '@/mixin/page';
export default {
  name: 'Classes',
  components: {
    CoustomTable,
    CoustomFormDialog,
  },
  mixins: [pageMixin],
  dicts: ['iot_class_type', 'iot_access_type'],
  provide() {
    return {
      dictData: this.dict,
    };
  },
  data() {
    return {
      queryFormJson: QueryFormJson,
      pageButtonsJson: PageButtonsJson,
      tableJson: TableJson,
      dialogJson: DialogJson,
      associationFields: ['packageName'],
    };
  },
  computed: {
    packageName() {
      const { query } = this.$route;
      return query.packageName;
    },
  },
  created() {},
  mounted() {
    this.getList({ packageName: this.packageName }, getDevApiIotClassesList);
  },

  methods: {
    async handlerSubmit(formData) {
      this.handlerSave({
        formData,
        queryParams: { packageName: this.packageName },
        apis: {
          addApi: postDevApiIotClasses,
          editApi: putDevApiIotClasses,
        },
        editParams: ['classId'],
      });
    },

    handlerTableRowClick(item) {
      this.goToPage({
        path: '/carbon-iot/packages/classes/detail',
        query: {
          className: item.className,
        },
      });
    },

    // 新增按钮事件
    handlerPageButton(item) {
      this.pageButton({ item });
    },

    async getInfo(id) {
      this.getInfoData({ id, api: getDevApiIotClassesClassId });
    },

    deleteItem(id) {
      this.deleteTableItem({ id, api: deleteDevApiIotClassesClassId });
    },

    handlerButton(btnItem) {
      this.tableButton({ btnItem, idKey: 'classId' });
    },
  },
};
</script>
<style scoped lang="scss"></style>
