<template>
  <div class="app-container">
    <CoustomTable
      :page-buttons-json="pageButtonsJson"
      :query-form-json="queryFormJson"
      :table-data="tableData"
      :table-json="tableJson"
      :total="total"
      :paginations="defaultQueryParams"
      @onButton="handlerButton"
      @onPageButton="handlerPageButton"
      @onSearch="handlerSearch"
      @onTableRowClick="handlerTableRowClick"
      @onPagination="handlerPagination"
    >
      <div slot="content" style="width: 100%">
        <el-row :gutter="20">
          <el-col v-for="(item, index) in tableData" :key="index" :span="8">
            <el-card class="card-layout">
              <div class="card-title" @click="goClasses(item)">
                <span class="el-icon-menu card-title-icon" />
                <span>{{ item.packageDispname }}</span>
              </div>

              <div style="cursor: pointer" @click="goClasses(item)">
                <template v-for="(row, rowIndex) in tableJson.columnJson.data">
                  <div v-if="!row.hiddenItem" :key="rowIndex" class="card-item">
                    <span class="card-item-label">{{ row.label }}</span>
                    <span>{{ item[row.prop] }}</span>
                  </div>
                </template>
              </div>

              <div class="card-button">
                <el-button
                  v-for="(btnItem, btnIndex) in tableJson.buttonList"
                  :key="btnIndex"
                  class="button"
                  v-bind="btnItem.options"
                  @click="handlerButton({ row: item, item: btnItem })"
                >{{ btnItem.label }}</el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </CoustomTable>

    <CoustomFormDialog
      ref="formDialog"
      :dialog-json="dialogJson"
      :init-data="initFormData"
      :visible.sync="dialogVisible"
      @onSubmit="handlerSubmit"
      @onDialogClose="handlerDialogClose"
    />
  </div>
</template>
<script>
import CoustomTable from '@/components/CoustomTable';
import CoustomFormDialog from '@/components/CoustomFormDialog';
import { TableJson, QueryFormJson, PageButtonsJson, DialogJson } from './constants';
import {
  getDevApiIotPackagesList,
  postDevApiIotPackages,
  getDevApiIotPackagesPackageId,
  deleteDevApiIotPackagesPackageId,
  putDevApiIotPackages,
} from '@/api/iot/packages-controller.js';
import pageMixin from '@/mixin/page';
export default {
  name: 'Package',
  components: {
    CoustomTable,
    CoustomFormDialog,
  },
  mixins: [pageMixin],
  data() {
    return {
      queryFormJson: QueryFormJson,
      pageButtonsJson: PageButtonsJson,
      tableJson: TableJson,
      dialogJson: DialogJson,
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.getList({}, getDevApiIotPackagesList);
  },
  methods: {
    goClasses(item) {
      this.goToPage({
        path: '/carbon-iot/packages/classes',
        query: {
          packageName: item.packageName,
        },
      });
    },

    async handlerSubmit(formData) {
      this.handlerSave({
        formData,
        queryParams: {},
        apis: {
          addApi: postDevApiIotPackages,
          editApi: putDevApiIotPackages,
        },
        editParams: ['packageId'],
      });
    },

    handlerTableRowClick(item) {},

    // 新增按钮事件
    handlerPageButton(item) {
      this.pageButton({ item });
    },

    async getInfo(id) {
      this.getInfoData({ id, api: getDevApiIotPackagesPackageId });
    },

    deleteItem(id) {
      this.deleteTableItem({ id, api: deleteDevApiIotPackagesPackageId });
    },

    handlerButton(btnItem) {
      this.tableButton({ btnItem, idKey: 'packageId' });
    },
  },
};
</script>
<style scoped lang="scss">
.card-layout {
  margin-bottom: 24px;
  cursor: pointer;
  .card-title {
    display: flex;
    align-items: center;
    color: #333;
    font-size: 16px;
    font-weight: 800;
    margin: 0px 0 12px;

    &-icon {
      margin-right: 6px;
      font-size: 24px;
    }
  }
  .card-item {
    font-size: 14px;
    color: #555;
    height: 28px;
    line-height: 28px;
    &-label {
      width: 100px;
      display: inline-block;
    }
  }

  .card-button {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 14px;

    ::v-deep {
      .el-button--text {
        font-size: 14px;
      }
    }
  }
}
</style>
