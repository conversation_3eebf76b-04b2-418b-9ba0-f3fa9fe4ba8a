export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: 'auto',
    labelPosition: 'left',
    size: 'small',
  },
  formItemJson: [
    {
      label: '包名称',
      prop: 'packageDispname',
      type: 'Input',
      isEnter: true,
      options: {
        clearable: true,
        placeholder: '请输入包名称',
      },
    },
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary',
        },
      },
      {
        label: '重置',
        type: 'Reset',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini',
        },
      },
    ],
  },
};

export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: '',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};

export const TableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    data: [
      {
        align: 'left',
        headerAlign: 'left',
        label: '包名称',
        prop: 'packageDispname',
        showOverflowTooltip: true,
        isLink: true,
        hiddenItem: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: '包英文名',
        prop: 'packageName',
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: '描述',
        prop: 'packageDispname',
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: '创建时间',
        prop: 'createTime',
        showOverflowTooltip: true,
      },
      // {
      //   align: "center",
      //   headerAlign: "center",
      //   label: "操作",
      //   type: "func",
      //   hiddenItem: true,
      //   buttonList: [
      //     {
      //       label: "编辑",
      //       type: 'Edit',
      //       permi: '',
      //       options: {
      //         icon: "el-icon-edit",
      //         size: "mini",
      //         type: "text",
      //       }
      //     },
      //     {
      //       label: "删除",
      //       type: 'Delete',
      //       permi: '',
      //       options: {
      //         icon: "el-icon-delete",
      //         size: "mini",
      //         type: "text",
      //       }
      //     },
      //   ],
      // },
    ],
  },
  buttonList: [
    {
      label: '编辑',
      type: 'Edit',
      permi: '',
      options: {
        icon: 'el-icon-edit',
        size: 'mini',
        type: 'text',
      },
    },
    {
      label: '删除',
      type: 'Delete',
      permi: '',
      options: {
        icon: 'el-icon-delete',
        size: 'mini',
        type: 'text',
      },
    },
  ],
};

export const DialogJson = {
  type: 'Add',
  title: '包',
  options: {
    width: '600px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary',
      },
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: '',
      },
    },
  ],
  formJson: {
    // 表单设置
    formOption: {
      inline: false,
      labelWidth: '100px',
      size: 'small',
      labelPosition: 'top',
    },
    // 表单项
    formItemJson: [
      {
        label: '包中文名称',
        prop: 'packageDispname',
        type: 'Input',
        rules: [{ required: true, message: '请输入包中文名称', trigger: 'blur' }],
        options: {
          clearable: true,
          placeholder: '请输入包中文名称',
        },
      },
      {
        label: '包英文名称',
        prop: 'packageName',
        type: 'Input',
        rules: [
          { required: true, message: '请输入包英文名称', trigger: 'blur' },
          {
            required: true,
            validator: (rule, value, callback) => {
              const reg = /^[a-zA-Z]{1}\w*$/;
              if (!reg.test(value)) {
                callback(new Error('请输入以字母(不区分大小写)、数字、下划线(_)随意组合的字符串'));
              }
              callback();
            },
            trigger: ['blur'],
          },
        ],
        options: {
          clearable: true,
          placeholder: '请输入包英文名称',
        },
      },
      {
        label: '视图样式',
        prop: 'viewStyle',
        type: 'Input',
        rules: [{ required: false, message: '请输入视图样式', trigger: 'blur' }],
        options: {
          clearable: true,
          placeholder: '请输入视图样式',
        },
      },
      {
        label: '描述',
        prop: 'descriptions',
        type: 'Input',
        rules: [],
        options: {
          clearable: true,
          placeholder: '请输入描述',
          type: 'textarea',
          maxlength: '100',
          showWordLimit: true,
          rows: 4,
        },
      },
    ],
  },
};
