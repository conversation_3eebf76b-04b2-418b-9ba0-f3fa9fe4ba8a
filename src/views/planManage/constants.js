import { getBpmPlanTemplateList } from '@/api/v2/plan/plan-template-controller';

export const fields = {
  planId: {
    label: '计划Id',
    prop: 'fieldId'
  },
  planName: {
    label: '计划名称',
    prop: 'planName'
  },
  planType: {
    label: '计划类型',
    prop: 'planType'
  },
  planDesc: {
    label: '计划描述',
    prop: 'planDesc'
  },
  planStatus: {
    label: '计划状态',
    prop: 'planStatus'
  },
  createTime: {
    label: '创建时间',
    prop: 'createTime'
  },
  templateId: {
    label: '计划模版名称',
    prop: 'templateId'
  },
  templateName: {
    label: '计划模版名称',
    prop: 'templateName'
  }
};

export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: '120px',
    labelPosition: 'right',
    size: 'small',
    formFlex: {
      gutter: 20,
      span: 8
    }
  },
  formItemJson: [
    {
      label: '计划信息',
      showTitle: false,
      children: [
        {
          label: fields.planName.label,
          prop: fields.planName.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.planName.label
          }
        }
      ]
    }
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        permi: 'planTemplateField:manage:query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary'
        }
      },
      {
        label: '重置',
        type: 'Reset',
        permi: 'planTemplateField:manage:query',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini'
        }
      }
    ]
  }
};


/**
 * 由Java类 PlanTemplateField 自动生成的页面按钮配置
 */
export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: 'planTemplateField:manage:add',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};


/**
 * 由Java类 PlanTemplateField 自动生成的表格配置
 */
export const TableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    selectIdKey: 'fieldId',
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号'
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.planName.label,
        prop: fields.planName.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.createTime.label,
        prop: fields.createTime.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        width: '200',
        label: '操作',
        type: 'func',
        fixed: 'right',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: 'plan:manage:edit',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text'
            }
          },
          {
            label: '编辑计划',
            type: 'EditPlan',
            permi: 'plan:manage:edit',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text'
            }
          },
          {
            label: '删除',
            type: 'Delete',
            permi: 'plan:manage:remove',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text'
            }
          }
        ]
      }
    ]
  }
};


/**
 * 由Java类 PlanTemplateField 自动生成的对话框配置
 */
export const DialogJson = {
  type: 'Add',
  title: '计划信息',
  options: {
    width: '580px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary'
      }
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: ''
      }
    }
  ],
  formJson: {
    formOption: {
      labelPosition: 'right',
      inline: false,
      labelWidth: '160px',
      size: 'small',
      formFlex: {
        gutter: 20,
        span: 24
      }
    },
    defaultFormData: {
    },
    formItemJson: [
      {
        label: '基本信息',
        showTitle: false,
        children: [
          {
            label: fields.planName.label,
            prop: fields.planName.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.planName.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.planName.label
            }
          },
          {
            label: fields.templateId.label,
            prop: fields.templateId.prop,
            type: 'Select',
            rules: [
              {
                required: true,
                message: '请选择' + fields.templateId.label,
                trigger: 'blur'
              }
            ],
            apiConfig: {
              api: getBpmPlanTemplateList,
              params: {
                value: 'templateId',      // 值字段名
                label: 'templateName',    // 标签字段名
                queryParams: {
                  // 只查询已发布的模版
                  publishStatus: 1
                }   // API查询参数
              }
            },
            options: {
              clearable: true,
              placeholder: '请选择' + fields.templateId.label
            }
          },
        ]
      }
    ]
  }
};
