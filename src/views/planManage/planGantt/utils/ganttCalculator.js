/**
 * 甘特图数据计算工具
 */

// 依赖类型
export const DependencyType = {
  FS: 'FinishToStart', // 完成到开始
  SS: 'StartToStart',  // 开始到开始
  FF: 'FinishToFinish', // 完成到完成
  SF: 'StartToFinish'  // 开始到完成
};

/**
 * 计算两个日期之间的工作日天数
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 * @returns {number} 工作日天数
 */
export function calculateWorkDays(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  let days = 0;
  const current = new Date(start);

  while (current <= end) {
    // 排除周末
    if (current.getDay() !== 0 && current.getDay() !== 6) {
      days++;
    }
    current.setDate(current.getDate() + 1);
  }
  return days;
}

/**
 * 根据依赖类型和偏移量计算任务时间
 * @param {Object} task 当前任务
 * @param {Object} dependency 依赖任务
 * @param {string} dependencyType 依赖类型
 * @param {number} offset 偏移量(工作日)
 * @returns {Object} 计算后的任务时间
 */
export function calculateTaskTime(task, dependency, dependencyType, offset = 0) {
  const result = { ...task };
  const offsetDays = offset;

  switch (dependencyType) {
    case DependencyType.FS: // 完成到开始
      // 当前任务开始时间 = 依赖任务结束时间 + 偏移量
      result.start_date = addWorkDays(dependency.end_date, offsetDays);
      result.end_date = addWorkDays(result.start_date, task.duration - 1);
      break;

    case DependencyType.SS: // 开始到开始
      // 当前任务开始时间 = 依赖任务开始时间 + 偏移量
      result.start_date = addWorkDays(dependency.start_date, offsetDays);
      result.end_date = addWorkDays(result.start_date, task.duration - 1);
      break;

    case DependencyType.FF: // 完成到完成
      // 当前任务结束时间 = 依赖任务结束时间 + 偏移量
      result.end_date = addWorkDays(dependency.end_date, offsetDays);
      result.start_date = addWorkDays(result.end_date, -(task.duration - 1));
      break;

    case DependencyType.SF: // 开始到完成
      // 当前任务结束时间 = 依赖任务开始时间 + 偏移量
      result.end_date = addWorkDays(dependency.start_date, offsetDays);
      result.start_date = addWorkDays(result.end_date, -(task.duration - 1));
      break;
  }

  return result;
}

/**
 * 添加工作日
 * @param {string} date 日期
 * @param {number} days 天数
 * @returns {string} 计算后的日期
 */
function addWorkDays(date, days) {
  const result = new Date(date);
  let remainingDays = days;

  while (remainingDays !== 0) {
    result.setDate(result.getDate() + (remainingDays > 0 ? 1 : -1));

    // 如果是周末,跳过
    if (result.getDay() === 0 || result.getDay() === 6) {
      continue;
    }

    remainingDays += (remainingDays > 0 ? -1 : 1);
  }

  return formatDate(result);
}

/**
 * 格式化日期为 YYYY-MM-DD
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 重新计算甘特图数据
 * @param {Array} tasks 任务列表
 * @param {Array} dependencies 依赖关系列表
 * @returns {Array} 计算后的任务列表
 */
export function recalculateGanttData(tasks, dependencies) {
  // 创建任务映射
  const taskMap = new Map(tasks.map(task => [task.id, { ...task }]));

  // 创建依赖映射
  const dependencyMap = new Map();
  dependencies.forEach(dep => {
    if (!dependencyMap.has(dep.linkedToTaskKey)) {
      dependencyMap.set(dep.linkedToTaskKey, []);
    }
    dependencyMap.get(dep.linkedToTaskKey).push(dep);
  });

  // 递归计算任务时间
  function calculateTaskTimes(taskId, visited = new Set()) {
    if (visited.has(taskId)) {
      return; // 防止循环依赖
    }
    visited.add(taskId);

    const task = taskMap.get(taskId);
    const deps = dependencyMap.get(taskId) || [];

    // 先计算所有依赖任务
    deps.forEach(dep => {
      calculateTaskTimes(dep.linkedFromTaskKey, visited);
    });

    // 计算当前任务时间
    deps.forEach(dep => {
      const dependencyTask = taskMap.get(dep.linkedFromTaskKey);
      const updatedTask = calculateTaskTime(task, dependencyTask, dep.type, dep.offset || 0);
      Object.assign(task, updatedTask);
    });
  }

  // 计算所有任务
  tasks.forEach(task => {
    calculateTaskTimes(task.id);
  });

  return Array.from(taskMap.values());
}
