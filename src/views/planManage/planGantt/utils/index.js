import { FieldTypeMap, DefaultFormConfig, GanttEditFormOptions } from '../constants';


/**
 * 根据节点路径删除树中的节点
 * @param {Array} tree - 树结构数据
 * @param {Array} path - 节点路径数组,如 [0,0,0]
 * @returns {Boolean} - 是否删除成功
 */
export function deleteTreeNodeByPath(tree, path) {
  // 如果路径为空或树为空,直接返回false
  if (!path?.length || !tree?.length) {
    return false;
  }

  // 递归函数
  function deleteNode(nodes, currentPath) {
    // 获取当前层级的索引
    const currentIndex = currentPath[0];

    // 如果索引超出范围,返回false
    if (currentIndex < 0 || currentIndex >= nodes.length) {
      return false;
    }

    // 如果是最后一层,直接删除节点
    if (currentPath.length === 1) {
      nodes.splice(currentIndex, 1);
      return true;
    }

    // 获取当前节点
    const currentNode = nodes[currentIndex];

    // 如果当前节点没有子节点,返回false
    if (!currentNode.children?.length) {
      return false;
    }

    // 递归处理下一层
    return deleteNode(currentNode.children, currentPath.slice(1));
  }

  // 开始递归删除
  return deleteNode(tree, path);
}

// 使用示例:
// const tree = [
//   {
//     id: 1,
//     children: [
//       {
//         id: 2,
//         children: [
//           { id: 3 }
//         ]
//       }
//     ]
//   }
// ];

// console.log('tree1', JSON.stringify(tree));
// const path = [0, 0, 0]; // 要删除id为3的节点
// deleteTreeNodeByPath(tree, path);

// console.log('tree', JSON.stringify(tree));


/**
 * 将树结构中的所有节点id提取出来,返回一维数组
 * @param {Array} tree - 树结构数据
 * @param {String} [childrenKey='children'] - 子节点的key名
 * @returns {Array} - 包含所有节点id的一维数组
 * @example
 * const tree = [
 *   { id: 1, children: [{ id: 2 }, { id: 3, children: [{ id: 4 }] }] },
 *   { id: 5 }
 * ];
 * flattenTreeIds(tree); // 返回 [1, 2, 3, 4, 5]
 */
export function flattenTreeIds(tree, childrenKey = 'children') {
  // 如果输入为空,返回空数组
  if (!tree?.length) {
    return [];
  }

  // 递归处理函数
  function flatten(nodes) {
    return nodes.reduce((acc, node) => {
      // 如果节点无效,跳过
      if (!node) {
        return acc;
      }

      // 收集当前节点的id
      const ids = [node.id];

      // 如果有子节点,递归处理
      const children = node[childrenKey];
      if (children?.length) {
        ids.push(...flatten(children));
      }

      return acc.concat(ids);
    }, []);
  }

  return flatten(tree);
}

// 使用示例:
// const tree = [
//   {
//     id: 1,
//     children: [
//       { id: 2 },
//       {
//         id: 3,
//         children: [
//           { id: 4 }
//         ]
//       }
//     ]
//   },
//   { id: 5 }
// ];
// console.log(flattenTreeIds(tree)); // [1, 2, 3, 4, 5]

/**
 * 以targetTree为基准合并两个树结构,仅更新指定字段
 * @param {Array} sourceTree - 原始树结构数据
 * @param {Array} targetTree - 目标树结构数据
 * @param {Array} fields - 需要更新的字段列表
 * @param {String} [childrenKey='children'] - 子节点的key名
 * @param {String} [idKey='id'] - 用于匹配节点的唯一标识字段
 * @returns {Array} - 更新后的新树结构
 * @example
 * const sourceTree = [
 *   { id: 1, name: 'old', value: 100, children: [{ id: 2, name: 'old2' }, { id: 3, name: 'old3' }] }
 * ];
 * const targetTree = [
 *   { id: 1, name: 'new', value: 200, children: [{ id: 2, name: 'new2' }] }
 * ];
 * const fields = ['name'];
 * mergeTreeNodes(sourceTree, targetTree, fields);
 * // 返回: [{ id: 1, name: 'new', value: 100, children: [{ id: 2, name: 'new2' }] }]
 */
export function mergeTreeNodes(sourceTree, targetTree, fields, childrenKey = 'children', idKey = 'id') {
  // 如果输入为空,返回空数组
  if (!targetTree?.length || !fields?.length) {
    return [];
  }
  if (!sourceTree?.length) {
    return JSON.parse(JSON.stringify(targetTree)); // 深拷贝targetTree
  }

  // 递归处理函数
  function merge(sourceNodes, targetNodes) {
    // 创建sourceNodes的Map,方便查找
    const sourceMap = new Map(
      sourceNodes.map(node => [node[idKey], node])
    );

    // 遍历targetNodes,构建新的树结构
    return targetNodes.map(targetNode => {
      const sourceNode = sourceMap.get(targetNode[idKey]);

      // 创建新节点
      const newNode = sourceNode
        ? { ...sourceNode } // 如果找到源节点,复制它
        : { ...targetNode }; // 如果没找到,使用目标节点

      // 更新指定字段
      fields.forEach(field => {
        if (field in targetNode) {
          newNode[field] = targetNode[field];
        }
      });

      // 处理子节点
      const targetChildren = targetNode[childrenKey];
      if (targetChildren?.length) {
        const sourceChildren = sourceNode?.[childrenKey] || [];
        newNode[childrenKey] = merge(sourceChildren, targetChildren);
      } else {
        // 如果targetNode没有子节点,确保newNode也没有子节点
        delete newNode[childrenKey];
      }

      return newNode;
    });
  }

  return merge(sourceTree, targetTree);
}


/**
 * 将扁平的任务数据转换为树状结构
 * @param {Array} data - 原始任务数据数组
 * @returns {Array} 树状结构的任务数据
 */
export function convertToTreeStructure(data) {
  // 创建一个Map来存储所有节点，以taskId为key
  const nodeMap = new Map();

  // 第一步：将所有节点添加到Map中，并初始化children数组
  data.forEach(item => {
    nodeMap.set(item.taskId, {
      ...item,
      children: []
    });
  });

  // 第二步：构建树状结构
  const rootNodes = [];

  data.forEach(item => {
    const node = nodeMap.get(item.taskId);

    if (item.parentTaskId === 0) {
      // 根节点
      rootNodes.push(node);
    } else {
      // 子节点，添加到父节点的children中
      const parentNode = nodeMap.get(item.parentTaskId);
      if (parentNode) {
        parentNode.children.push(node);
      }
    }
  });

  return rootNodes;
}



/**
 * 将后端返回数据转换为表单渲染配置
 * @param {Array} data - 后端返回数据
 * @returns {Array} - 表单渲染配置
 */
export function convertToFormConfig(data) {
  const formConfig = data.map(item => {
    const isSelect = ['taskStatus', 'predecessorType', 'offsetType', 'isMilestone'].includes(item.fieldName);
    const isRequired = ['serialNo', 'taskStatus', 'taskCode', 'taskName', 'taskContent', 'responsiblePerson', 'plannedStartDate', 'plannedEndDate', 'plannedDuration'].includes(item.fieldName);
    const isDate = ['plannedStartDate', 'plannedEndDate', 'actualStartDate', 'actualEndDate'].includes(item.fieldName);
    const isTreeSelect = ['predecessorTaskId'].includes(item.fieldName);
    const fieldConfig = {
      label: item.fieldLabel,
      prop: item.fieldName,
      type: 'Input',
      options: {
        clearable: true,
        placeholder: isSelect || isDate || isTreeSelect ? `请选择${item.fieldLabel}` : `请输入${item.fieldLabel}`
      },
      rules: [
        {
          required: isRequired,
          message: isSelect || isDate || isTreeSelect ? `请选择${item.fieldLabel}` : `请输入${item.fieldLabel}`,
          trigger: 'blur'
        }
      ]
    }

    if (isDate) {
      fieldConfig.type = 'DatePicker';
    } else if (isSelect) {
      fieldConfig.type = 'Select';
      fieldConfig.option = GanttEditFormOptions[item.fieldName] || [];
    } else if (isTreeSelect) {
      fieldConfig.type = 'TreeSelect';
      fieldConfig.option = [];
      fieldConfig.options = {
        ...fieldConfig.options,
        multiple: true,
        flat: true,
        fieldConfig: {
          idKey: 'taskId',
          labelKey: 'taskName',
          childrenKey: 'children',
        }
      };
    } else {
      fieldConfig.type = 'Input';
    }
    return fieldConfig;
  });
  console.log('123', data, formConfig)
  return {
    ...DefaultFormConfig,
    formJson: {
      ...DefaultFormConfig.formJson,
      formItemJson:[{
        label: '任务信息',
        showTitle: false,
        children: formConfig
      }]
    }
  }
}
