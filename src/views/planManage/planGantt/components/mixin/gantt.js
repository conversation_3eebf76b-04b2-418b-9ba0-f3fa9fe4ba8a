
import * as VTableGantt from '@visactor/vtable-gantt';
import * as VTable_editors from '@visactor/vtable-editors';
import * as VTable from '@visactor/vtable';
import { deleteTreeNodeByPath, flattenTreeIds, mergeTreeNodes } from '../../utils/index';
import dayjs from "dayjs";
import { records, columns, defaultRecord, OffsetType } from '../../constants';
import { deepClone } from '@/utils';
const popup = document.createElement('div');
Object.assign(popup.style, {
  position: 'fixed',
  width: '300px',
  backgroundColor: '#f1f1f1',
  border: '1px solid #ccc',
  padding: '20px',
  textAlign: 'left',
});

export default {
  data() {
    return {
      records: defaultRecord,
      ganttInstance: null,
      currentPosition: null,
      currentTimeUnit: 'day', // 当前时间单位
      timeUnits: [
        { label: '日', value: 'day' },
        { label: '周', value: 'week' },
        { label: '月', value: 'month' },
        { label: '季', value: 'quarter' },
        { label: '年', value: 'year' }
      ],
      ganttOptions: null, // 保存甘特图配置
    };
  },
  beforeDestroy() {
    if (this.ganttInstance) {
      this.ganttInstance.release();
    }
  },
  methods: {
    // 添加新方法获取列宽度
    getColWidth(unit) {
      const widthMap = {
        day: 80,
        week: 80,
        month: 100,
        quarter: 200,
        year: 300
      };
      return widthMap[unit] || 80;
    },

    // 添加新方法计算日期范围
    getDateRange(unit) {
      const now = new Date();
      const result = {
        minDate: null,
        maxDate: null
      };

      switch(unit) {
        case 'day':
        case 'week':
          // 当前时间前后3个月
          result.minDate = new Date(now.getFullYear(), now.getMonth() - 3, 1);
          result.maxDate = new Date(now.getFullYear(), now.getMonth() + 3, 0);
          break;

        case 'month':
          // 当前年
          result.minDate = new Date(now.getFullYear(), 0, 1);
          result.maxDate = new Date(now.getFullYear(), 11, 31);
          break;

        case 'quarter':
          // 最近2年
          result.minDate = new Date(now.getFullYear() - 1, 0, 1);
          result.maxDate = new Date(now.getFullYear() + 1, 11, 31);
          break;

        case 'year':
          // 最近3年
          result.minDate = new Date(now.getFullYear() - 1, 0, 1);
          result.maxDate = new Date(now.getFullYear() + 2, 11, 31);
          break;

        default:
          // 默认显示当年
          result.minDate = new Date(now.getFullYear(), 0, 1);
          result.maxDate = new Date(now.getFullYear(), 11, 31);
      }

      // 格式化日期为 YYYY-MM-DD
      const formatDate = (date) => {
        return date.toISOString().split('T')[0];
      };

      return {
        minDate: formatDate(result.minDate),
        maxDate: formatDate(result.maxDate)
      };
    },

    // 切换时间单位
    changeTimeUnit(unit) {
      this.currentTimeUnit = unit;
      const scales = this.getTimeScales(unit);

      // 使用初始配置的深拷贝
      const currentOptions = this.ganttOptions

      // 更新配置
      currentOptions.timelineHeader.scales = scales;
      // 根据时间单位设置列宽
      currentOptions.timelineHeader.colWidth = this.getColWidth(unit);

      // 设置日期范围
      const dateRange = this.getDateRange(unit);
      currentOptions.minDate = dateRange.minDate;
      currentOptions.maxDate = dateRange.maxDate;

      // 保留当前任务数据
      currentOptions.records = this.records;

      // 使用更新后的配置
      this.updateOption(currentOptions);
    },
    // 获取时间刻度配置
    getTimeScales(unit) {
      const baseStyle = {
        fontSize: 13,
        fontFamily: 'PingFang SC',
        textAlign: 'center',
        textBaseline: 'middle',
        color: '#262626',
        padding: [8, 0],
      };

      switch(unit) {
        case 'day':
          // 日视图：上栏显示年月，下栏显示日期
          return [
            {
              unit: 'month',
              step: 1,
              format: (date) => {
                return `${date.startDate.getFullYear()}年${date.startDate.getMonth() + 1}月`;
              },
              style: baseStyle,
            },
            {
              unit: 'day',
              step: 1,
              format: (date) => {
                return date.startDate.getDate().toString();
              },
              style: {
                ...baseStyle,
                fontSize: 12,
                color: '#8c8c8c',
              },
            }
          ];

        case 'week':
          // 周视图：上栏显示年月，下栏显示第几周
          return [
            {
              unit: 'month',
              step: 1,
              format: (date) => {
                return `${date.startDate.getFullYear()}年${date.startDate.getMonth() + 1}月`;
              },
              style: baseStyle,
            },
            {
              unit: 'week',
              step: 1,
              format: (date) => {
                return `第${date.dateIndex}周`;
              },
              style: {
                ...baseStyle,
                fontSize: 12,
                color: '#8c8c8c',
              },
            }
          ];

        case 'quarter':
          // 季度视图：上栏显示年，下栏显示季度，均分宽度
          return [
            {
              unit: 'year',
              step: 1,
              format: (date) => {
                return `${date.startDate.getFullYear()}年`;
              },
              style: {
                ...baseStyle,
                width: '100%',
              },
            },
            {
              unit: 'quarter',
              step: 1,
              format: (date) => {
                return `第${Math.floor(date.startDate.getMonth() / 3) + 1}季度`;
              },
              style: {
                ...baseStyle,
                fontSize: 12,
                color: '#8c8c8c',
                width: '100%',
              },
            }
          ];

        case 'year':
          // 年视图：单栏显示年份，均分宽度
          return [
            {
              unit: 'year',
              step: 1,
              format: (date) => {
                return `${date.startDate.getFullYear()}年`;
              },
              style: {
                ...baseStyle,
                width: '800px',
              },
            }
          ];

        default:
          // 月视图：下栏显示月
          return [
            {
              unit: 'month',
              step: 1,
              format: (date) => {
                const year = date.startDate.getFullYear();
                const month = date.startDate.getMonth() + 1;
                return `${year}年${month}月`;
              },
              style: {
                ...baseStyle,
                fontSize: 12,
                color: '#8c8c8c',
              },
            }
          ];
      }
    },

    initGantt(planDetail) {
      console.log('VTableGantt', VTableGantt);
      const input_editor = new VTable_editors.InputEditor();
      const date_input_editor = new VTable_editors.DateInputEditor();
      VTableGantt.VTable.register.editor('input', input_editor);
      VTableGantt.VTable.register.editor('date-input', date_input_editor);
      // 解析 ganttConfig
      let ganttConfig = {};
      try {
        ganttConfig = JSON.parse(planDetail.ganttConfig);
      } catch (error) {
        console.error('ganttConfig 解析失败', error);
      }
      const option = {
        // records,
        records: [{
          isRoot: true,
          id: '',
          title: '',
          developer: '',
          start: '',
          end: '',
          taskBarStart: '',
          taskBarEnd: '',
          progress: '',
          dutyPerson: '',
          actualStart: '',
          actualEnd: '',
          actualDuration: '',
        }],
        taskListTable: {
          columns: this.columnsFields,
          // 展开层级
          hierarchyExpandLevel: 8,
          menu: {
            contextMenuItems: ['从施工包添加任务', '新建任务', '编辑任务', '删除任务'],
          },
          tableWidth: 800,
          minTableWidth: 0,
          maxTableWidth: '100%',
          theme: {
            headerStyle: {
              borderColor: '#e1e4e8',
              fontSize: 13,
              fontFamily: 'PingFang SC',
              fontWeight: 500,
              color: '#262626',
              bgColor: '#EEF1F5',
              padding: [12, 16],
            },
            bodyStyle: {
              fontSize: 13,
              fontFamily: 'PingFang SC',
              color: '#595959',
              bgColor: '#ffffff',
              borderColor: '#f0f0f0',
              padding: [0, 16],
            },
          },
        },
        taskKeyField: 'taskId',
        dependency: {
          // 依赖关系
          links: ganttConfig?.dependencyLinks || [
            // {
            //   type: VTableGantt.TYPES.DependencyType.StartToStart,
            //   linkedFromTaskKey: 10,
            //   linkedToTaskKey: 23
            // }
            // {
            //   type: VTableGantt.TYPES.DependencyType.FinishToStart,
            //   linkedFromTaskKey: 1,
            //   linkedToTaskKey: 2,
            // },
            // {
            //   type: VTableGantt.TYPES.DependencyType.StartToFinish,
            //   linkedFromTaskKey: 2,
            //   linkedToTaskKey: 3,
            // },
            // {
            //   type: VTableGantt.TYPES.DependencyType.StartToStart,
            //   linkedFromTaskKey: 3,
            //   linkedToTaskKey: 4,
            // },
            // {
            //   type: VTableGantt.TYPES.DependencyType.FinishToFinish,
            //   linkedFromTaskKey: 4,
            //   linkedToTaskKey: 5,
            // },
          ],
          // linkSelectable: false,
          linkSelectedLineStyle: {
            shadowBlur: 5, //阴影宽度
            shadowColor: '#F56C6C',
            lineColor: '#F56C6C',
            lineWidth: 1,
          },
          // 连接线可创建, 当点击任务条时，会出现创建依赖线的操作点
          linkCreatable: false,
          // 连接线可删除
          linkDeletable: false,
        },
        tasksShowMode: VTableGantt.TYPES.TasksShowMode.Tasks_Separate,
        frame: {
          verticalSplitLineMoveable: true,
          outerFrameStyle: {
            borderColor: '#ebedf0',
            borderLineWidth: 1,
            cornerRadius: 12,
            padding: [1, 1, 1, 1],
          },
          verticalSplitLine: {
            lineWidth: 3,
            lineColor: '#e1e4e8',
          },
          verticalSplitLineHighlight: {
            lineColor: 'green',
            lineWidth: 3,
          },
        },
        grid: {
          backgroundColor: '#fafaff',
          weekendBackgroundColor: 'rgba(94, 180, 245, 0.10)',
          verticalLine: {
            lineWidth: 1,
            lineColor: '#f5f5f5',
          },
          horizontalLine: {
            lineWidth: 1,
            lineColor: '#f5f5f5',
          },
        },
        headerRowHeight: 60,
        rowHeight: 40,
        // 使用快捷键删除连接线
        keyboardOptions: {
          deleteLinkOnBack: true,
          deleteLinkOnDel: true,
        },
        taskBar: {
          startDateField: 'taskBarStart',
          endDateField: 'taskBarEnd',
          progressField: 'progress',
          // 任务条是否可创建任务
          scheduleCreation: false,
          scheduleCreatable: false,
          // 任务条是否可移动
          moveable: false,
          // 任务条是否可调整宽度
          resizable: false,
          // 任务条是否可拖拽到时间范围外
          moveToExtendDateRange: true,
          hoverBarStyle: {
            barOverlayColor: 'rgba(99, 144, 0, 0.2)',
          },
          labelText: '{taskName} {progress}%',
          labelTextStyle: {
            // padding: 2,
            fontFamily: 'Arial',
            fontSize: 13,
            textAlign: 'left',
            textOverflow: 'ellipsis',
            color: 'rgb(240, 246, 251)',
          },
          barStyle: {
            width: 24,
            barColor: '#d6e4ff',
            completedBarColor: '#597ef7',
            cornerRadius: 12,
            borderLineWidth: 2,
            borderColor: 'rgb(7, 88, 150)',
          },
          milestoneStyle: {
            width: 16,
            fillColor: value => (value.record.progress >= 100 ? '#597ef7' : '#d6e4ff'),
            borderColor: '#597ef7',
            borderLineWidth: 0,
            labelText: '{title}',
            labelTextStyle: {
              fontSize: 14,
              color: 'rgb(1, 43, 75)',
            },
          },
          // 自定义任务条
          // TODO: 自定义任务条, 会导致配置丢失, 暂时注释
          // customLayout: this.customLayout,
        },
        timelineHeader: {
          verticalLine: {
            lineWidth: 1,
            lineColor: '#e1e4e8',
          },
          horizontalLine: {
            lineWidth: 1,
            lineColor: '#e1e4e8',
          },
          backgroundColor: '#EEF1F5',
          colWidth: 80,
          scales: this.getTimeScales(this.currentTimeUnit),
        },
        minDate: '2025-01-14',
        maxDate: '2025-10-15',
        markLine: [
          {
            date: dayjs().format('YYYY-MM-DD'),
            style: {
              lineWidth: 1,
              lineColor: 'blue',
              lineDash: [8, 4]
            }
          }
        ],
        rowSeriesNumber: {
          title: '行号',
          dragOrder: true,
          headerStyle: {
            bgColor: '#EEF1F5',
            borderColor: '#e1e4e8',
          },
          style: {
            color: '#000',
            fontSize: 14,
          },
        },
        scrollStyle: {
          visible: 'focus',
        },
        overscrollBehavior: 'none',
      };
      console.log('option', option);
      // 保存初始配置
      // this.ganttOptions = JSON.parse(JSON.stringify(option));
      this.ganttOptions = option;

      this.ganttInstance = new VTableGantt.Gantt(this.$refs.ganttContainer, option);
      this.addMenuClickEvent();
      this.addCellClickEvent();
      this.addChangeDateRangeEvent();
      this.addCreateDependencyLinkEvent();
      this.addCreateTaskScheduleEvent();
      console.log('this.ganttInstance', this.ganttInstance);
    },

    addMenuClickEvent() {
      this.ganttInstance.taskListTableInstance.on(VTable.ListTable.EVENT_TYPE.DROPDOWN_MENU_CLICK, e => {
        const { col, row, menuKey } = e;
        // 获取要删除的项
        const currentRecord = this.ganttInstance.taskListTableInstance.getRecordByCell(col, row);
        this.currentEditTask = currentRecord;
        this.currentPosition = {
          col,
          row,
        };
        console.log('currentRecord', currentRecord);
        if (menuKey === '删除任务') {
          if (currentRecord.isRoot) {
            this.$message.error('根项不能删除');
          } else {
            // let deleteIndexs = this.ganttInstance.taskListTableInstance.getRecordIndexByCell(col, row);
            // if (deleteIndexs === 0 && this.records.length === 1) {
            //   this.setRecords(defaultRecord);
            // } else {
            //   if (typeof deleteIndexs === 'number') {
            //     deleteIndexs = [deleteIndexs]
            //   }
            //   deleteTreeNodeByPath(this.records, deleteIndexs);
            //   this.setRecords(this.records);
            // }

            this.deleteTask(currentRecord);
          }
        } else if (menuKey === '从施工包添加任务') {
          // 如果当前项是根项，则打开抽屉
          if (currentRecord.isRoot) {
            this.openDrawer([])
          } else {
            const treeIds = flattenTreeIds(this.records);
            console.log('ids', treeIds);
            this.openDrawer(treeIds)
          }
        } else if (menuKey === '新建任务') {
          // 如果当前项是根项，则打开抽屉
          if (currentRecord.isRoot) {
            // this.openDrawer([])
          }
          console.log('currentRecord', currentRecord);
          this.editDialogVisible = true;
        } else if (menuKey === '编辑任务') {
          // 如果存在 extendedFieldValue 字段，则解析并更新当前编辑任务
          try {
            const extendedFieldValue = currentRecord.extendedFieldValue;
            if (extendedFieldValue) {
              this.currentEditTask = {
                ...this.currentEditTask,
                ...(JSON.parse(extendedFieldValue))
              }
            }
          } catch (err) {
            console.log('解析extendedFieldValue失败', err);
          }
          if (currentRecord.isRoot) {
            this.$message.error('初始项不能编辑');
          } else {
            console.log('currentRecord', currentRecord);
            this.editDialogVisible = true;
          }
        }
      });
    },

    addCellClickEvent() {
      this.ganttInstance.taskListTableInstance.on(VTable.ListTable.EVENT_TYPE.CLICK_CELL, e => {
        console.log('CLICK_CELL', e);
      });
    },
    addChangeDateRangeEvent() {
      this.ganttInstance.on(VTableGantt.TYPES.GANTT_EVENT_TYPE.CHANGE_DATE_RANGE, e => {
        console.log('CHANGE_DATE_RANGE 时间变化', e);
        this.updateTask(e);
      });
    },
    addCreateTaskScheduleEvent() {
      this.ganttInstance.on(VTableGantt.TYPES.GANTT_EVENT_TYPE.CREATE_TASK_SCHEDULE, e => {
        console.log('CREATE_TASK_SCHEDULE 创建任务', e);
        this.updateTask(e);
      });
    },
    addCreateDependencyLinkEvent() {
      this.ganttInstance.on(VTableGantt.TYPES.GANTT_EVENT_TYPE.CREATE_DEPENDENCY_LINK, e => {
        console.log('CREATE_DEPENDENCY_LINK 创建依赖关系', e);
        this.updateDependencyLink(e);
      });
    },

    // 创建依赖关系时，需要根据四种依赖关系ss、sf、ff、fs，计算start、end、taskBarStart、taskBarEnd，并更新目标任务的 前置任务 preTask 前置任务 preTaskType 字段
    updateDependencyLink(e) {
      const link = e.link;
      const { linkedFromTaskKey, linkedToTaskKey, type } = link;
      const currentRecords = [...this.records];

      // 递归查找任务节点
      const findTask = (nodes, targetId) => {
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i];
          if (node.id === targetId) {
            return node;
          }
          if (node.children && node.children.length > 0) {
            const found = findTask(node.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };

      // 查找源任务和目标任务
      const sourceTask = findTask(currentRecords, linkedFromTaskKey);
      const targetTask = findTask(currentRecords, linkedToTaskKey);

      if (!sourceTask || !targetTask) {
        console.error('未找到相关任务');
        return;
      }

      // 新增：每个任务只允许有一个前置任务
      // if (targetTask.preTask) {
      //   this.$message.error('每个任务只允许有一个前置任务');
      //   return;
      // }

      // 根据依赖类型计算时间
      const calculateTime = () => {
        const { offsetValue, offsetType } = targetTask || {};
        const timeOffset = (offsetType === OffsetType.lead ? offsetValue : -offsetValue) || 0;

        switch (type) {
          case 'start_to_start':
            targetTask.start = sourceTask.start;
            targetTask.taskBarStart = dayjs(sourceTask.start).add(timeOffset, 'day').format('YYYY-MM-DD');
            break;
          case 'start_to_finish':
            targetTask.end = sourceTask.start;
            targetTask.taskBarEnd = dayjs(sourceTask.start).add(timeOffset, 'day').format('YYYY-MM-DD');
            break;
          case 'finish_to_start':
            targetTask.start = sourceTask.end;
            targetTask.taskBarStart = dayjs(sourceTask.end).add(timeOffset, 'day').format('YYYY-MM-DD');
            break;
          case 'finish_to_finish':
            targetTask.end = sourceTask.end;
            targetTask.taskBarEnd = dayjs(sourceTask.end).add(timeOffset, 'day').format('YYYY-MM-DD');
            break;
        }
      };

      // 更新目标任务的依赖关系字段
      const updateDependencyFields = () => {
        targetTask.preTask = linkedFromTaskKey;
        targetTask.preTaskType = type;
      };

      // 执行更新
      calculateTime();
      updateDependencyFields();

      // 更新整个数据源
      this.setRecords(currentRecords);
    },

    // 时间变化、创建任务时，更新对应任务对的start、end、taskBarStart、taskBarEnd值，需根据偏移量计算start、end
    updateTask(e) {
      const { record, startDate, endDate } = e;
      const id = record.id;

      // 获取当前记录数据
      const currentRecords = [...this.records];

      // 用于记录已处理的任务，避免循环依赖
      const processedTasks = new Set();

      // 递归查找并更新节点
      const updateNode = (nodes, targetId) => {
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i];

          // 如果找到目标节点
          if (node.id === targetId) {
            // 获取偏移量,如果存在偏移量,则根据偏移量计算start、end
            const { offsetValue, offsetType } = node || {};
            if (offsetValue && offsetType) {
              console.log('偏移量', offsetValue, offsetType)
              // 此处需要取反，因为是根据 taskBar 反显任务
              const timeOffset = offsetType === OffsetType.lead ? offsetValue : -offsetValue;
              node.start = dayjs(startDate).add(timeOffset, 'day').format('YYYY-MM-DD');
              node.end = dayjs(endDate).add(timeOffset, 'day').format('YYYY-MM-DD');
            } else {
              node.start = startDate;
              node.end = endDate;
            }
            console.log('node', node, startDate, endDate);
            // 更新时间相关字段
            node.taskBarStart = startDate;
            node.taskBarEnd = endDate;
            return true;
          }

          // 如果有子节点,递归查找
          if (node.children && node.children.length > 0) {
            if (updateNode(node.children, targetId)) {
              return true;
            }
          }
        }
        return false;
      };

      // 查找所有依赖于指定任务的任务
      const findDependentTasks = (nodes, sourceTaskId) => {
        const dependentTasks = [];
        const traverse = (nodes) => {
          for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i];
            if (node.preTask === sourceTaskId) {
              dependentTasks.push(node);
            }
            if (node.children && node.children.length > 0) {
              traverse(node.children);
            }
          }
        };
        traverse(nodes);
        return dependentTasks;
      };

      // 根据依赖类型更新依赖任务的时间
      const updateDependentTaskTime = (sourceTask, dependentTask) => {
        if (processedTasks.has(dependentTask.id)) {
          return;
        }
        processedTasks.add(dependentTask.id);

        const { offsetValue, offsetType } = dependentTask || {};
        const timeOffset = (offsetType === OffsetType.lead ? -offsetValue : offsetValue) || 0;

        // 计算任务的原始时长(天数)
        const originalDuration = (dayjs(dependentTask.end).diff(dayjs(dependentTask.start), 'day')) || 0;

        switch (dependentTask.preTaskType) {
          case 'start_to_start':
            // 保持原始时长,设置新的开始时间
            dependentTask.start = sourceTask.start;
            dependentTask.taskBarStart = dayjs(sourceTask.start).add(timeOffset, 'day').format('YYYY-MM-DD');
            // 根据原始时长设置结束时间
            dependentTask.end = dayjs(dependentTask.start).add(originalDuration, 'day').format('YYYY-MM-DD');
            dependentTask.taskBarEnd = dayjs(dependentTask.taskBarStart).add(originalDuration, 'day').format('YYYY-MM-DD');
            break;
          case 'start_to_finish':
            // 保持原始时长,设置新的结束时间
            dependentTask.end = sourceTask.start;
            dependentTask.taskBarEnd = dayjs(sourceTask.start).add(timeOffset, 'day').format('YYYY-MM-DD');
            // 根据原始时长设置开始时间
            dependentTask.start = dayjs(dependentTask.end).subtract(originalDuration, 'day').format('YYYY-MM-DD');
            dependentTask.taskBarStart = dayjs(dependentTask.taskBarEnd).subtract(originalDuration, 'day').format('YYYY-MM-DD');
            break;
          case 'finish_to_start':
            // 保持原始时长,设置新的开始时间
            dependentTask.start = sourceTask.end;
            dependentTask.taskBarStart = dayjs(sourceTask.end).add(timeOffset, 'day').format('YYYY-MM-DD');
            // 根据原始时长设置结束时间
            dependentTask.end = dayjs(dependentTask.start).add(originalDuration, 'day').format('YYYY-MM-DD');
            dependentTask.taskBarEnd = dayjs(dependentTask.taskBarStart).add(originalDuration, 'day').format('YYYY-MM-DD');
            break;
          case 'finish_to_finish':
            // 保持原始时长,设置新的结束时间
            dependentTask.end = sourceTask.end;
            dependentTask.taskBarEnd = dayjs(sourceTask.end).add(timeOffset, 'day').format('YYYY-MM-DD');
            // 根据原始时长设置开始时间
            dependentTask.start = dayjs(dependentTask.end).subtract(originalDuration, 'day').format('YYYY-MM-DD');
            dependentTask.taskBarStart = dayjs(dependentTask.taskBarEnd).subtract(originalDuration, 'day').format('YYYY-MM-DD');
            break;
        }

        // 递归处理依赖链
        const nextDependentTasks = findDependentTasks(currentRecords, dependentTask.id);
        nextDependentTasks.forEach(task => {
          updateDependentTaskTime(dependentTask, task);
        });
      };

      // 从根节点开始查找并更新当前任务
      updateNode(currentRecords, id);

      // 找到当前任务节点
      const findCurrentTask = (nodes, targetId) => {
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i];
          if (node.id === targetId) {
            return node;
          }
          if (node.children && node.children.length > 0) {
            const found = findCurrentTask(node.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };

      const currentTask = findCurrentTask(currentRecords, id);
      if (currentTask) {
        // 更新所有依赖任务的时间
        const dependentTasks = findDependentTasks(currentRecords, id);
        dependentTasks.forEach(task => {
          updateDependentTaskTime(currentTask, task);
        });
      }

      // 更新整个数据源
      this.setRecords(currentRecords);
    },
    // 添加依赖关系
    addLink(link) {
      const dependencyLinks = this.ganttInstance.options.dependency.links;
      const currentIndex = dependencyLinks.findIndex(item => item.linkedFromTaskKey === link.linkedFromTaskKey && item.linkedToTaskKey === link.linkedToTaskKey);
      if (currentIndex !== -1) {
        dependencyLinks[currentIndex] = link;
      } else {
        dependencyLinks.push(link);
      }
      const currentOptions = this.ganttOptions
      currentOptions.records = this.records;
      currentOptions.dependency.links = dependencyLinks;
      this.updateOption(currentOptions);
    },

    // 更新配置
    updateOption(options) {
      this.ganttInstance.updateOption(options);
      this.ganttOptions = options;
    },
    // 更新依赖关系
    updateDependency(dependency = []) {
      const currentOptions = {
        ...this.ganttOptions,
        dependency: {
          ...this.ganttOptions.dependency,
          links: [
            ...dependency,
          ],
        },
      };
      console.log('currentOptions12', currentOptions);
      this.ganttInstance.updateOption(currentOptions);
      this.ganttOptions = currentOptions;
    },

    updateRecords(record) {
      // 获取当前记录数据
      const currentRecords = [...this.records];

      // 递归查找并更新节点
      const updateNode = (nodes, targetId) => {
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i];

          // 如果找到目标节点
          if (node.id === targetId) {
            // 保留原有数据,只更新编辑的字段
            Object.keys(record).forEach(key => {
              if (record[key] !== undefined && record[key] !== null && record[key] !== '') {
                node[key] = record[key];
              }
            });
            return true;
          }

          // 如果有子节点,递归查找
          if (node.children && node.children.length > 0) {
            if (updateNode(node.children, targetId)) {
              return true;
            }
          }
        }
        return false;
      };

      // 从根节点开始查找并更新
      updateNode(currentRecords, record.id);

      // 更新整个数据源
      this.setRecords(currentRecords);
    },

    setRecords(data, isMerge = false) {
      console.log('setRecords', data);
      if (isMerge) {
        if (!this.records[0].isRoot) {
          this.records = mergeTreeNodes( this.records, data, ['id', 'name']);
        } else {
          this.records = data;
        }
      } else {
        this.records = data;
      }
      console.log('setRecords2', this.records);
      this.ganttInstance?.setRecords?.(this.records);
    },

    setTasksMode(mode) {
      this.ganttInstance.updateTasksShowMode(mode);
    },

    customLayout(args) {
      console.log('customLayout', args);
      const { width, height, taskRecord } = args;
      const that_ = this;
      const container = new VTableGantt.VRender.Group({
        width,
        height,
        cornerRadius: 10,
        fill: '#446eeb',
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        alignItems: 'center',
        boundsPadding: 10,
        zIndex: 1,
      });

      // if (taskRecord?.timeConflict) {
      //   const reportIcon = new VTableGantt.VRender.Image({
      //     width: 20,
      //     height: 20,
      //     image: report,
      //   });
      //   container.add(reportIcon);
      // }

      // if (taskRecord?.keyNode) {
      //   const reportIcon = new VTableGantt.VRender.Image({
      //     width: 20,
      //     height: 20,
      //     image: flag,
      //   });
      //   container.add(reportIcon);
      // }

      const name = new VTableGantt.VRender.Text({
        text: taskRecord.name,
        fill: taskRecord?.keyNode ? '#fff' : '#0f2819',
        suffixPosition: 'end',
        fontSize: 14,
        boundsPadding: 10,
      });

      container.add(name);

      container.addEventListener('mouseenter', event => {
        console.log('mouseenter', event);
        const container = this.$refs.ganttContainer;
        const containerRect = container.getBoundingClientRect();
        const targetY = event.target.globalAABBBounds.y2;
        const targetX = event.target.globalAABBBounds.x1;
        that_.showTooltip(taskRecord, event.client.x, targetY + containerRect.top);
      });

      container.addEventListener('mouseleave', () => {
        that_.hideTooltip();
      });
      console.log('first', container);

      return {
        rootContainer: container,
        renderDefaultBar: true,
        renderDefaultText: true,
      };
    },
    hideTooltip() {
      if (document.body.contains(popup)) {
        document.body.removeChild(popup);
      }
    },
    showTooltip(infoList, x, y) {
      popup.innerHTML = '';
      popup.id = 'popup';
      popup.style.left = x + 'px';
      popup.style.top = y + 'px';
      const heading = document.createElement('h4');
      heading.textContent = '任务信息';
      heading.style.margin = '0px';
      popup.appendChild(heading);
      const keys = {
        title: '名称',
        start: '计划开始',
        end: '计划结束',
        progress: '进度',
        priority: '优先级',
      };
      for (const key in infoList) {
        if (!keys[key]) {
          continue;
        }
        const info = infoList[key];
        const info1 = document.createElement('p');
        info1.textContent = keys[key] + ': ' + info;
        popup.appendChild(info1);
      }

      // 将弹出框添加到文档主体中
      document.body.appendChild(popup);
    },
  },
};
