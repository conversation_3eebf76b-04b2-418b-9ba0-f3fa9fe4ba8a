<template>
  <div class="gantt-container">
    <div id="ganttContainer" ref="ganttContainer" class="gantt-layout" />
    <edit-task-dialog :visible.sync="editDialogVisible" :task-data="currentEditTask" @submit="handleEditSubmit" />
    <el-drawer title="从施工包添加任务" :visible.sync="drawerVisible">
      <tree-drawer :visible="drawerVisible" :default-checked-keys="treeIds" @submit="handleTreeSubmit"
        @cancel="handleTreeCancel" />
    </el-drawer>
  </div>
</template>

<script>
import ganttMixin from './mixin/gantt';
import treeDrawer from './treeDrawer';
import EditTaskDialog from './EditTaskDialog/index.vue';
import { OffsetType } from '../constants';
import dayjs from 'dayjs';
import { convertToTreeStructure } from '../utils';
import * as VTableGantt from '@visactor/vtable-gantt';
import {
  postBpmPlanTaskImportProjpackage,
  putBpmPlanTask,
  postBpmPlanTask,
  getBpmPlanTaskList,
  deleteBpmPlanTaskTaskId,
} from '@/api/v2/plan/plan-task-controller';
export default {
  components: {
    treeDrawer,
    EditTaskDialog,
  },
  props: {},
  mixins: [ganttMixin],
  provide() {
    return {
      getRecords: () => this.records,
    };
  },
  inject: ['getColumnsFields', 'getUserFields'],
  data() {
    return {
      drawerVisible: false,
      // 默认选中树 ids, 即左侧列表 id 拉平后的 ids
      treeIds: [],
      editDialogVisible: false,
      currentEditTask: null,
      // 任务 map
      taskListMap: null
    };
  },
  computed: {
    columnsFields() {
      return this.getColumnsFields?.() || [];
    },
    userFields() {
      return this.getUserFields?.() || {};
    }
  },
  mounted() {
    // this.$nextTick(() => {
    //   // const timer = setTimeout(() => {
    //   //   this.getTaskList();
    //   //   clearTimeout(timer);
    //   // }, 200);
    // });
  },
  watch: {
    // columnsFields: {
    //   handler(newVal) {
    //     if (newVal?.length) {
    //       // this.initGantt();
    //     }
    //   },
    //   immediate: true,
    // },
  },
  methods: {
    init(planDetail) {
      this.initGantt(planDetail);
      this.getTaskList();
    },
    openDrawer(treeIds) {
      if (treeIds?.length) {
        this.treeIds = treeIds;
      }
      this.drawerVisible = true;
    },
    addTask() {
      console.log('addTask');
    },
    save() {
      console.log('save', this.ganttInstance);
    },
    handleTreeCancel() {
      this.drawerVisible = false;
    },
    // 处理依赖关系
    // handleDependency(formData) {
    //   const { preTask, preTaskType } = formData;
    //   if (!preTask) return null;
    //   return {
    //     type: preTaskType,
    //     linkedFromTaskKey: Number(preTask),
    //     linkedToTaskKey: Number(formData.id),
    //   };
    // },
    // 任务依赖关系处理
    handleDependency(taskRowsItem) {
      const { predecessorTaskId = [], predecessorType } = taskRowsItem;
      const { taskId } = this.currentEditTask;
      let oldPredecessorTaskId = this.currentEditTask?.predecessorTaskId
      if(typeof oldPredecessorTaskId === 'string') {
        oldPredecessorTaskId = oldPredecessorTaskId.split(',')
      }
      const currentLinks = this.ganttOptions.dependency.links || [];
      // 需要新增的 id 集合
      const addTaskIds = predecessorTaskId.filter(item => !oldPredecessorTaskId.includes(item));
      // 需要删除的 id 集合
      const deleteTaskIds = oldPredecessorTaskId.filter(item => !predecessorTaskId.includes(item));
      if (addTaskIds.length > 0) {
        addTaskIds.forEach((item) => {
          currentLinks.push({
            type: predecessorType,
            linkedFromTaskKey: Number(item),
            linkedToTaskKey: Number(taskId),
          });
        });
      }

      if (deleteTaskIds.length > 0) {
        deleteTaskIds.forEach((el) => {
          const index = currentLinks.findIndex(item => item.linkedFromTaskKey === Number(el) && item.linkedToTaskKey === Number(taskId) && item.type === predecessorType)
          if (index !== -1) {
            currentLinks.splice(index, 1)
          }
        });
      }

      console.log('任务依赖关系处理', currentLinks);
      this.updateDependency(currentLinks);
    },
    // 校验依赖关系
    checkDependency(taskRowsItem) {
      const { predecessorTaskId, predecessorType } = taskRowsItem;
      // 未填写则不校验
      if (!predecessorTaskId || !predecessorType) return true
      const { taskId } = this.currentEditTask;
      const preTaskIds = predecessorTaskId.map?.(item => Number(item)) || []
      // 校验依赖关系
      if (preTaskIds.includes(taskId)) {
        this.$message.error('不允许设置自身为前置任务');
        return false;
      }
      // TODO: 需要补充最复杂的 SF/SS/FS/FF 校验
      if (!this.checkTaskRelation(preTaskIds, predecessorType, taskRowsItem)) {
        // this.$message.error('前置任务关系错误');
        return false;
      }
      return true;
    },
    // 甘特图数据处理
    handleGanttData(taskRowsItem) {
      const { plannedStartDate, plannedEndDate, isMilestone, offsetDays, offsetType } = taskRowsItem;
      let taskBarStart = plannedStartDate
        ? dayjs(plannedStartDate).format('YYYY-MM-DD')
        : '';
      let taskBarEnd = plannedEndDate ? dayjs(plannedEndDate).format('YYYY-MM-DD') : '';
      // 如果存在偏移量则进行偏移
      if (offsetDays && offsetType) {
        if (offsetType === OffsetType.lag) {
          taskBarStart = dayjs(taskBarStart).add(offsetDays, 'day').format('YYYY-MM-DD');
          taskBarEnd = dayjs(taskBarEnd).add(offsetDays, 'day').format('YYYY-MM-DD');
        } else {
          taskBarStart = dayjs(taskBarStart).subtract(offsetDays, 'day').format('YYYY-MM-DD');
          taskBarEnd = dayjs(taskBarEnd).subtract(offsetDays, 'day').format('YYYY-MM-DD');
        }
      }
      return {
        taskBarStart,
        taskBarEnd,
        // 任务类型
        type: isMilestone ? VTableGantt.TYPES.TaskType.MILESTONE : VTableGantt.TYPES.TaskType.TASK,
        // 任务标题
        title: taskRowsItem.taskName,
      };
    },
    //获取任务列表
    async getTaskList() {
      const { code, rows = [] } = await getBpmPlanTaskList({
        planId: this.$route.query.planId,
      });
      if (code === 200 && rows?.length) {
        const taskListMap = new Map();
        // TODO: 此处需要处理真实甘特图数据
        let taskList = rows.map(item => {
          let extendedFieldValue = {};
          try {
            if (item.extendedFieldValue) {
              extendedFieldValue = JSON.parse(item.extendedFieldValue);
            }
          } catch (err) {
            console.log('解析extendedFieldValue失败', err);
          }

          const newItem = {
            ...item,
            // 用户字段处理
            ...extendedFieldValue,
            // 甘特图真实数据
            ...this.handleGanttData(item),
          }

          taskListMap.set(item.taskId, newItem)
          return newItem
        });
        this.taskListMap = taskListMap;
        taskList = convertToTreeStructure(taskList);
        // 如果数据少于 20 条则补充空数据
        if (taskList?.length < 20) {
          // 数据取整数
          const emptyIndex = Math.ceil(20 - (taskList?.length || 0));
          for (let i = 0; i < emptyIndex; i++) {
            taskList.push({
              taskId: i,
              taskName: '',
            });
          }
        }
        this.setRecords(taskList);
        // 处理依赖关系
      }
    },
    async handleTreeSubmit(selectedKeys) {
      console.log('handleTreeSubmit', selectedKeys);
      const { planId } = this.$route.query;
      const { taskId } = this.currentEditTask;
      const res = await postBpmPlanTaskImportProjpackage({
        planId: planId,
        rootTaskId: taskId || 0,
        projPackageIds: selectedKeys,
      });
      if (res.code === 200) {
        this.handleTreeCancel();
        this.$message.success('导入施工包成功');
        // 刷新任务列表
        this.getTaskList();
      } else {
        this.$message.error('新增任务失败');
      }
      // this.setRecords(data, true);
    },
    // 新增、编辑任务
    async handleEditSubmit(editedTask) {
      console.log('editedTask', editedTask);

      // 校验依赖关系
      if (!this.checkDependency(editedTask)) {
        return;
      }

      // 处理依赖关系
      this.handleDependency(editedTask);

      const { taskId, ancestors, taskLevel, parentTaskId } = this.currentEditTask;
      let params = {};
      // 将 columnsFields 中的 field 作为 key value 为 '', 放入到 params 中
      this.columnsFields.forEach(item => {
        params[item.field] = editedTask[item.field] || '';
      });

      // 前置任务 id 需要转换为逗号隔开的字符
      if (editedTask.predecessorTaskId?.length) {
        params.predecessorTaskId = editedTask.predecessorTaskId.join(',');
      }
      // 如果任务类型不是里程碑则设置为 0
      if (!params.isMilestone) {
        params.isMilestone = 0;
      }

      params = {
        ancestors: ancestors || 0,
        taskLevel: taskLevel || 0,
        parentTaskId: parentTaskId || 0,
        planId: this.$route.query.planId,
        ...params,
      };
      // 将 params 中和 userFields 中相同的字段进行剔除
      Object.keys(this.userFields).forEach(key => {
        delete params[key];
      });
      // 将editedTask 中和 userFields 中相同的字段放入 params.extendedFieldValue 中以JSON格式保存, 如果 extendedFieldValue 不存在则创建
      let extendedFieldValue = {};
      Object.keys(this.userFields).forEach(key => {
        extendedFieldValue[key] = editedTask[key];
      });
      if (!!Object.keys(extendedFieldValue).length) {
        params.extendedFieldValue = JSON.stringify(extendedFieldValue);
      } else {
        params.extendedFieldValue = '';
      }

      let url = postBpmPlanTask;
      let messageText = '新增';
      if (taskId) {
        params.taskId = taskId;
        url = putBpmPlanTask;
        messageText = '编辑';
      }
      console.log('params', params);
      const res = await url(params);
      if (res.code === 200) {
        this.$message.success(`${messageText}任务成功`);
        this.getTaskList();
        this.editDialogVisible = false;
      } else {
        this.$message.error(`${messageText}任务失败`);
      }

      return;
      const formData = {
        ...this.currentEditTask,
        ...editedTask,
      };
      const dependency = this.handleDependency(formData);
      this.updateRecords({
        ...formData,
        ...this.handleTaskBarTime(formData),
        ...this.handleMilestone(formData),
        dependency,
      });

      // 添加依赖关系
      if (dependency) {
        this.addLink(dependency);
      }

      console.log('handleEditSubmit', editedTask, {
        ...formData,
        ...this.handleTaskBarTime(formData),
        ...this.handleDependency(formData),
        ...this.handleMilestone(formData),
      });

      this.$message.success('编辑成功');
    },
    // 删除任务
    async deleteTask(task) {
      console.log('deleteTask', task);
      const { taskId } = task;

      this.$modal
        .confirm('是否确认删除表编号为"' + taskId + '"的数据项？')
        .then(async function () {
          // eslint-disable-next-line no-undef
          return await deleteBpmPlanTaskTaskId(taskId);
        })
        .then(() => {
          this.getTaskList();
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
          // this.$modal.msgError('删除失败，请稍后再试');
        });
    },
    // 处理里程碑
    handleMilestone(formData) {
      const { isMilestone } = formData;
      if (!isMilestone) return null;
      return {
        type: 'milestone',
      };
    },
    // 处理任务条时间
    handleTaskBarTime(formData) {
      const { start, end, offsetValue, offsetType } = formData;
      if (!offsetValue || !offsetType) {
        return {
          taskBarStart: start,
          taskBarEnd: end,
        };
      }

      // 使用 dayjs 处理时间
      const startDate = dayjs(start);
      const endDate = dayjs(end);

      // 根据偏移类型计算时间
      const timeOffset = offsetType === OffsetType.lead ? -offsetValue : offsetValue;

      return {
        taskBarStart: startDate.add(timeOffset, 'day').format('YYYY-MM-DD'),
        taskBarEnd: endDate.add(timeOffset, 'day').format('YYYY-MM-DD'),
      };
    },
    /**
     * 校验SF/SS/FS/FF四种关系
     * @param {string} preTaskIdArr - 前置任务 ID 数组
     * @param {string} curtTask - 当前任务
     * @returns {boolean} 校验结果
     */
    checkTaskRelation(preTaskIdArr, predecessorType, curtTask) {
      // 处理当前任务，计算甘特图上真实开始时间和结束时间
      const realCurtTask = this.handleGanttData(curtTask)
      // 至少有一个错误就返回
      const checkStatus = preTaskIdArr.some(preTaskId => !this.validateTaskRelation(predecessorType, preTaskId, realCurtTask))
      // 校验SF/SS/FS/FF四种关系,  如果关系不符合则返回 false
      if (checkStatus) {
        return false;
      }
      return true;
    },
    /**
     * 获取任务关系类型
     * @param {string} predecessorId - 前置任务ID
     * @param {string} successorId - 后续任务ID
     * @returns {string} 关系类型（FS/SS/FF/SF）
     */
    getRelationType(predecessorId, successorId) {
      // 实际应从数据源获取对应关系类型
      // 示例实现，需根据实际数据结构调整
      return 'FS'; // 默认FS关系
    },
    /**
     * 验证任务关系有效性
     * @param {string} type - 关系类型（FS/SS/FF/SF）
     * @param {string} predecessorId - 前置任务ID
     * @param {string} curtTask - 当前任务
     * @returns {boolean} 校验结果
     */
    validateTaskRelation(type, predecessorId, curtTask) {
      switch (type) {
        case VTableGantt.TYPES.DependencyType.FinishToStart: // 完成-开始
          return this.validateFS(predecessorId, curtTask);
        case VTableGantt.TYPES.DependencyType.StartToStart: // 开始-开始
          return this.validateSS(predecessorId, curtTask);
        case VTableGantt.TYPES.DependencyType.FinishToFinish: // 完成-完成
          return this.validateFF(predecessorId, curtTask);
        case VTableGantt.TYPES.DependencyType.StartToFinish: // 开始-完成
          return this.validateSF(predecessorId, curtTask);
        default:
          this.$message.error('不支持的任务关系类型');
          return false;
      }
    },
    /**
     * FS(完成-开始)验证
     * @param {string} predecessorId - 前置任务ID
     * @param {object} curtTask - 当前任务
     * @returns {boolean} 校验结果
     */
    validateFS(predecessorId, curtTask) {
      const preTask = this.getTasksById(predecessorId);
      if (dayjs(preTask.taskBarEnd).valueOf() > dayjs(curtTask.taskBarStart).valueOf()) {
        this.$message.error(`前置任务【${preTask.taskName}】必须在后续任务开始前完成`);
        return false;
      }
      return true;
    },
    /**
     * SS(开始-开始)验证
     * @param {string} predecessorId - 前置任务ID
     * @param {object} curtTask - 后续任务
     * @returns {boolean} 校验结果
     */
    validateSS(predecessorId, curtTask) {
      const preTask = this.getTasksById(predecessorId);
      if (dayjs(preTask.taskBarStart).valueOf() > dayjs(curtTask.taskBarStart).valueOf()) {
        this.$message.error(`前置任务【${preTask.taskName}】必须与后续任务同时或更早开始`);
        return false;
      }
      return true;
    },
    /**
     * FF(完成-完成)验证
     * @param {string} predecessorId - 前置任务ID
     * @param {object} curtTask - 后续任务
     * @returns {boolean} 校验结果
     */
    validateFF(predecessorId, curtTask) {
      const preTask = this.getTasksById(predecessorId);
      if (dayjs(preTask.taskBarEnd).valueOf() > dayjs(curtTask.taskBarEnd).valueOf()) {
        this.$message.error(`前置任务【${preTask.taskName}】必须与后续任务同时或更早完成`);
        return false;
      }
      return true;
    },
    /**
     * SF(开始-完成)验证
     * @param {string} predecessorId - 前置任务ID
     * @param {object} curtTask - 后续任务
     * @returns {boolean} 校验结果
     */
    validateSF(predecessorId, curtTask) {
      const preTask = this.getTasksById(predecessorId);
      if (dayjs(preTask.taskBarStart).valueOf() > dayjs(curtTask.taskBarEnd).valueOf()) {
        this.$message.error(`前置任务【${preTask.taskName}】开始时间必须在后续任务完成时间之前`);
        return false;
      }
      return true;
    },
    /**
     * 获取前置任务对象
     * @param {Array<string>} preTaskId - 前置任务 id
     * @returns {Array<Object>} 前置任务对象
     */
    getTasksById(preTaskId) {
      return this.taskListMap.get(preTaskId) || {}
    }
  },
};
</script>
<style scoped lang="scss">
.gantt-container {
  width: 100%;
  height: 90%;

  .gantt-layout {
    width: 100%;
    height: calc(100% - 60px);
    position: relative;
  }
}
</style>
