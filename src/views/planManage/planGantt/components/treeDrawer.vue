<template>
  <div class="tree-drawer">
    <el-select
      v-model="selectProjectId"
      filterable
      placeholder="请选择项目"
      style="width: 240px; margin-bottom: 10px"
    >
      <el-option
        v-for="item in projectOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
    <el-tree
      ref="projectTree"
      v-if="treeData.length !== 0"
      show-checkbox
      default-expand-all
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      :check-strictly="false"
      highlight-current
      :expand-on-click-node="false"
      :load="loadNode"
      lazy
      @node-click="handleNodeClick"
    >
    </el-tree>

    <div class="tree-drawer-footer">
      <el-button type="primary" @click="submitForm">确定</el-button>
      <el-button @click="cancelForm">取消</el-button>
    </div>
  </div>
</template>
<script>
import { getBpmEnergyList } from '@/api/v2/energy/proj-new-energy-controller';
import { listPackage } from '@/api/v2/energy/proj-package';
export default {
  name: 'TreeDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      projectOptions: [],
      selectProjectId: '',
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
    };
  },
  created() {},
  mounted() {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.getProjectList();
          this.$nextTick(() => {
            this.$refs.projectTree?.setCheckedKeys(this.defaultCheckedKeys);
          });
        } else {
          this.$refs.projectTree?.setCheckedKeys([]);
        }
      },
      immediate: true,
    },
  },
  computed: {},
  methods: {
    cancelForm() {
      this.$emit('cancel');
    },
    submitForm() {
      // const checkedNodes = this.$refs.projectTree.getCheckedNodes();
      // const halfCheckedNodes = this.$refs.projectTree.getHalfCheckedNodes();
      // const allSelectedNodes = [...checkedNodes, ...halfCheckedNodes];
      // const treeData = this.buildTree(allSelectedNodes);
      // console.log('treeData', treeData);
      const checkedKeys = this.$refs.projectTree.getCheckedKeys();
      console.log('checkedKeys', checkedKeys);
      this.$emit('submit', checkedKeys);
    },
    async getProjectList() {
      const { rows = [] } = await getBpmEnergyList();
      this.projectOptions = rows.map(item => {
        item.value = item.projectId;
        item.label = item.projectName;
        return item;
      });
      this.selectProjectId = this.projectOptions[0].value;
      this.getPackageList();
    },
    async getPackageList(params = {}) {
      const { rows = [] } = await listPackage({
        parentId: 0,
        projectId: this.selectProjectId,
        ...params,
      });
      let data = rows.map(item => {
        const { projId, projName, projType, projTypeCode, children, parentId } = item;
        return {
          isLeaf: true,
          children: [],
          parentId,
          projId,
          id: projId,
          label: projName,
          title: projName,
          name: projName,
          projType,
          projTypeCode,
          start: '',
          end: '',
          progress: 0,
          priority: 'P0',
          developer: '',
        };
      });
      if (Object.keys(params).length === 0) {
        this.treeData = data;
      } else {
        return new Promise(resolve => {
          resolve(data);
        });
      }
    },
    async loadNode(node, resolve) {
      this.node = node;
      this.resolve = resolve;
      this.node.childNodes = [];
      console.log('node :>> 触发', node);
      if (node.level === 0) {
        return resolve(this.treeData);
      }
      if (node.level >= 1) {
        console.log('first', node, node.level, node.data.projId);
        const data = await this.getPackageList({
          parentId: node.level === 0 ? 0 : node.data.projId,
        });
        return resolve(data);
      }

      resolve([]);
    },
    handleNodeClick(data) {
      this.$emit('onClick', data);
    },
    buildTree(nodes) {
      const nodeIds = new Set(nodes.map(node => node.id));

      const topNodes = nodes.filter(node => {
        if (!node.parentId) return true;
        return !nodeIds.has(node.parentId);
      });

      const buildChildren = parentNode => {
        const children = nodes.filter(node => node.parentId === parentNode.id);
        if (children.length) {
          parentNode.children = children.map(child => {
            const childNode = { ...child };
            buildChildren(childNode);
            return childNode;
          });
        }
        return parentNode;
      };

      return topNodes.map(node => buildChildren({ ...node }));
    },
  },
};
</script>
<style scoped lang="scss">
.tree-drawer {
  height: 100%;
  padding: 0 24px;
  position: relative;
  .tree-drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
