import { OffsetTypeOption, DependencyTypeOption } from '../../constants';

const fields = {
 title: {
    label: '任务名称',
    prop: 'title'
  },
  start: {
    label: '开始时间',
    prop: 'start'
  },
  end: {
    label: '结束时间',
    prop: 'end'
  },
  status: {
    label: '任务状态',
    prop: 'status'
  },
  duration: {
    label: '工期',
    prop: 'duration'
  },
  actualStart: {
    label: '实际开始时间',
    prop: 'actualStart'
  },
  actualEnd: {
    label: '实际结束时间',
    prop: 'actualEnd'
  },
  actualDuration: {
    label: '实际工期',
    prop: 'actualDuration'
  },
  dutyPerson: {
    label: '责任人',
    prop: 'dutyPerson'
  },
  progress: {
    label: '进度',
    prop: 'progress'
  },
  description: {
    label: '任务内容',
    prop: 'description'
  },
  preTask: {
    label: '前置任务',
    prop: 'preTask'
  },
  preTaskType: {
    label: '前置类型',
    prop: 'preTaskType'
  },
  offsetType: {
    label: '偏移类型',
    prop: 'offsetType'
  },
  offsetValue: {
    label: '偏移量（天）',
    prop: 'offsetValue'
  },
  isMilestone: {
    label: '设为里程碑',
    prop: 'isMilestone'
  }
}
export const DialogJson = {
  type: 'Edit',
  title: '任务',
  options: {
    width: '800px',
    appendToBody: true,
    destroyOnClose: false,
    closeOnClickModal: false
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary'
      }
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: ''
      }
    }
  ],
  formJson: {
    formOption: {
      labelPosition: 'right',
      inline: false,
      labelWidth: '100px',
      size: 'small',
      formFlex: {
        gutter: 20,
        span: 12
      }
    },
    formItemJson: [
      {
        label: '任务信息',
        showTitle: true,
        children: [
          {
            label: fields.title.label,
            prop: fields.title.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入任务名称',
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: `请输入${fields.title.label}`
            }
          },
          {
            label: fields.status.label,
            prop: fields.status.prop,
            type: 'Input',
            options: {
              clearable: true,
              placeholder: `请输入${fields.status.label}`
            }
          },
          {
            label: fields.start.label,
            prop: fields.start.prop,
            type: 'DatePicker',
            options: {
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              placeholder: `请选择${fields.start.label}`,
              clearable: true
            }
          },
          {
            label: fields.end.label,
            prop: fields.end.prop,
            type: 'DatePicker',
            options: {
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              placeholder: `请选择${fields.end.label}`,
              clearable: true
            }
          },
          {
            label: fields.duration.label,
            prop: fields.duration.prop,
            type: 'Input',
            options: {
              clearable: true,
              placeholder: `请输入${fields.duration.label}`
            }
          },
          {
            label: fields.progress.label,
            prop: fields.progress.prop,
            type: 'Input',
            options: {
              min: 0,
              max: 100,
              clearable: true,
              placeholder: `请输入${fields.progress.label}`
            }
          },
          {
            label: fields.actualStart.label,
            prop: fields.actualStart.prop,
            type: 'DatePicker',
            options: {
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              placeholder: `请选择${fields.actualStart.label}`,
              clearable: true
            }
          },
          {
            label: fields.actualEnd.label,
            prop: fields.actualEnd.prop,
            type: 'DatePicker',
            options: {
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              placeholder: `请选择${fields.actualEnd.label}`,
              clearable: true
            }
          },
          {
            label: fields.actualDuration.label,
            prop: fields.actualDuration.prop,
            type: 'Input',
            options: {
              clearable: true,
              placeholder: `请输入${fields.actualDuration.label}`
            }
          },
          {
            label: fields.dutyPerson.label,
            prop: fields.dutyPerson.prop,
            type: 'Input',
            options: {
              clearable: true,
              placeholder: `请输入${fields.dutyPerson.label}`
            }
          },
          {
            label: fields.description.label,
            prop: fields.description.prop,
            type: 'Input',
            options: {
              type: 'textarea',
              clearable: true,
              placeholder: `请输入${fields.description.label}`
            }
          }
        ]
      },
      {
        label: '高级信息',
        showTitle: true,
        children: [
          {
            label: fields.preTask.label,
            prop: fields.preTask.prop,
            type: 'Input',
            options: {
              clearable: true,
              placeholder: `请输入${fields.preTask.label}`
            }
          },
          {
            label: fields.preTaskType.label,
            prop: fields.preTaskType.prop,
            type: 'Select',
            options: {
              clearable: true,
              placeholder: `请输入${fields.preTaskType.label}`
            },
            option: DependencyTypeOption
          },
          {
            label: fields.offsetType.label,
            prop: fields.offsetType.prop,
            type: 'Select',
            options: {
              clearable: true,
              placeholder: `请输入${fields.offsetType.label}`
            },
            option: OffsetTypeOption
          },
          {
            label: fields.offsetValue.label,
            prop: fields.offsetValue.prop,
            type: 'Input',
            options: {
              clearable: true,
              placeholder: `请输入${fields.offsetValue.label}`
            }
          },
          {
            label: fields.isMilestone.label,
            prop: fields.isMilestone.prop,
            type: 'Switch',
            options: {
              activeValue: '1',
              inactiveValue: '0'
            }
          }
        ]
      }
    ]
  }
}
