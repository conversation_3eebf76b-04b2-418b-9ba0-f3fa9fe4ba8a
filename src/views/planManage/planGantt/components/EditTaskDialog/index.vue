<template>
  <CoustomFormDialog
    ref="formDialog"
    :dialog-json="dialogJson"
    :init-data="form"
    :visible.sync="dialogVisible"
    @onSubmit="handleSubmit"
    @onDialogClose="handleClose"
  />
</template>

<script>
import CoustomFormDialog from '@/components/CoustomV2/CoustomFormDialog/index.vue';
import { DialogJson } from './constants.js';
export default {
  name: 'EditTaskDialog',
  components: {
    CoustomFormDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    taskData: {
      type: Object,
      default: () => ({}),
    },
  },
  inject: ['getFormJson', 'getRecords'],
  data() {
    return {
      form: {
        title: '',
        id: '',
        status: '',
        start: '',
        end: '',
        duration: '',
        actualStart: '',
        actualEnd: '',
        actualDuration: '',
        dutyPerson: '',
        progress: 0,
      },
      // dialogJson: DialogJson
    };
  },
  watch: {
    visible() {
      if (this.visible) {
        // 编辑时将表格数据赋值给前置任务option
        const formItemJson = this.dialogJson.formJson.formItemJson[0].children;
        // 使用递归函数处理数据
        const option = this.processTaskData(this.records);

        this.dialogJson.formJson.formItemJson[0].children = formItemJson.map((item) => {
          if (item.prop === 'predecessorTaskId') {
            return {
              ...item,
              // 过滤空数据
              option,
            };
          }
          return item;
        });
        this.form = {
          ...this.taskData,
          // 前置任务 id 需要转换为数组
          predecessorTaskId: String(this.taskData.predecessorTaskId)?.split?.(',') || [],
        };
      }
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      },
    },
    records() {
      return this.getRecords();
    },
    // dialogJson 可以修改
    dialogJson: {
      get() {
        return this.getFormJson() || {};
      },
      set(value) {
        this.dialogJson = value;
      },
    },
  },
  methods: {
    // 递归处理任务数据，过滤并映射需要的字段
    processTaskData(tasks) {
      if (!Array.isArray(tasks)) return [];

      return tasks
        .filter((item) => !!item.taskName)
        .map((item) => ({
          taskId: item.taskId,
          taskName: item.taskName,
          children: this.processTaskData(item.children) // 递归处理children
        }));
    },

    handleClose() {
      this.dialogVisible = false;
    },
    handleSubmit(formData) {
      this.$refs.formDialog.resetLoading();
      this.$emit('submit', formData);
      // this.handleClose();
    },
  },
};
</script>
