<template>
  <div class="plan-container">
    <div class="gantt-toolbar">
      <div>
        <el-form :inline="true" :model="formData" class="demo-form-inline">
          <el-form-item label="任务布局模式">
            <el-select v-model="formData.tasksMode" placeholder="任务布局模式" @change="handleTasksModeChange">
              <el-option
                v-for="item in tasksModeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间单位">
            <el-radio-group v-model="formData.timeUnit" @change="handleTimeUnitChange">
              <el-radio-button v-for="item in timeUnits" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <el-button type="primary" @click="save">保存</el-button>
    </div>
    <v-gantt ref="gantt" />
  </div>
</template>

<script>
import vGantt from './components/vGantt.vue';
import * as VTableGantt from '@visactor/vtable-gantt';
import { getBpmPlanTemplateFieldList } from '@/api/v2/plan/plan-template-field-controller';
import { getBpmPlanInstPlanId, putBpmPlanInst } from '@/api/v2/plan/plan-inst-controller';
import { convertToFormConfig } from './utils';
import { GanttEditFormFields, GanttListFields, taskStatusMap, predecessorTypeMap, offsetTypeMap, isMilestoneMap } from './constants';

export default {
  components: {
    vGantt,
  },
  props: {},
  // provide 默认值传递初始值，如果动态数据需要使用，则需要使用函数返回
  provide() {
    return {
      getColumnsFields: () => this.columnsFields,
      getFormJson: () => this.formJson,
      getUserFields: () => this.userFields,
    };
  },
  data() {
    return {
      formData: {
        tasksMode: VTableGantt.TYPES.TasksShowMode.Tasks_Separate,
        timeUnit: 'week',
      },
      tasksModeOptions: [
        { label: '任务独立', value: VTableGantt.TYPES.TasksShowMode.Tasks_Separate },
        { label: '子任务内联', value: VTableGantt.TYPES.TasksShowMode.Sub_Tasks_Inline },
        { label: '子任务独立', value: VTableGantt.TYPES.TasksShowMode.Sub_Tasks_Separate },
        { label: '子任务排列', value: VTableGantt.TYPES.TasksShowMode.Sub_Tasks_Arrange },
        { label: '子任务紧凑', value: VTableGantt.TYPES.TasksShowMode.Sub_Tasks_Compact },
        {
          label: '项目子任务内联',
          value: VTableGantt.TYPES.TasksShowMode.Project_Sub_Tasks_Inline,
        },
      ],
      timeUnits: [
        { label: '日', value: 'day' },
        { label: '周', value: 'week' },
        { label: '月', value: 'month' },
        { label: '季', value: 'quarter' },
        { label: '年', value: 'year' }
      ],
      columnsFields: [],
      formJson: {},
      userFields: {},
      // 计划详情
      planDetail: {},
    };
  },
  beforeDestroy() {},
  created() {
    this.init();
  },
  methods: {
    async init() {
      const { templateId, planId } = this.$route.query || {};
      // 获取模版字段
      await this.getTemplateField(templateId);
      // 获取计划详情
      await this.getPlanDetail(planId);
      // 初始化甘特图
      this.$nextTick(() => {
        this.$refs.gantt.init(this.planDetail);
      });
    },
    // 获取计划详情
    async getPlanDetail(planId) {
      const res = await getBpmPlanInstPlanId(planId);
      console.log('res', res);
      if(res.code === 200) {
        this.planDetail = res.data || {};
      }
    },
    // 获取模版字段
    async getTemplateField(templateId) {
      if (templateId) {
        const res = await getBpmPlanTemplateFieldList({
          templateId,
          pageNum: 1,
          pageSize: 9999,
        });
        const rows = res.rows || [];
        // 根据读写权限和displayOrder 字段进行排序，数字越小越靠前；
        const allfields = rows.filter((item) => item.rwFlag === 'READ_WRITE').sort((a, b) => a.displayOrder - b.displayOrder).map((item) => ({
          ...item,
          fieldName: this.underlineToCamel(item.fieldName),
        }));
        // 根据显示状态 displayStatus (0-禁用,1-启用)，将 displayStatus = 1  单独拆出一个数组，作为表头展示
        this.columnsFields = allfields.filter((item) => item.displayStatus === 1).map((item, index) => ({
          field: item.fieldName,
          title: item.fieldLabel,
          width: 'auto',
          minWidth: 80,
          tree: index === 0,
          fieldFormat: (record) => {
           const value = record[item.fieldName]
           if(item.fieldName === 'taskStatus') {
            return taskStatusMap[value]
           }
           if(item.fieldName === 'predecessorType') {
            return predecessorTypeMap[value]
           }
           if(item.fieldName === 'offsetType') {
            return offsetTypeMap[value]
           }
           if(item.fieldName === 'isMilestone') {
            return isMilestoneMap[value]
           }
           return value
          },
        }));


        // 将 allfields 转换为表单渲染配置
        this.formJson = convertToFormConfig(allfields);

        // 将字段拥有者为 user 的单独拆出为 map 格式，保存数据时单独存储
        const userFields = {};
        allfields.filter((item) => item.fieldOwner === 'user').forEach((item) => {
          userFields[item.fieldName] = null;
        });
        this.userFields = userFields;
      }
    },
    async save() {
      console.log('save', this.$refs.gantt.ganttInstance);
      // 获取 ganttInstance 的配置
      const ganttInstanceRef = this.$refs.gantt.ganttInstance;
      const links = ganttInstanceRef?.options?.dependency?.links || [];
      const ganttConfig = {
        // 依赖关系
        dependencyLinks: links.map((item) => ({
          linkedFromTaskKey: item.linkedFromTaskKey,
          linkedToTaskKey: item.linkedToTaskKey,
          type: item.type,
        })),
      };
      console.log('ganttConfig', ganttConfig);
      // return;
      const { planName, projectId, templateId, planId } = this.planDetail;
      // 更新计划主
      const res = await putBpmPlanInst({
        planId,
        planName,
        projectId,
        templateId,
        ganttConfig: JSON.stringify(ganttConfig),
      });
      if(res.code === 200) {
        this.$message.success('保存成功');
      } else {
        this.$message.error('保存失败');
      }
    },
    handleTasksModeChange(value) {
      this.$refs.gantt.setTasksMode(value);
    },
    handleTimeUnitChange(value) {
      this.$refs.gantt.changeTimeUnit(value);
    },
    // 下划线转驼峰
    underlineToCamel(str) {
      return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    },
  },
};
</script>
<style scoped lang="scss">
.plan-container {
  width: 100%;
  height: 100vh;
  padding: 24px;
}
.gantt-toolbar {
  margin-bottom: 14px;
  display: flex;
  justify-content: space-between;

  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}
</style>
