export const ganttData = {
  "data": [
    {
      "actual_end_date": "2025-04-23",
      "actual_start_date": "2025-04-15",
      "duration": 10,
      "end_date": "2025-05-01",
      "id": "2|jvUiek",
      "parent": "0",
      "progress": 0.40,
      "start_date": "2025-04-21",
      "text": "2",
      "time": -8,
      "responsible_person": "1|管理员",
      "milestone_node": null
    },
    {
      "actual_end_date": "2025-04-24",
      "actual_start_date": "2025-04-21",
      "duration": 1,
      "end_date": "2025-04-22",
      "id": "1|rMyrYE",
      "parent": "0",
      "progress": 0,
      "start_date": "2025-04-21",
      "text": "1",
      "time": 2,
      "responsible_person": "1|管理员",
      "milestone_node": null
    },
    {
      "actual_end_date": null,
      "actual_start_date": null,
      "duration": 2,
      "end_date": "2025-04-29",
      "id": "3",
      "parent": "1",
      "progress": 0,
      "start_date": "2025-04-27",
      "text": "节点1",
      "time": 9,
      "responsible_person": "1|管理员",
      "milestone_node": "1"
    },
    {
      "actual_end_date": null,
      "actual_start_date": null,
      "duration": 33,
      "end_date": "2025-05-30",
      "id": "4",
      "parent": "1",
      "progress": 0,
      "start_date": "2025-04-27",
      "text": "节点2",
      "time": -22,
      "responsible_person": "1|管理员",
      "milestone_node": "0"
    },
    {
      "actual_end_date": null,
      "actual_start_date": null,
      "duration": 1,
      "end_date": "2025-05-02",
      "id": "2",
      "parent": "1|rMyrYE",
      "progress": 0,
      "start_date": "2025-05-01",
      "text": "节点1",
      "time": 6,
      "responsible_person": "1|管理员",
      "milestone_node": "1"
    },
    {
      "actual_end_date": null,
      "actual_start_date": "2025-05-06",
      "duration": 7,
      "end_date": "2025-05-02",
      "id": "1",
      "parent": "1|rMyrYE",
      "progress": 0.11,
      "start_date": "2025-04-25",
      "text": "节点2444",
      "time": 6,
      "responsible_person": "1|管理员",
      "milestone_node": "0"
    },
  ],
  // 添加任务模板
  taskTemplate: {
    id: '',
    text: '',
    start_date: '',
    end_date: '',
    duration: 1,
    progress: 0,
    parent: '0',
    responsible_person: '',
    milestone_node: '0',
    actual_start_date: null,
    actual_end_date: null,
    time: 0
  }
}

