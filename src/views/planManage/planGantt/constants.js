
import { TYPES } from '@visactor/vtable-gantt';

export const  OffsetType = {
  lead: 'lead',
  lag: 'lag'
}

export const OffsetTypeOption = [
  {
    label: '提前',
    value: OffsetType.lead
  },
  {
    label: '后置',
    value: OffsetType.lag
  }
];

export const DependencyTypeOption = [
  {
    label: 'SS',
    value: TYPES.DependencyType.StartToStart
  },
  {
    label: 'SF',
    value: TYPES.DependencyType.StartToFinish
  },
  {
    label: 'FS',
    value: TYPES.DependencyType.FinishToStart
  },
  {
    label: 'FF',
    value: TYPES.DependencyType.FinishToFinish
  }
]

// fieldType 字段映射为前端类型
export const FieldTypeMap = {
  varchar: 'Input',
  int: 'Input',
  decimal: 'Input',
  text: 'Input',
  datetime: 'DatePicker', // 日期时间
  date: 'DatePicker', // 日期
  bigint: 'Input', // 长整型
  boolean: 'Switch', // 布尔型
  enum: 'Select', // 枚举型
  varchar: 'Select', // 枚举型
  tinyint: 'Select', // 枚举型
}

// 默认甘特图表单配置
export const DefaultFormConfig = {
  type: 'Edit',
  title: '任务',
  options: {
    width: '800px',
    appendToBody: true,
    destroyOnClose: false,
    closeOnClickModal: false
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary'
      }
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: ''
      }
    }
  ],
  formJson: {
    formOption: {
      labelPosition: 'right',
      inline: false,
      labelWidth: '120px',
      size: 'small',
      formFlex: {
        gutter: 20,
        span: 12
      }
    },
    formItemJson: [
      {
        label: '任务信息',
        showTitle: true,
        children: []
      }
    ]
  }
}




// 默认甘特图数据
export const defaultRecord = [{
  isRoot: true,
  id: '',
  title: '',
  developer: '',
  start: '',
}]

// 甘特图表格列配置
export const columns = [
  {
    field: 'title',
    title: '任务名称',
    width: 'auto',
    minWidth: 140,
    editor: 'input',
    tree: true,
  },
  {
    field: 'id',
    title: '任务编码',
    width: 'auto',
    minWidth: 50,
  },
  {
    field: 'status',
    title: '任务状态',
    width: 'auto',
    minWidth: 50,
  },
  {
    field: 'start',
    title: '开始时间',
    width: '120px',
    editor: 'date-input',
  },
  {
    field: 'end',
    title: '结束时间',
    width: '120px',
    editor: 'date-input',
  },
  {
    field: 'duration',
    title: '工期',
    width: 'auto',
    editor: 'input',
  },
  {
    field: 'actualStart',
    title: '实际开始时间',
    width: '120px',
    editor: 'date-input',
  },
  {
    field: 'actualEnd',
    title: '实际结束时间',
    width: '120px',
    editor: 'date-input',
  },
  {
    field: 'actualDuration',
    title: '实际工期',
    width: 'auto',
    editor: 'input',
  },
  {
    field: 'dutyPerson',
    title: '责任人',
    width: 'auto',
    editor: 'input',
  },
  {
    field: 'progress',
    title: '进度',
    width: 'auto',
    headerStyle: {
      borderColor: '#e1e4e8',
    },
    style: {
      borderColor: '#e1e4e8',
      color: 'green',
    },
    editor: 'input',
  },
];

// 测试甘特图数据
export const records = [
  {
      "isLeaf": true,
      "children": [
          {
              "isLeaf": true,
              "children": [
                  {
                      "isLeaf": true,
                      "children": [],
                      "parentId": 3,
                      "projId": 9,
                      "id": 9,
                      "label": "qwe",
                      "title": "qwe",
                      "name": "qwe",
                      "start": "",
                      "end": "",
                      "progress": 0,
                      "priority": "P0",
                      "developer": "",
                      "hierarchyState": "collapse"
                  }
              ],
              "parentId": 1,
              "projId": 3,
              "id": 3,
              "label": "子单位工程哦",
              "title": "子单位工程哦2334",
              "name": "子单位工程哦",
              "start": "",
              "end": "",
              "progress": 0,
              "priority": "P0",
              "developer": "",
              "vtable_gantt_linkedTo": [
                  {
                      "type": "start_to_finish",
                      "linkedFromTaskKey": 2,
                      "linkedToTaskKey": 3
                  },
                  {
                      "type": "start_to_finish",
                      "linkedFromTaskKey": 2,
                      "linkedToTaskKey": 3
                  },
                  {
                      "type": "start_to_finish",
                      "linkedFromTaskKey": 2,
                      "linkedToTaskKey": 3
                  },
                  {
                      "type": "start_to_finish",
                      "linkedFromTaskKey": 2,
                      "linkedToTaskKey": 3
                  },
                  {
                      "type": "start_to_finish",
                      "linkedFromTaskKey": 2,
                      "linkedToTaskKey": 3
                  },
                  {
                      "type": "start_to_finish",
                      "linkedFromTaskKey": 2,
                      "linkedToTaskKey": 3
                  },
                  {
                      "type": "start_to_finish",
                      "linkedFromTaskKey": 2,
                      "linkedToTaskKey": 3
                  },
                  {
                      "type": "start_to_finish",
                      "linkedFromTaskKey": 2,
                      "linkedToTaskKey": 3
                  },
                  {
                      "type": "start_to_finish",
                      "linkedFromTaskKey": 2,
                      "linkedToTaskKey": 3
                  }
              ],
              "vtable_gantt_linkedFrom": [
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  },
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  },
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  },
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  },
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  },
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  },
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  },
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  },
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  }
              ],
              "hierarchyState": "collapse"
          },
          {
              "isLeaf": true,
              "children": [],
              "parentId": 1,
              "projId": 4,
              "id": 4,
              "label": "123123",
              "title": "123123234",
              "name": "123123",
              "start": "2025-06-10",
              "end": "2025-06-25",
              "progress": 0,
              "priority": "P0",
              "developer": "",
              "hierarchyState": "collapse",
              "taskBarStart": "2025-06-10",
              "taskBarEnd": "2025-06-25",
              "vtable_gantt_linkedTo": [
                  {
                      "type": "start_to_start",
                      "linkedFromTaskKey": 3,
                      "linkedToTaskKey": 4
                  }
              ]
          },
          {
              "isLeaf": true,
              "children": [],
              "parentId": 1,
              "projId": 6,
              "id": 6,
              "label": "qwe",
              "title": "qwe",
              "name": "qwe",
              "start": "",
              "end": "",
              "progress": 0,
              "priority": "P0",
              "developer": "",
              "hierarchyState": "collapse"
          },
          {
              "isLeaf": true,
              "children": [],
              "parentId": 1,
              "projId": 13,
              "id": 13,
              "label": "12",
              "title": "12",
              "name": "12",
              "start": "",
              "end": "",
              "progress": 0,
              "priority": "P0",
              "developer": "",
              "hierarchyState": "collapse"
          }
      ],
      "parentId": 0,
      "projId": 1,
      "id": 1,
      "label": "基坑集水井-qwe",
      "title": "基坑集水井-qwe",
      "name": "基坑集水井-qwe",
      "start": "",
      "end": "",
      "progress": 0,
      "priority": "P0",
      "developer": "",
      "hierarchyState": "expand",
      "vtable_gantt_linkedFrom": [
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          },
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          },
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          },
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          },
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          },
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          },
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          },
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          },
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          }
      ]
  },
  {
      "isLeaf": true,
      "children": [],
      "parentId": 0,
      "projId": 2,
      "id": 2,
      "label": "基坑集水井",
      "title": "基坑集水井",
      "name": "基坑集水井",
      "start": "",
      "end": "",
      "progress": 0,
      "priority": "P0",
      "developer": "",
      "vtable_gantt_linkedTo": [
          {
              "type": "finish_to_start",
              "linkedFromTaskKey": 1,
              "linkedToTaskKey": 2
          }
      ],
      "vtable_gantt_linkedFrom": [
          {
              "type": "start_to_finish",
              "linkedFromTaskKey": 2,
              "linkedToTaskKey": 3
          }
      ]
  },
  {
      "isLeaf": true,
      "children": [],
      "parentId": 0,
      "projId": 8,
      "id": 8,
      "label": "升压-1",
      "title": "升压-1",
      "name": "升压-1",
      "start": "",
      "end": "",
      "progress": 0,
      "priority": "P0",
      "developer": ""
  },
  {
      "isLeaf": true,
      "children": [],
      "parentId": 0,
      "projId": 12,
      "id": 12,
      "label": "升压",
      "title": "升压",
      "name": "升压",
      "start": "",
      "end": "",
      "progress": 0,
      "priority": "P0",
      "developer": ""
  }
]


// 甘特图编辑列表不展示字段
export const GanttListFields = [
  'taskId', // 任务ID
  'serialNo', // 序号
  'taskCode', // 任务编码
  'parentTaskId', // 父任务ID
  'relatedPlanId', // 关联计划ID
  'relatedTaskId', // 关联任务ID
  'taskLevel', // 任务层级
];


// 甘特图编辑表单不展示字段
export const GanttEditFormFields = [
  'taskId', // 任务ID
  'serialNo', // 序号
  'taskCode', // 任务编码
  'parentTaskId', // 父任务ID
  'relatedPlanId', // 关联计划ID
  'relatedTaskId', // 关联任务ID
  'taskLevel', // 任务层级
];

export const taskStatusMap = {
  0: '未开始',
  1: '进行中',
  2: '已完成'
}

export const predecessorTypeMap = {
  [TYPES.DependencyType.StartToStart]: 'SS',
  [TYPES.DependencyType.StartToFinish]: 'SF',
  [TYPES.DependencyType.FinishToStart]: 'FS',
  [TYPES.DependencyType.FinishToFinish]: 'FF'
}

export const offsetTypeMap = {
  'lead': '提前',
  'lag': '后置'
}

export const isMilestoneMap = {
  '1': '是',
  '0': '否'
}

// 甘特图编辑表单字段下来选择对应选项
export const GanttEditFormOptions = {
  taskStatus: [
    {
      label: '未开始',
      value: '0'
    },
    {
      label: '进行中',
      value: '1'
    },
    {
      label: '已完成',
      value: '2'
    }
  ],
  // 前置任务类型
  predecessorType: DependencyTypeOption,
  // 偏移类型
  offsetType: OffsetTypeOption,
  // 是否是里程碑
  isMilestone: [
    {
      label: '是',
      value: 1
    },
    {
      label: '否',
      value: 0
    }
  ]
}
