/**
 * 由Java类 PlanTemplateField 自动转换的字段定义
 */
export const fields = {
  fieldId: {
    label: '字段id',
    prop: 'fieldId'
  },
  templateId: {
    label: '模板id',
    prop: 'templateId'
  },
  fieldName: {
    label: '字段英文名称',
    prop: 'fieldName'
  },
  fieldLabel: {
    label: '字段中文名称',
    prop: 'fieldLabel'
  },
  displayOrder: {
    label: '显示顺序',
    prop: 'displayOrder'
  },
  fieldDesc: {
    label: '字段描述',
    prop: 'fieldDesc'
  },
  fieldOwner: {
    label: '字段拥有者',
    prop: 'fieldOwner'
  },
  rwFlag: {
    label: '读写标识',
    prop: 'rwFlag'
  },
  delFlag: {
    label: '删除标识',
    prop: 'delFlag'
  },
  fieldType: {
    label: '字段类型',
    prop: 'fieldType'
  },
  displayStatus: {
    label: '显示状态',
    prop: 'displayStatus'
  },
};

const fieldTypeOptions = [
  {
    label: 'varchar',
    value: 'varchar'
  },
  {
    label: 'text',
    value: 'text'
  },
  {
    label: 'int',
    value: 'int'
  },
  {
    label: 'date',
    value: 'date'
  },
  {
    label: 'datetime',
    value: 'datetime'
  },
  {
    label: 'bigint',
    value: 'bigint'
  }
]

const fieldOwnerOptions = [
  {
    label: '系统',
    value: 'system'
  },
  {
    label: '用户',
    value: 'user'
  }
]

const rwFlagOptions = [
  {
    label: '读写',
    value: 'READ_WRITE'
  },
  {
    label: '只读',
    value: 'READ_ONLY'
  }
]

const displayStatusOptions = [
  {
    label: '显示',
    value: 1
  },
  {
    label: '隐藏',
    value: 0
  }
]



/**
 * 使用示例：
 *
 * // 创建表单项
 * const formItem = {
 *   label: fields.fieldId.label,
 *   prop: fields.fieldId.prop,
 *   type: 'Input'
 * };
 */

/**
 * 由Java类 PlanTemplateField 自动生成的查询表单配置
 */
export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: '120px',
    labelPosition: 'right',
    size: 'small',
    formFlex: {
      gutter: 20,
      span: 8
    }
  },
  formItemJson: [
    {
      label: 'PlanTemplateField信息',
      showTitle: false,
      children: [
        {
          label: fields.fieldName.label,
          prop: fields.fieldName.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.fieldName.label
          }
        },
        {
          label: fields.fieldLabel.label,
          prop: fields.fieldLabel.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.fieldLabel.label
          }
        },
        {
          label: fields.fieldOwner.label,
          prop: fields.fieldOwner.prop,
          type: 'Select',
          options: {
            clearable: true,
            placeholder: '请选择' + fields.fieldOwner.label
          },
          option: fieldOwnerOptions
        }
      ]
    }
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        permi: 'planTemplateField:manage:query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary'
        }
      },
      {
        label: '重置',
        type: 'Reset',
        permi: 'planTemplateField:manage:query',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini'
        }
      }
    ]
  }
};


/**
 * 由Java类 PlanTemplateField 自动生成的页面按钮配置
 */
export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: 'planTemplateField:manage:add',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};


/**
 * 由Java类 PlanTemplateField 自动生成的表格配置
 */
export const TableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号'
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fieldName.label,
        prop: fields.fieldName.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fieldLabel.label,
        prop: fields.fieldLabel.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.displayOrder.label,
        prop: fields.displayOrder.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fieldType.label,
        prop: fields.fieldType.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.rwFlag.label,
        prop: fields.rwFlag.prop,
        optionsMap: {
          READ_WRITE: '读写',
          READ_ONLY: '只读'
        },
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.displayStatus.label,
        prop: fields.displayStatus.prop,
        optionsMap: {
          1: '显示',
          0: '隐藏'
        },
        colorOptions: {
          1: '#67C23A',
          0: '#F56C6C'
        },
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fieldOwner.label,
        prop: fields.fieldOwner.prop,
        optionsMap: {
          system: '系统',
          user: '用户'
        },
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fieldDesc.label,
        prop: fields.fieldDesc.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        width: '120',
        label: '操作',
        type: 'func',
        fixed: 'right',
        buttonList: [
          // {
          //   label: '详情',
          //   type: 'Info',
          //   permi: 'planTemplateField:manage:info',
          //   options: {
          //     icon: 'el-icon-view',
          //     size: 'mini',
          //     type: 'text'
          //   }
          // },
          {
            label: '编辑',
            type: 'Edit',
            permi: 'planTemplateField:manage:edit',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text'
            },
            // 字段拥有者 0-未发布,1-已发布
            hiddenOptions: {
              prop: fields.fieldOwner.prop,
              values: ['user'],
            },
          },
          {
            label: '删除',
            type: 'Delete',
            permi: 'planTemplateField:manage:remove',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text'
            },
            // 字段拥有者 0-未发布,1-已发布
            hiddenOptions: {
              prop: fields.fieldOwner.prop,
              values: ['user'],
            },
          }
        ]
      }
    ]
  }
};


/**
 * 由Java类 PlanTemplateField 自动生成的对话框配置
 */
export const DialogJson = {
  type: 'Add',
  title: '模版字段',
  options: {
    width: '600px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary'
      }
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: ''
      }
    }
  ],
  formJson: {
    formOption: {
      labelPosition: 'right',
      inline: false,
      labelWidth: '120px',
      size: 'small',
      formFlex: {
        gutter: 20,
        span: 24
      }
    },
    defaultFormData: {
      fieldType: 'varchar',
      rwFlag: 'READ_WRITE',
      displayStatus: 1
    },
    formItemJson: [
      {
        label: '基本信息',
        showTitle: false,
        children: [
          {
            label: fields.fieldName.label,
            prop: fields.fieldName.prop,
            type: 'Input',
            rules: [
              { required: true, message: '请输入' + fields.fieldName.label, trigger: 'blur' },
              {
                required: true,
                validator: (rule, value, callback) => {
                  const reg = /^[a-z_]+$/;
                  if (!reg.test(value)) {
                    callback(new Error('请输入仅包含小写字母和下划线(_)的字符串'));
                  }
                  callback();
                },
                trigger: ['blur'],
              },
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.fieldName.label
            }
          },
          {
            label: fields.fieldLabel.label,
            prop: fields.fieldLabel.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.fieldLabel.label,
                trigger: 'change'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.fieldLabel.label
            }
          },
          {
            label: fields.displayOrder.label,
            prop: fields.displayOrder.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.displayOrder.label,
                trigger: 'change'
              }
            ],
            options: {
              type: 'number',
              clearable: true,
              min: 0,
              placeholder: '请输入' + fields.displayOrder.label
            }
          },
          {
            label: fields.fieldType.label,
            prop: fields.fieldType.prop,
            type: 'Radio',
            rules: [
              {
                required: true,
                message: '请选择' + fields.fieldType.label,
                trigger: 'change'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请选择' + fields.fieldType.label
            },
            option: fieldTypeOptions
          },
          {
            label: fields.displayStatus.label,
            prop: fields.displayStatus.prop,
            type: 'Radio',
            rules: [
              {
                required: true,
                message: '请输入' + fields.displayStatus.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.displayStatus.label
            },
            option: displayStatusOptions
          },
          {
            label: fields.rwFlag.label,
            prop: fields.rwFlag.prop,
            type: 'Radio',
            rules: [
              {
                required: true,
                message: '请输入' + fields.rwFlag.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.rwFlag.label
            },
            option: rwFlagOptions
          },
          {
            label: fields.fieldDesc.label,
            prop: fields.fieldDesc.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.fieldDesc.label,
                trigger: 'change'
              }
            ],
            options: {
              type: 'textarea',
              clearable: true,
              placeholder: '请输入' + fields.fieldDesc.label
            }
          },
        ]
      }
    ]
  }
};
