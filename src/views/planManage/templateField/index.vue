<template>
  <div class="app-container">
    <CoustomTable
      ref="projectTable"
      :page-buttons-json="pageButtonsJson"
      :query-form-json="queryFormJson"
      :table-data="tableData"
      :table-json="tableJson"
      :total="total"
      :isDialog="isDialog"
      :paginations="defaultQueryParams"
      @onButton="handlerButton"
      @onPageButton="handlerPageButton"
      @onSearch="handlerSearch"
      @onTableRowClick="handlerTableRowClick"
      @onRowClick="handlerRowClick"
      @onPagination="handlerPagination"
    />

    <CoustomFormDialog
      ref="formDialog"
      :dialog-json="dialogJson"
      :init-data="initFormData"
      :visible.sync="dialogVisible"
      @onSubmit="handlerSubmit"
      @onDialogClose="handlerDialogClose"
    >
    </CoustomFormDialog>

  </div>
</template>
<script>
import CoustomTable from "@/components/CoustomV2/CoustomTable";
import CoustomFormDialog from "@/components/CoustomV2/CoustomFormDialog";
import {
  TableJson,
  QueryFormJson,
  PageButtonsJson,
  DialogJson,
} from "./constants.js";
import {
  getBpmPlanTemplateFieldList,
  postBpmPlanTemplateField,
  getBpmPlanTemplateFieldFieldId,
  deleteBpmPlanTemplateFieldFieldId,
  putBpmPlanTemplateField,
} from '@/api/v2/plan/plan-template-field-controller';
import pageMixin from "@/mixin/page";
export default {
  name: "PlantTemplate",
  props: {
    isDialog: {
      type: Boolean,
      default: false,
    },
    selectList: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    isMultiple: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    CoustomTable,
    CoustomFormDialog,
  },
  mixins: [pageMixin],
  data() {
    return {
      queryFormJson: Object.freeze(QueryFormJson),
      pageButtonsJson: Object.freeze(PageButtonsJson),
      tableJson: Object.freeze(TableJson),
      dialogJson: DialogJson,
    };
  },
  watch: {
    tableData: {
      handler(val) {
        if (!!val.length && this.visible) {
          this.$nextTick(() => {
            this.toggleSelection(this.selectList);
          });
        }
      },
      immediate: true,
    },
  },
  created() {
    this.$set(this.defaultQueryParams, 'templateId', this.$route.query.templateId);
  },
  mounted() {
    this.getList({}, getBpmPlanTemplateFieldList);
  },

  methods: {
    handlerMainDeviceButton(item) {
    },
    handlerMainDevicePageButton(item) {

    },

    clearSelection() {
      const refElement = this.$refs.projectTable.$refs.coustomTable;
      refElement.setCurrentRow(null);
      refElement.clearSelection();
    },
    toggleSelection(selection) {
      if (selection && selection.length > 0) {
        this.$nextTick(() => {
          const refElement = this.$refs.projectTable.$refs.coustomTable;
          refElement.clearSelection();
          selection.forEach((item) => {
            const row = this.tableData.find((k) => k.fieldId === item.fieldId);
            refElement.toggleRowSelection(row, true);
            if (this.isMultiple) {
              refElement.toggleRowSelection(row, true);
            } else {
              refElement.setCurrentRow(row);
            }
          });
        });
      } else {
        this.$nextTick(() => {
          const refElement = this.$refs.projectTable.$refs.coustomTable;
          refElements.clearSelection();
          refElement.setCurrentRow(null);
        });
      }
    },
    async handlerSubmit(formData) {
      if (formData.attachment && formData.attachment.length > 0) {
        formData.attachment = formData.attachment.map((item) => item.name).join(",")
      }
      this.handlerSave({
        formData,
        queryParams: {
          templateId: this.$route.query.templateId,
        },
        apis: {
          addApi: postBpmPlanTemplateField,
          editApi: putBpmPlanTemplateField,
        },
        editParams: ["fieldId"],
      });
    },

    handlerRowClick(row) {
      if (this.isDialog) {
        this.$emit("onTableRowClick", row);
        return;
      }
    },

    handlerTableRowClick(item) {
      this.goToPage({
        path: "/carbon-iot/packages/classes/detail",
        query: {
          className: item.className,
        },
      });
    },

    // 新增按钮事件
    handlerPageButton(item) {
      this.pageButton({ item });
    },

    async getInfo(fieldId) {
      this.getInfoData({ id: fieldId, api: getBpmPlanTemplateFieldFieldId });
    },

    deleteItem(fieldId) {
      this.deleteTableItem({ id: fieldId, api: deleteBpmPlanTemplateFieldFieldId });
    },

    handlerButton(btnItem) {
      this.tableButton({ btnItem, idKey: "fieldId" });
    },
  },
};
</script>
<style scoped lang="scss">
.title-divider {
  height: 18px;
  width: 100%;
  border-left: 4px solid #11a983;
  font-size: 16px;
  color: #333;
  font-weight: 600;
  display: flex;
  align-items: center;
  padding-left: 12px;
  margin-bottom: 24px;
}</style>
