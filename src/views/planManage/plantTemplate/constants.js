/**
 * 由Java类 PlanTemplateField 自动转换的字段定义
 */
export const fields = {
  projectId: {
    label: '项目id',
    prop: 'projectId'
  },
  templateName: {
    label: '模板名称',
    prop: 'templateName'
  },
  enableStatus: {
    label: '启用状态',
    prop: 'enableStatus'
  },
  publishStatus: {
    label: '发布状态',
    prop: 'publishStatus'
  },
  remark: {
    label: '备注',
    prop: 'remark'
  },
};

/**
 * 使用示例：
 *
 * // 创建表单项
 * const formItem = {
 *   label: fields.fieldId.label,
 *   prop: fields.fieldId.prop,
 *   type: 'Input'
 * };
 */

/**
 * 由Java类 PlanTemplateField 自动生成的查询表单配置
 */
export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: '120px',
    labelPosition: 'right',
    size: 'small',
    formFlex: {
      gutter: 20,
      span: 8
    }
  },
  formItemJson: [
    {
      label: 'PlanTemplateField信息',
      showTitle: false,
      children: [
        {
          label: fields.templateName.label,
          prop: fields.templateName.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.templateName.label
          }
        }
      ]
    }
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        permi: 'planTemplateField:manage:query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary'
        }
      },
      {
        label: '重置',
        type: 'Reset',
        permi: 'planTemplateField:manage:query',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini'
        }
      }
    ]
  }
};


/**
 * 由Java类 PlanTemplateField 自动生成的页面按钮配置
 */
export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: 'planTemplateField:manage:add',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};


/**
 * 由Java类 PlanTemplateField 自动生成的表格配置
 */
export const TableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号'
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.templateName.label,
        prop: fields.templateName.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.enableStatus.label,
        prop: fields.enableStatus.prop,
        optionsMap: {
          1: '已启用',
          0: '已禁用'
        },
        colorOptions: {
          1: '#67C23A',
          0: '#F56C6C'
        }
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.publishStatus.label,
        prop: fields.publishStatus.prop,
        optionsMap: {
          1: '已发布',
          0: '未发布'
        },
        colorOptions: {
          1: '#67C23A',
          0: '#F56C6C'
        }
      },
      {
        align: 'left',
        headerAlign: 'left',
        width: '280',
        label: '操作',
        type: 'func',
        fixed: 'right',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: 'planTemplateField:manage:edit',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text'
            }
          },
          {
            label: '编辑模版字段',
            type: 'EditField',
            permi: 'planTemplateField:manage:edit',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text'
            }
          },
          {
            label: '发布',
            type: 'Live',
            permi: 'planTemplateField:manage:publish',
            options: {
              icon: 'el-icon-s-promotion',
              size: 'mini',
              type: 'text'
            },
            // 发布 0-未发布,1-已发布
            hiddenOptions: {
              prop: fields.publishStatus.prop,
              values: [0],
            },
          },
          {
            label: '删除',
            type: 'Delete',
            permi: 'planTemplateField:manage:remove',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text'
            }
          }
        ]
      }
    ]
  }
};


/**
 * 由Java类 PlanTemplateField 自动生成的对话框配置
 */
export const DialogJson = {
  type: 'Add',
  title: '计划模版',
  options: {
    width: '600px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary'
      }
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: ''
      }
    }
  ],
  formJson: {
    formOption: {
      labelPosition: 'right',
      inline: false,
      labelWidth: '100px',
      size: 'small',
      formFlex: {
        gutter: 20,
        span: 24
      }
    },
    defaultFormData: {
      enableStatus: 1
    },
    formItemJson: [
      {
        label: '基本信息',
        showTitle: false,
        children: [
          {
            label: fields.templateName.label,
            prop: fields.templateName.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.templateName.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.templateName.label
            }
          },
          {
            label: fields.enableStatus.label,
            prop: fields.enableStatus.prop,
            type: 'Radio',
            rules: [
              {
                required: true,
                message: '请输入' + fields.enableStatus.label,
                trigger: 'blur'
              }
            ],
            options: {
              placeholder: '请输入' + fields.enableStatus.label
            },
            option: [
              {
                label: '启用',
                value: 1
              },
              {
                label: '禁用',
                value: 0
              }
            ]
          }
        ]
      }
    ]
  }
};
