<template>
  <div class="app-container">
    <CoustomTable
      ref="projectTable"
      :page-buttons-json="pageButtonsJson"
      :query-form-json="queryFormJson"
      :table-data="tableData"
      :table-json="tableJson"
      :total="total"
      :isDialog="isDialog"
      :paginations="defaultQueryParams"
      @onButton="handlerButton"
      @onPageButton="handlerPageButton"
      @onSearch="handlerSearch"
      @onTableRowClick="handlerTableRowClick"
      @onRowClick="handlerRowClick"
      @onPagination="handlerPagination"
    />

    <CoustomFormDialog
      ref="formDialog"
      :dialog-json="dialogJson"
      :init-data="initFormData"
      :visible.sync="dialogVisible"
      @onSubmit="handlerSubmit"
      @onDialogClose="handlerDialogClose"
    >
    </CoustomFormDialog>

  </div>
</template>
<script>
import CoustomTable from "@/components/CoustomV2/CoustomTable";
import CoustomFormDialog from "@/components/CoustomV2/CoustomFormDialog";
import {
  TableJson,
  QueryFormJson,
  PageButtonsJson,
  DialogJson,
} from "./constants.js";
import {
  getBpmPlanInstList,
  postBpmPlanInst,
  getBpmPlanInstPlanId,
  deleteBpmPlanInstPlanId,
  putBpmPlanInst,
} from '@/api/v2/plan/plan-inst-controller';
import pageMixin from "@/mixin/page";
export default {
  name: "PlanList",
  props: {
    isDialog: {
      type: Boolean,
      default: false,
    },
    selectList: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    isMultiple: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    CoustomTable,
    CoustomFormDialog,
  },
  mixins: [pageMixin],
  data() {
    return {
      queryFormJson: Object.freeze(QueryFormJson),
      pageButtonsJson: Object.freeze(PageButtonsJson),
      tableJson: Object.freeze(TableJson),
      dialogJson: DialogJson,
      // defaultQueryParams: {
      //   contractType: '02',
      // },
    };
  },
  watch: {
    tableData: {
      handler(val) {
        if (!!val.length && this.visible) {
          // this.$nextTick(() => {
          //   this.toggleSelection(this.selectList);
          // });
        }
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {
    this.getList({}, getBpmPlanInstList);
  },

  methods: {
    handlerMainDeviceButton(item) {
    },
    handlerMainDevicePageButton(item) {

    },

    clearSelection() {
      const refElement = this.$refs.projectTable.$refs.coustomTable;
      refElement.setCurrentRow(null);
      refElement.clearSelection();
    },
    toggleSelection(selection) {
      if (selection && selection.length > 0) {
        this.$nextTick(() => {
          const refElement = this.$refs.projectTable.$refs.coustomTable;
          refElement.clearSelection();
          selection.forEach((item) => {
            const row = this.tableData.find((k) => k.planId === item.planId);
            refElement.toggleRowSelection(row, true);
            if (this.isMultiple) {
              refElement.toggleRowSelection(row, true);
            } else {
              refElement.setCurrentRow(row);
            }
          });
        });
      } else {
        this.$nextTick(() => {
          const refElement = this.$refs.projectTable.$refs.coustomTable;
          refElement.clearSelection();
          refElement.setCurrentRow(null);
        });
      }
    },
    async handlerSubmit(formData) {
      console.log(formData);
       this.handlerSave({
        formData,
        queryParams: {
          templateId: formData.templateId,
          planName: formData.planName,
        },
        apis: {
          addApi: postBpmPlanInst,
          editApi: putBpmPlanInst,
        },
        editParams: ["planId"],
        onSuccess: (result) => {
          this.$router.push({
            path: '/carbon-flowable/planManage/planList/create',
            query: {
              ...formData,
              planId: result?.planId,
            },
          });
        }
      });
    },

    handlerRowClick(row) {
      if (this.isDialog) {
        this.$emit("onTableRowClick", row);
        return;
      }
    },

    handlerTableRowClick(item) {
      this.goToPage({
        path: "/carbon-iot/packages/classes/detail",
        query: {
          className: item.className,
        },
      });
    },

    // 新增按钮事件
    handlerPageButton(item) {
      this.pageButton({ item });
    },

    async getInfo(planId) {
      this.getInfoData({ id: planId, api: getBpmPlanInstPlanId });
    },

    deleteItem(planId) {
      this.deleteTableItem({ id: planId, api: deleteBpmPlanInstPlanId });
    },

    handlerButton(btnItem) {
      const { item, row } = btnItem;
      if(item.type === 'EditPlan'){
        this.goToPage({
          path: '/carbon-flowable/planManage/planList/create',
          query: {
            planName: row.planName,
            planId: row.planId,
            templateId: row.templateId,
          },
        });
        return;
      }
      this.tableButton({ btnItem, idKey: "planId" });
    },
  },
};
</script>
<style scoped lang="scss">
.title-divider {
  height: 18px;
  width: 100%;
  border-left: 4px solid #11a983;
  font-size: 16px;
  color: #333;
  font-weight: 600;
  display: flex;
  align-items: center;
  padding-left: 12px;
  margin-bottom: 24px;
}</style>
