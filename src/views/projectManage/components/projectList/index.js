export const fields = {
  projectId: {
    label: '项目ID',
    prop: 'projectId'
  },
  projectName: {
    label: '项目名称',
    prop: 'projectName'
  },
  projectLocation: {
    label: '项目地点',
    prop: 'projectLocation'
  },
  projectOwner: {
    label: '项目业主',
    prop: 'projectOwner'
  },
  projectScale: {
    label: '项目规模',
    prop: 'projectScale'
  },
  constructionNature: {
    label: '建设性质',
    prop: 'constructionNature'
  },
  projectType: {
    label: '项目类型',
    prop: 'projectType'
  },
  landArea: {
    label: '占地面积',
    prop: 'landArea'
  },
  landNature: {
    label: '土地性质',
    prop: 'landNature'
  },
  gridConnectionMode: {
    label: '并网方式',
    prop: 'gridConnectionMode'
  },
  plannedStartDate: {
    label: '计划开工时间',
    prop: 'plannedStartDate'
  },
  plannedCompletionDate: {
    label: '计划竣工时间',
    prop: 'plannedCompletionDate'
  },
  projectInvestment: {
    label: '项目投资',
    prop: 'projectInvestment'
  },
  fundingSource: {
    label: '资金来源',
    prop: 'fundingSource'
  },
  designLifespan: {
    label: '设计寿命(数字)',
    prop: 'designLifespan'
  },
  annualPowerGeneration: {
    label: '年发电量',
    prop: 'annualPowerGeneration'
  },
  annualEquivalentHours: {
    label: '年等效利用小时数',
    prop: 'annualEquivalentHours'
  },
  environmentalImpact: {
    label: '环境影响',
    prop: 'environmentalImpact'
  },
  benefitAnalysis: {
    label: '效益分析',
    prop: 'benefitAnalysis'
  },
  constructionUnit: {
    label: '建设单位',
    prop: 'constructionUnit'
  },
  constructionLeader: {
    label: '建设单位项目负责人',
    prop: 'constructionLeader'
  },
  constructionLeaderName: {
    label: '建设单位项目负责人',
    prop: 'constructionLeaderName'
  },
  designUnit: {
    label: '设计单位',
    prop: 'designUnit'
  },
  designLeader: {
    label: '设计单位项目负责人',
    prop: 'designLeader'
  },
  designLeaderName: {
    label: '设计单位项目负责人',
    prop: 'designLeaderName'
  },
  supervisionUnit: {
    label: '监理单位',
    prop: 'supervisionUnit'
  },
  supervisionLeader: {
    label: '监理单位项目负责人',
    prop: 'supervisionLeader'
  },
  supervisionLeaderName: {
    label: '监理单位项目负责人',
    prop: 'supervisionLeaderName'
  },
  generalContractor: {
    label: '总包单位',
    prop: 'generalContractor'
  },
  generalContractorLeader: {
    label: '总包单位项目负责人',
    prop: 'generalContractorLeader'
  },
  generalContractorLeaderName: {
    label: '总包单位项目负责人',
    prop: 'generalContractorLeaderName'
  },
  constructionCompany: {
    label: '施工单位',
    prop: 'constructionCompany'
  },
  constructionCompanyLeader: {
    label: '施工单位项目负责人',
    prop: 'constructionCompanyLeader'
  },
  constructionCompanyLeaderName: {
    label: '施工单位项目负责人',
    prop: 'constructionCompanyLeaderName'
  }
};

export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: '120px',
    labelPosition: 'right',
    size: 'small',
    formFlex: {
      gutter: 20,
      span: 8
    }
  },
  formItemJson: [
    {
      label: '项目信息',
      showTitle: false,
      children: [
        {
          label: fields.projectName.label,
          prop: fields.projectName.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.projectName.label
          }
        },
        {
          label: fields.projectLocation.label,
          prop: fields.projectLocation.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.projectLocation.label
          }
        },
        {
          label: fields.projectOwner.label,
          prop: fields.projectOwner.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.projectOwner.label
          }
        },
        {
          label: fields.constructionNature.label,
          prop: fields.constructionNature.prop,
          type: 'Select',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请选择' + fields.constructionNature.label
          },
          dictType: 'construction_nature',
          option: []
        },
        {
          label: fields.projectType.label,
          prop: fields.projectType.prop,
          type: 'Select',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请选择' + fields.projectType.label
          },
          dictType: 'project_type',
          option: []
        }
      ]
    }
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        permi: 'project:manage:query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary'
        }
      },
      {
        label: '重置',
        type: 'Reset',
        permi: 'project:manage:query',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini'
        }
      }
    ]
  }
};

export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: 'project:manage:add',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};

export const TableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    selectIdKey: 'projectId',
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号'
      },
      {
        align: 'left',
        headerAlign: 'left',
        width: '180',
        label: fields.projectName.label,
        prop: fields.projectName.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.projectLocation.label,
        prop: fields.projectLocation.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.projectOwner.label,
        prop: fields.projectOwner.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.projectType.label,
        prop: fields.projectType.prop,
        dictType: 'project_type',
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.constructionNature.label,
        prop: fields.constructionNature.prop,
        dictType: 'construction_nature',
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.projectScale.label,
        prop: fields.projectScale.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.projectInvestment.label,
        prop: fields.projectInvestment.prop,
        showOverflowTooltip: true
      },
      {
        align: 'center',
        headerAlign: 'center',
        width: '130',
        label: '操作',
        type: 'func',
        fixed: 'right',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: 'project:manage:edit',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text'
            }
          },
          {
            label: '删除',
            type: 'Delete',
            permi: 'project:manage:remove',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text'
            }
          }
        ]
      }
    ]
  }
};

export const DialogJson = {
  type: 'Add',
  title: '项目',
  options: {
    width: '70%',
    appendToBody: true,
    destroyOnClose: false,
    closeOnClickModal: false
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary'
      }
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: ''
      }
    }
  ],
  formJson: {
    formOption: {
      labelPosition: 'right',
      inline: false,
      labelWidth: '160px',
      size: 'small',
      formFlex: {
        gutter: 20,
        span: 12
      }
    },
    defaultFormData: {
    },
    formItemJson: [
      {
        label: '基本信息',
        showTitle: true,
        children: [
          {
            label: fields.projectName.label,
            prop: fields.projectName.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.projectName.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.projectName.label
            }
          },
          {
            label: fields.projectLocation.label,
            prop: fields.projectLocation.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.projectLocation.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.projectLocation.label
            }
          },
          {
            label: fields.projectOwner.label,
            prop: fields.projectOwner.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.projectOwner.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.projectOwner.label
            }
          },
          {
            label: fields.projectScale.label,
            prop: fields.projectScale.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.projectScale.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              slotAppendName: 'MW',
              placeholder: '请输入' + fields.projectScale.label,
            }
          },
          {
            label: fields.constructionNature.label,
            prop: fields.constructionNature.prop,
            type: 'Select',
            rules: [
              {
                required: true,
                message: '请选择' + fields.constructionNature.label,
                trigger: 'change'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请选择' + fields.constructionNature.label
            },
            dictType: 'construction_nature',
            option: []
          },
          {
            label: fields.projectType.label,
            prop: fields.projectType.prop,
            type: 'Select',
            rules: [
              {
                required: true,
                message: '请选择' + fields.projectType.label,
                trigger: 'change'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请选择' + fields.projectType.label
            },
            dictType: 'project_type',
            option: []
          },
          {
            label: fields.landArea.label,
            prop: fields.landArea.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.landArea.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.landArea.label
            }
          },
          {
            label: fields.landNature.label,
            prop: fields.landNature.prop,
            type: 'Select',
            rules: [
              {
                required: true,
                message: '请选择' + fields.landNature.label,
                trigger: 'change'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请选择' + fields.landNature.label
            },
            dictType: 'land_nature',
            option: []
          },
          {
            label: fields.gridConnectionMode.label,
            prop: fields.gridConnectionMode.prop,
            type: 'Select',
            rules: [
              {
                required: true,
                message: '请选择' + fields.gridConnectionMode.label,
                trigger: 'change'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请选择' + fields.gridConnectionMode.label
            },
            dictType: 'grid_connection_mode',
            option: []
          },
          {
            label: fields.plannedStartDate.label,
            prop: fields.plannedStartDate.prop,
            type: 'DatePicker',
            rules: [
              {
                required: true,
                message: '请选择' + fields.plannedStartDate.label,
                trigger: 'change'
              }
            ],
            options: {
              type: 'date',
              valueFormat: 'yyyy-MM-dd',
              format: 'yyyy-MM-dd',
              pickerOptions: {}
            }
          },
          {
            label: fields.plannedCompletionDate.label,
            prop: fields.plannedCompletionDate.prop,
            type: 'DatePicker',
            rules: [
              {
                required: true,
                message: '请选择' + fields.plannedCompletionDate.label,
                trigger: 'change'
              }
            ],
            options: {
              type: 'date',
              valueFormat: 'yyyy-MM-dd',
              format: 'yyyy-MM-dd',
              pickerOptions: {}
            }
          },
          {
            label: fields.projectInvestment.label,
            prop: fields.projectInvestment.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.projectInvestment.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              slotAppendName: '万元',
              placeholder: '请输入' + fields.projectInvestment.label
            }
          },
          {
            label: fields.fundingSource.label,
            prop: fields.fundingSource.prop,
            type: 'Select',
            rules: [
              {
                required: true,
                message: '请选择' + fields.fundingSource.label,
                trigger: 'change'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请选择' + fields.fundingSource.label
            },
            dictType: 'funding_source',
            option: []
          },
          {
            label: fields.designLifespan.label,
            prop: fields.designLifespan.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.designLifespan.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              slotAppendName: '年',
              placeholder: '请输入' + fields.designLifespan.label
            }
          },
          {
            label: fields.annualPowerGeneration.label,
            prop: fields.annualPowerGeneration.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.annualPowerGeneration.label,
                trigger: 'blur'
              }
            ],
            options: {
              slotAppendName: 'kWh',
              clearable: true,
              placeholder: '请输入' + fields.annualPowerGeneration.label
            }
          },
          {
            label: fields.annualEquivalentHours.label,
            prop: fields.annualEquivalentHours.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.annualEquivalentHours.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.annualEquivalentHours.label
            }
          },
          {
            label: fields.environmentalImpact.label,
            prop: fields.environmentalImpact.prop,
            type: 'Input',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.environmentalImpact.label,
                trigger: 'blur'
              }
            ],
            options: {
              type: 'textarea',
              clearable: true,
              rows: 4,
              placeholder: '简要说明项目对环境的影响，如无重大影响可写 “对环境影响较小，主要为施工期的扬尘、噪声等，运营期无污染物排放” 等'
            }
          },
          {
            label: fields.benefitAnalysis.label,
            prop: fields.benefitAnalysis.prop,
            type: 'Input',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.benefitAnalysis.label,
                trigger: 'blur'
              }
            ],
            options: {
              type: 'textarea',
              rows: 4,
              clearable: true,
              placeholder: '包括经济效益，如项目的内部收益率、投资回收期、年收益等；社会效益，如提供就业机会、促进当地经济发展等'
            }
          }
        ]
      },
      {
        label: '参建单位',
        showTitle: true,
        children: [
          {
            label: fields.constructionUnit.label,
            prop: fields.constructionUnit.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.constructionUnit.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.constructionUnit.label
            }
          },
          {
            label: fields.constructionLeader.label,
            prop: fields.constructionLeaderName.prop,
            associationProp: fields.constructionLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.constructionLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.constructionLeader.label
            }
          },
          {
            label: fields.designUnit.label,
            prop: fields.designUnit.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.designUnit.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.designUnit.label
            }
          },
          {
            label: fields.designLeader.label,
            prop: fields.designLeaderName.prop,
            associationProp: fields.designLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.designLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.designLeader.label
            }
          },
          {
            label: fields.supervisionUnit.label,
            prop: fields.supervisionUnit.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.supervisionUnit.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.supervisionUnit.label
            }
          },
          {
            label: fields.supervisionLeader.label,
            prop: fields.supervisionLeaderName.prop,
            associationProp: fields.supervisionLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.supervisionLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.supervisionLeader.label
            }
          },
          {
            label: fields.generalContractor.label,
            prop: fields.generalContractor.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.generalContractor.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.generalContractor.label
            }
          },
          {
            label: fields.generalContractorLeader.label,
            prop: fields.generalContractorLeaderName.prop,
            associationProp: fields.generalContractorLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.generalContractorLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.generalContractorLeader.label
            }
          },
          {
            label: fields.constructionCompany.label,
            prop: fields.constructionCompany.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.constructionCompany.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.constructionCompany.label
            }
          },
          {
            label: fields.constructionCompanyLeader.label,
            prop: fields.constructionCompanyLeaderName.prop,
            associationProp: fields.constructionCompanyLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.constructionCompanyLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.constructionCompanyLeader.label
            }
          }
        ]
      }
    ]
  }
};
