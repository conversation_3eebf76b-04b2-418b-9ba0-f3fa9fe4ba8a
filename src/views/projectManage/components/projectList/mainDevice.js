// import { validateAmount, arrivalTimeValidator } from "@/utils/validator";

// 行程明细相关
export const fields = {
  id: {
    label: '明细ID',
    prop: 'id'
  },

  reimburseId: {
    label: '报销ID',
    prop: 'reimburseId'
  },

  seqNo: {
    label: '序号',
    prop: 'seqNo'
  },

  departurePlace: {
    label: '子序号名称',
    prop: 'departurePlace'
  },

  arrivalPlace: {
    label: '主要设备类型',
    prop: 'arrivalPlace'
  },

  departureDate: {
    label: '设备品牌',
    prop: 'departureDate'
  },

  arrivalDate: {
    label: '设备型号',
    prop: 'arrivalDate'
  },
  subsidyType: {
    label: '设备规格',
    prop: 'subsidyType'
  },
  tripDays: {
    label: '设备规模',
    prop: 'tripDays'
  },
  arrivalPlace: {
    label: '到达地',
    prop: 'arrivalPlace'
  },

  subsidyFee: {
    label: '补贴金额',
    prop: 'subsidyFee'
  },
  gasolineFee: {
    label: '汽油费',
    prop: 'gasolineFee'
  },
  trainBusFee: {
    label: '火/汽车费',
    prop: 'trainBusFee'
  },
  airTicketFee: {
    label: '机票',
    prop: 'airTicketFee'
  },
  otherFee: {
    label: '其他车费',
    prop: 'otherFee'
  },
  accommodationFee: {
    label: '住宿费',
    prop: 'accommodationFee'
  },
  totalAmount: {
    label: '总金额',
    prop: 'totalAmount'
  },

  remark: {
    label: '备注',
    prop: 'remark'
  },

  orderNum: {
    label: '排序号',
    prop: 'orderNum'
  },

  status: {
    label: '状态',
    prop: 'status'
  },

  delFlag: {
    label: '删除标志',
    prop: 'delFlag'
  },

  createBy: {
    label: '创建者',
    prop: 'createBy'
  },

  createTime: {
    label: '创建时间',
    prop: 'createTime'
  },

  updateBy: {
    label: '更新者',
    prop: 'updateBy'
  },

  updateTime: {
    label: '更新时间',
    prop: 'updateTime'
  }
};

export const mainDevicePageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  hiddenToolbar: true,
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: '',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};

export const mainDeviceTableJson = {
  options: {
    height: 240,
  },
  columnJson: {
    showSelect: false,
    showIndex: true,
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号',
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.departurePlace.label,
        prop: fields.departurePlace.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.departureDate.label,
        prop: fields.departureDate.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.arrivalPlace.label,
        prop: fields.arrivalPlace.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.arrivalDate.label,
        prop: fields.arrivalDate.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.subsidyType.label,
        prop: fields.subsidyType.prop,
        showOverflowTooltip: true,
        dictType: 'subsidy_type',
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.tripDays.label,
        prop: fields.tripDays.prop,
      },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   width: '120',
      //   label: fields.subsidyFee.label,
      //   prop: fields.subsidyFee.prop,
      //   showOverflowTooltip: true,
      // },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   width: '120',
      //   label: fields.gasolineFee.label,
      //   prop: fields.gasolineFee.prop,
      //   showOverflowTooltip: true,
      // },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   width: '120',
      //   label: fields.trainBusFee.label,
      //   prop: fields.trainBusFee.prop,
      //   showOverflowTooltip: true,
      // },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   width: '120',
      //   label: fields.airTicketFee.label,
      //   prop: fields.airTicketFee.prop,
      // },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   width: '120',
      //   label: fields.accommodationFee.label,
      //   prop: fields.accommodationFee.prop,
      // },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   width: '120',
      //   label: fields.otherFee.label,
      //   prop: fields.otherFee.prop,
      // },
      // {
      //   align: 'left',
      //   headerAlign: 'left',
      //   width: '120',
      //   fixed: 'right',
      //   label: fields.totalAmount.label,
      //   prop: fields.totalAmount.prop,
      // },
      {
        align: 'center',
        headerAlign: 'center',
        width: '130',
        label: '操作',
        type: 'func',
        fixed: 'right',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: '',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text',
            },
          },
          {
            label: '删除',
            type: 'Delete',
            permi: '',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text',
            },
          },
        ],
      },
    ],
  },
};

export const mainDeviceDialogJson = {
  type: 'Add',
  title: '主要设备',
  options: {
    width: '70%',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary',
      },
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: '',
      },
    },
  ],
  formJson: {
    formOption: {
      labelPosition: 'right',
      inline: false,
      labelWidth: '100px',
      size: 'small',
      formFlex: {
        gutter: 20,
        span: 6
      }
    },
    formItemJson: [
      {
        label: '基本信息',
        showTitle: false,
        children: [
          {
            label: fields.departurePlace.label,
            prop: fields.departurePlace.prop,
            type: 'Input',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.departurePlace.label,
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.departurePlace.label,
                trigger: 'blur',
              },
            ],
          },
          {
            label: fields.departureDate.label,
            prop: fields.departureDate.prop,
            type: 'DatePicker',
            isEnter: true,
            options: {
              type: 'date',
              valueFormat: 'yyyy-MM-dd',
              pickerOptions: {}
            },
            rules: [
              {
                required: true,
                message: '请选择' + fields.departureDate.label,
                trigger: 'blur',
              }
            ],
          },
          {
            label: fields.arrivalPlace.label,
            prop: fields.arrivalPlace.prop,
            type: 'Input',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.arrivalPlace.label,
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.arrivalPlace.label,
                trigger: 'blur',
              },
            ],
          },
          {
            label: fields.arrivalDate.label,
            prop: fields.arrivalDate.prop,
            type: 'DatePicker',
            isEnter: true,
            options: {
              type: 'date',
              valueFormat: 'yyyy-MM-dd',
              pickerOptions: {}
            },
            rules: [
              {
                required: true,
                message: '请选择' + fields.arrivalDate.label,
                trigger: 'blur',
              },
              // {
              //   validator: arrivalTimeValidator,
              //   trigger: 'blur',
              // },
            ],
          },
          {
            label: fields.subsidyType.label,
            prop: fields.subsidyType.prop,
            type: 'Select',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.subsidyType.label,
            },
            dictType: 'subsidy_type',

          },
          {
            label: fields.tripDays.label,
            prop: fields.tripDays.prop,
            type: 'Input',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.tripDays.label,
            },
          },
          {
            label: fields.subsidyFee.label,
            prop: fields.subsidyFee.prop,
            type: 'Input',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.subsidyFee.label,
            },
            rules: [
              // { validator: validateAmount, trigger: 'blur' }
            ]
          },
          {
            label: fields.gasolineFee.label,
            prop: fields.gasolineFee.prop,
            type: 'Input',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.gasolineFee.label,
            },
            rules: [
              // { validator: validateAmount, trigger: 'blur' }
            ]
          },
          {
            label: fields.trainBusFee.label,
            prop: fields.trainBusFee.prop,
            type: 'Input',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.trainBusFee.label,
            },
            rules: [
              // { validator:   validateAmount, trigger: 'blur' }
            ]
          },
          {
            label: fields.airTicketFee.label,
            prop: fields.airTicketFee.prop,
            type: 'Input',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.airTicketFee.label,
            },
            rules: [
              // { validator: validateAmount, trigger: 'blur' }
            ]
          },
          {
            label: fields.accommodationFee.label,
            prop: fields.accommodationFee.prop,
            type: 'Input',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.accommodationFee.label,
            },
            rules: [
              // { validator: validateAmount, trigger: 'blur' }
            ]
          },
          {
            label: fields.otherFee.label,
            prop: fields.otherFee.prop,
            type: 'Input',
            isEnter: true,
            options: {
              clearable: true,
              placeholder: '请输入' + fields.otherFee.label,
            },
            rules: [
              // { validator: validateAmount, trigger: 'blur' }
            ]
          },
          // {
          //   label: fields.totalAmount.label,
          //   prop: fields.totalAmount.prop,
          //   type: 'Input',
          //   isEnter: true,
          //   options: {
          //     disabled: true,
          //     clearable: true,
          //     placeholder: '请输入' + fields.totalAmount.label,
          //   },
          //   rules: [
          //     { validator: validateAmount, trigger: 'blur' }
          //   ]
          // },
        ]
      }
    ],
  },
};

