<template>
  <div class="treeList">
    <!-- <el-button @click="handleAddClik" class="addBtn" icon="el-icon-plus" size="mini" type="primary" plain>新增</el-button> -->
    <el-tree
      ref="elTree"
      v-if="treeData.length !== 0"
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      highlight-current
      :expand-on-click-node="false"
      :load="loadNode"
      lazy
      @node-click="handleNodeClick"
    >
      <!-- <span class="custom-tree-node" slot-scope="{ node, data }">
        <span>{{ node.label }}</span>
        <span>
          <el-button type="text" icon="el-icon-plus" size="mini" @click="() => handleAdd(data, node)">
            新增
          </el-button>
          <el-button type="text"  icon="el-icon-delete" size="mini" @click="() => handleRemove(node, data)">
            删除
          </el-button>
        </span>
      </span> -->
    </el-tree>
    <el-empty :image-size="200" v-else></el-empty>
  </div>
</template>
<script>
export default {
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  created() {},
  mounted() {},
  computed: {},
  methods: {
    refreshNodeBy(id){
      let node = this.$refs['elTree'].getNode(id); // 通过节点id找到对应树节点对象
      node.loaded = false;
      node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
    },
    setCurrentKey(keys) {
      this.$refs.elTree.setCurrentKey(keys);
    },
    loadNode(node, resolve) {
      this.$emit('loadNode', node, resolve)
    },
    handleAddClik() {
      this.$emit('onAddClik')
    },
    handleAdd(data, node) {
      console.log('object :>> ', data, node);
      this.$emit('onAdd', data, node)
    },

    handleRemove(node, data) {
      console.log('object :>> ', node, data);
      this.$emit('onRemove', data)
    },
    handleNodeClick(data) {
      console.log(data);
      this.$emit('onClick', data)
    },
  },
};
</script>
<style scoped lang="scss">
.addBtn {
  margin-bottom: 12px;
}
::v-deep .el-tree-node__content {
  margin-bottom: 6px;
  width: 90%;
}
.custom-tree-node {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;;
  width: 90%;
}
</style>
