<template>
  <div class="app-container" v-loading="loading">
    <el-row :gutter="24">
      <el-col :span="6">
        <TreeList
          ref="treeList"
          :treeData="treeData"
          @onClick="hadnleTreeClick"
          @loadNode="loadNode"
        ></TreeList>
      </el-col>
      <el-col :span="18">
        <CoustomTable
          ref="projectTable"
          :page-buttons-json="pageButtonsJson"
          :query-form-json="queryFormJson"
          :table-data="tableData"
          :table-json="tableJson"
          :total="total"
          :paginations="defaultQueryParams"
          @onButton="handlerButton"
          @onPageButton="handlerPageButton"
          @onSearch="handlerSearch"
          @onTableRowClick="handlerTableRowClick"
          @onRowClick="handlerRowClick"
          @onPagination="handlerPagination"
        />

        <CoustomFormDialog
          ref="formDialog"
          :dialog-json="dialogJson"
          :init-data="initFormData"
          :visible.sync="dialogVisible"
          :add-init-no-reset="addInitNoReset"
          @onSubmit="handlerSubmit"
          @onDialogClose="handlerDialogClose"
          @formDataChange="handleFormDataChange"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import TreeList from './treeList.vue';
import CoustomTable from '@/components/CoustomV2/CoustomTable/index.vue';
import CoustomFormDialog from '@/components/CoustomV2/CoustomFormDialog/index.vue';
import pageMixin from '@/mixin/page';
import {
  getBpmEnergyList
} from '@/api/v2/energy/proj-new-energy-controller';
import {
  listPackage,
  delPackage,
  getPackage,
  updatePackage,
  addPackage,
} from '@/api/v2/energy/proj-package';
import { TableJson, QueryFormJson, PageButtonsJson, DialogJson } from './index.js';
export default {
  components: {
    TreeList,
    CoustomTable,
    CoustomFormDialog,
  },
  mixins: [pageMixin],
  data() {
    return {
      treeData: [],
      node: {},
      resolve: {},
      treeSelectId: '',
      treeSelectItem: null,
      tableData: [],
      loading: false,
      queryFormJson: Object.freeze(QueryFormJson),
      pageButtonsJson: Object.freeze(PageButtonsJson),
      tableJson: Object.freeze(TableJson),
      dialogJson: DialogJson,
      defaultQueryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    init() {
      this.getProjectList();
    },
    async getProjectList() {
      const { rows = [] } = await getBpmEnergyList();
      this.treeData = rows.map(item => {
        // 给项目节点添加一个id，用于区分项目节点和其它节点
        item.id = 'projectId-' + item.projectId;
        item.label = item.projectName;
        item.children = [];
        item.isLeaf = true;
        return item;
      });
      this.treeSelectItem = this.treeData[0];
      this.getPackageList();
      // 设置默认选中第一项
      this.$nextTick(() => {
        this.$refs.treeList.setCurrentKey(this.treeSelectItem.id);
      });
      console.log(this.treeData);
    },
    getPackageList() {
      const { divisionType, projId } = this.treeSelectItem;
      this.getList({
        parentId: divisionType ? projId : 0,
        projectId: this.treeSelectItem.projectId
      }, listPackage);
    },
    updateApiConfig() {
      const { divisionType, projId } = this.treeSelectItem;
      this.dialogJson.formJson.formItemJson[0].children[0].apiConfig.params.queryParams = {
        parentId: !divisionType ? 0 : projId
      };
    },
    async handlerSubmit(formData) {
      this.handlerSave({
        formData: {
          ...formData,
          projectId: this.treeSelectItem.projectId,
          parentId: formData.divisionType === '01' ? 0 : this.treeSelectItem.projId,
        },
        queryParams: {},
        apis: {
          addApi: addPackage,
          editApi: updatePackage,
        },
        editParams: ['projId'],
        onSuccess: () => {
          this.refreshTree();
        }
      });
    },

    // 新增回显部门及人员
    async handlerEcho() {
      const {
        constructionLeader,
        constructionLeaderName,
        designUnit,
        supervisionUnit,
        constructionCompany,
        constructionCompanyName,
        constructionCompanyLeader,
        constructionCompanyLeaderName,
        constructionUnit,
        generalContractor,
        generalContractorLeader,
        generalContractorLeaderName,
        supervisionLeader,
        supervisionLeaderName,
        designLeader,
        designLeaderName,
      } = this.treeSelectItem || {};

      this.addInitNoReset = true;

      this.initFormData = {
        constructionUnit,
        constructionLeader,
        constructionLeaderName,
        designUnit,
        supervisionUnit,
        constructionCompany,
        constructionCompanyName,
        constructionCompanyLeader,
        constructionCompanyLeaderName,
        constructionUnit,
        generalContractor,
        generalContractorLeader,
        generalContractorLeaderName,
        supervisionLeader,
        supervisionLeaderName,
        designLeader,
        designLeaderName,
      };
      this.updateDialogType();
    },

    updateDialogType() {
      const { level = 0 } = this.treeSelectItem || {};
      console.log('level', level, this.treeSelectItem)
      let divisionType = '';
      switch (level) {
        case 0:
          divisionType = '01';
          break;
        case 1:
          divisionType = '02';
          break;
        case 2:
          divisionType = '03';
          break;
        case 3:
          divisionType = '04';
          break;
        case 4:
          divisionType = '05';
          break;
        case 5:
          divisionType = '06';
          break;
      }
      this.initFormData.divisionType = divisionType;
      this.updateApiConfig();
    },

    handleFormDataChange(formData) {
      // console.log(formData);
      // if (formData.divisionType) {
      //   this.dialogJson.formJson.formItemJson[0].children[2].label = '子单位';
      // }
      // this.$emit('formDataChange', formData);
    },


    refreshTree() {
      this.$refs.treeList.refreshNodeBy(this.treeSelectItem.id);
    },
    async loadNode(node, resolve) {
      this.node = node;
      this.resolve = resolve;
      this.node.childNodes = [];
      console.log('node :>> 触发', node);
      if (node.level === 0) {
        return resolve(this.treeData);
      }
      if (node.level >= 1) {
        console.log('first', node, node.level, node.data.projId)
        const { rows = [] } = await listPackage({
          parentId: node.level === 1 ? 0 : node.data.projId,
        });
        const data = rows.map(item => {
          item.id = item.projId;
          item.level = node.level;
          item.label = item.projName;
          item.children = [];
          item.isLeaf = true;
          return item;
        });
        return resolve(data);
      }

      resolve([]);
    },
    hadnleTreeClick(item) {
      this.treeSelectItem = item;
      this.getPackageList();
    },



    handlerRowClick(row) {
      if (this.isDialog) {
        this.$emit('onTableRowClick', row);
        return;
      }
    },

    handlerTableRowClick(item) {
      this.goToPage({
        path: '/carbon-iot/packages/classes/detail',
        query: {
          className: item.className,
        },
      });
    },
    async getInfo(projId) {
      this.getInfoData({ id: projId, api: getPackage });
    },

    deleteItem(projId) {
      this.deleteTableItem({ id: projId, api: delPackage, onSuccess: () => {
        this.refreshTree();
      } });
    },
    // 新增按钮事件
    handlerPageButton(item) {
      if (item.type === 'Add') {
        this.handlerEcho();
      }

      this.pageButton({ item });
    },
    handlerButton(btnItem) {
      this.addInitNoReset = false;
      this.updateDialogType();
      this.tableButton({ btnItem, idKey: 'projId' });
    },
    async handleSubmit() {
      const result = await this.$refs.coustomForm.validateForm();
      if (result) {
        console.log(result);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.addBtn {
  margin-bottom: 12px;
}
::v-deep .el-tree-node__content {
  margin-bottom: 6px;
  width: 90%;
}
.custom-tree-node {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 90%;
}
</style>
