import { postBpmQaDivisionListAll } from '@/api/v2/qa/project-division-controller.js';

/**
 * 由Java类 ProjNewEnergy 自动转换的字段定义
 */
export const fields = {
  projId: {
    label: 'projId',
    prop: 'projId'
  },
  projectId: {
    label: '项目Id',
    prop: 'projectId'
  },
  projCode: {
    label: '工程编码',
    prop: 'projCode'
  },
  projName: {
    label: '工程名称',
    prop: 'projName'
  },
  parentId: {
    label: '父类',
    prop: 'parentId'
  },
  ancestors: {
    label: '组级别',
    prop: 'ancestors'
  },
  projOverview: {
    label: '工程简介',
    prop: 'projOverview'
  },
  constructionUnit: {
    label: '建设单位',
    prop: 'constructionUnit'
  },
  constructionLeader: {
    label: '建设单位项目负责人',
    prop: 'constructionLeader'
  },
  constructionLeaderName: {
    label: '建设单位项目负责人',
    prop: 'constructionLeaderName'
  },
  designUnit: {
    label: '设计单位',
    prop: 'designUnit'
  },
  designLeader: {
    label: '设计单位项目负责人',
    prop: 'designLeader'
  },
  designLeaderName: {
    label: '设计单位项目负责人',
    prop: 'designLeaderName'
  },
  supervisionUnit: {
    label: '监理单位',
    prop: 'supervisionUnit'
  },
  supervisionLeader: {
    label: '监理单位项目负责人',
    prop: 'supervisionLeader'
  },
  supervisionLeaderName: {
    label: '监理单位项目负责人',
    prop: 'supervisionLeaderName'
  },
  generalContractor: {
    label: '总包单位',
    prop: 'generalContractor'
  },
  generalContractorLeader: {
    label: '总包单位项目负责人',
    prop: 'generalContractorLeader'
  },
  generalContractorLeaderName: {
    label: '总包单位项目负责人',
    prop: 'generalContractorLeaderName'
  },
  constructionCompany: {
    label: '施工单位',
    prop: 'constructionCompany'
  },
  constructionCompanyLeader: {
    label: '施工单位项目负责人',
    prop: 'constructionCompanyLeader'
  },
  constructionCompanyLeaderName: {
    label: '施工单位项目负责人',
    prop: 'constructionCompanyLeaderName'
  },
  divisionId: {
    label: '工程类型',
    prop: 'divisionId'
  },
  // "类型：01：单位工程；02：子单位工程； 03：分部工程；04：子分部工程；05：分项工程；06：检验批")
  divisionType: {
    label: '类型',
    prop: 'divisionType'
  },
  orderNum: {
    label: '排序',
    prop: 'orderNum'
  },
  delFlag: {
    label: 'delFlag',
    prop: 'delFlag'
  },
};

/**
 * 使用示例：
 *
 * // 创建表单项
 * const formItem = {
 *   label: fields.projId.label,
 *   prop: fields.projId.prop,
 *   type: 'Input'
 * };
 */

/**
 * 由Java类 ProjNewEnergy 自动生成的查询表单配置
 */
export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: '80px',
    labelPosition: 'left',
    size: 'small',
    formFlex: {
      gutter: 20,
      span: 8
    }
  },
  formItemJson: [
    {
      label: '',
      showTitle: false,
      children: [
        {
          label: fields.projName.label,
          prop: fields.projName.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.projName.label
          }
        },
        {
          label: fields.projCode.label,
          prop: fields.projCode.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.projCode.label
          }
        }
      ]
    }
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        permi: 'projNewEnergy:manage:query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary'
        }
      },
      {
        label: '重置',
        type: 'Reset',
        permi: 'projNewEnergy:manage:query',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini'
        }
      }
    ]
  }
};


/**
 * 由Java类 ProjNewEnergy 自动生成的页面按钮配置
 */
export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: 'projNewEnergy:manage:add',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};


/**
 * 由Java类 ProjNewEnergy 自动生成的表格配置
 */
export const TableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    selectIdKey: 'projId',
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号'
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.projCode.label,
        prop: fields.projCode.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.projName.label,
        prop: fields.projName.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.projOverview.label,
        prop: fields.projOverview.prop,
        showOverflowTooltip: true
      },
      {
        align: 'center',
        headerAlign: 'center',
        width: '130',
        label: '操作',
        type: 'func',
        fixed: 'right',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: 'projNewEnergy:manage:edit',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text'
            }
          },
          {
            label: '删除',
            type: 'Delete',
            permi: 'projNewEnergy:manage:remove',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text'
            }
          }
        ]
      }
    ]
  }
};


/**
 * 由Java类 ProjNewEnergy 自动生成的对话框配置
 */
export const DialogJson = {
  type: 'Add',
  title: '',
  options: {
    width: '50%',
    appendToBody: true,
    destroyOnClose: false,
    closeOnClickModal: false
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary'
      }
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: ''
      }
    }
  ],
  formJson: {
    formOption: {
      labelPosition: 'right',
      inline: false,
      labelWidth: '160px',
      size: 'small',
      formFlex: {
        gutter: 20,
        span: 24
      }
    },
    defaultFormData: {
    },
    formItemJson: [
      {
        label: '基本信息',
        showTitle: false,
        children: [
          {
            label: fields.divisionId.label,
            prop: fields.divisionId.prop,
            type: 'Select',
            rules: [
              {
                required: true,
                message: '请选择' + fields.divisionId.label,
                trigger: 'blur'
              }
            ],
            apiConfig: {
              api: postBpmQaDivisionListAll,
              params: {
                value: 'divisionId',      // 值字段名
                label: 'projName',    // 标签字段名
                queryParams: {
                  parentId: 0
                }   // API查询参数
              }
            },
            options: {
              clearable: true,
              filterable: true,
              placeholder: '请输入' + fields.divisionId.label
            }
          },
          {
            label: fields.divisionType.label,
            prop: fields.divisionType.prop,
            type: 'Select',
            rules: [
              {
                required: true,
                message: '请输入' + fields.divisionType.label,
                trigger: 'blur'
              }
            ],
            options: {
              disabled: true,
              clearable: true,
              placeholder: '请输入' + fields.divisionType.label
            },
            dictType: 'division_type'
          },
          {
            label: fields.projName.label,
            prop: fields.projName.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.projName.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.projName.label
            },
            // label 根据配置的 associationProp 的值 进行固定回显
            labelConfig: {
              associationProp: fields.divisionType.prop,
              labelMap: {
                '01': '单位工程',
                '02': '子单位工程',
                '03': '分部工程',
                '04': '子分部工程',
                '05': '分项工程',
                '06': '检验批',
              }
            }
          },
          {
            label: fields.projCode.label,
            prop: fields.projCode.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.projCode.label,
                trigger: 'blur'
              },
              {
                validator: (rule, value, callback) => {
                  const reg =/^[a-zA-Z][a-zA-Z0-9-]*$/;
                  if (!reg.test(value)) {
                    callback(
                      new Error(
                        "请输入以字母(不区分大小写)、数字、- 随意组合的字符串"
                      )
                    );
                  }
                  callback();
                },
                trigger: ["blur"],
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.projCode.label
            },
            labelConfig: {
              associationProp: fields.divisionType.prop,
              labelMap: {
                '01': '单位工程编码',
                '02': '子单位工程编码',
                '03': '分部工程编码',
                '04': '子分部工程编码',
                '05': '分项工程编码',
                '06': '检验批编码',
              }
            }
          },
          {
            label: fields.projOverview.label,
            prop: fields.projOverview.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.projOverview.label,
                trigger: 'blur'
              }
            ],
            options: {
              type: 'textarea',
              rows: 3,
              clearable: true,
              placeholder: '请输入' + fields.projOverview.label
            }
          },
          {
            label: fields.constructionUnit.label,
            prop: fields.constructionUnit.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.constructionUnit.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.constructionUnit.label
            }
          },
          {
            label: fields.constructionLeader.label,
            prop: fields.constructionLeaderName.prop,
            associationProp: fields.constructionLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.constructionLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.constructionLeader.label
            }
          },
          {
            label: fields.designUnit.label,
            prop: fields.designUnit.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.designUnit.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.designUnit.label
            }
          },
          {
            label: fields.designLeader.label,
            prop: fields.designLeaderName.prop,
            associationProp: fields.designLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.designLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.designLeader.label
            }
          },
          {
            label: fields.supervisionUnit.label,
            prop: fields.supervisionUnit.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.supervisionUnit.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.supervisionUnit.label
            }
          },
          {
            label: fields.supervisionLeader.label,
            prop: fields.supervisionLeaderName.prop,
            associationProp: fields.supervisionLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.supervisionLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.supervisionLeader.label
            }
          },
          {
            label: fields.generalContractor.label,
            prop: fields.generalContractor.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.generalContractor.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.generalContractor.label
            }
          },
          {
            label: fields.generalContractorLeader.label,
            prop: fields.generalContractorLeaderName.prop,
            associationProp: fields.generalContractorLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.generalContractorLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.generalContractorLeader.label
            }
          },
          {
            label: fields.constructionCompany.label,
            prop: fields.constructionCompany.prop,
            type: 'SelectDept',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.constructionCompany.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.constructionCompany.label
            }
          },
          {
            label: fields.constructionCompanyLeader.label,
            prop: fields.constructionCompanyLeaderName.prop,
            associationProp: fields.constructionCompanyLeader.prop,
            type: 'SelectUser',
            formFlex: {
              span: 12
            },
            rules: [
              {
                required: true,
                message: '请输入' + fields.constructionCompanyLeader.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.constructionCompanyLeader.label
            }
          }
        ]
      }
    ]
  }
};
