<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="项目列表" name="projectList">
        <ProjectList></ProjectList>
      </el-tab-pane>
      <el-tab-pane label="施工包维护" name="projectDivide">
        <ProjectDivide ref="projectDivide"></ProjectDivide>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import ProjectList from './components/projectList/index.vue';
import ProjectDivide from './components/projectDivide/index.vue';
export default {
  name: 'ProjectManage',
  components: {
    ProjectList,
    ProjectDivide
  },
  data() {
    return {
      activeName: 'projectList',
      cacheName: 'projectList',
    };
  },
  dicts: [
    'project_type',
    'grid_connection_mode',
    'land_nature',
    'funding_source',
    'construction_nature',
    'division_type',
  ],
  provide() {
    return {
      dictData: this.dict,
    };
  },
  created() {},
  mounted() {},
  computed: {},
  methods: {
    handleClick(tab) {
      if (this.cacheName !== tab.name && tab.name === 'projectDivide') {
        this.$refs.projectDivide.init();
      }

      this.cacheName = tab.name;
    },
  },
};
</script>
<style scoped lang="scss"></style>
