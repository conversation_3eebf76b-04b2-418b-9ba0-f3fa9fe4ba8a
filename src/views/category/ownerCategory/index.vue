<template>
  <div class="app-container">
    <el-form
      :inline="true"
      :model="queryParams"
      label-width="68px"
      ref="queryForm"
      v-show="showSearch"
    >
      <el-form-item label="分类名称" prop="cateName">
        <el-input
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入分类名称"
          size="small"
          v-model="queryParams.cateName"
        />
      </el-form-item>
      <el-form-item label="显示顺序" prop="orderNum">
        <el-input
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入显示顺序"
          size="small"
          v-model="queryParams.orderNum"
        />
      </el-form-item>
      <el-form-item label="分类状态" prop="status">
        <el-select clearable placeholder="请选择分类状态" size="small" v-model="queryParams.status">
          <el-option
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
            v-for="dict in statusOption"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" icon="el-icon-search" size="mini" type="primary"
          >搜索</el-button
        >
        <el-button @click="resetQuery" icon="el-icon-refresh" size="mini">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          @click="handleAdd"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          v-hasPermi="['fl:category:add']"
          >新增</el-button
        >
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      :data="categoryList"
      :default-expand-all="false"
      :expand-row-keys="expands"
      :row-key="getRowKeys"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      v-loading="loading"
    >
      <el-table-column label="分类名称" prop="cateName" />
      <el-table-column align="center" label="排序" prop="orderNum" />
      <!-- <el-table-column align="center" label="封面" prop="cateCover">
        <template slot-scope="scope">
          <span v-if="!scope.row.cateCover"></span>
          <img
            :src="scope.row.cateCover"
            @click="showPreview(scope.row.cateCover)"
            style="
              width: 48px;
              height: 48px;
              object-fit: cover;
              cursor: pointer;
              border: 1px solid #ededed;
            "
            v-else
          />
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="分类状态" prop="status" >
        <template slot-scope="scope">
          <span>{{ scope.row.status === 0 ? '正常' : '停用' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" prop="remark" show-overflow-tooltip />
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        label="操作"
        width="160"
      >
        <template slot-scope="scope">
          <el-button
            @click="handleUpdate(scope.row)"
            icon="el-icon-edit"
            size="mini"
            type="text"
            v-hasPermi="['fl:category:edit']"
            v-if="scope.row.cateId !== fatherId"
            >修改</el-button
          >
          <el-button
            @click="handleAdd(scope.row)"
            icon="el-icon-plus"
            size="mini"
            type="text"
            v-hasPermi="['fl:category:add']"
            >新增</el-button
          >
          <el-button
            @click="handleDelete(scope.row)"
            icon="el-icon-delete"
            size="mini"
            type="text"
            v-hasPermi="['fl:category:remove']"
            v-if="scope.row.cateId !== fatherId"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改资源分类对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :title="title"
      :visible.sync="open"
      append-to-body
      width="500px"
    >
      <el-form :model="form" :rules="rules" label-width="80px" ref="form">
        <el-form-item label="父类" prop="parentId">
          <treeselect
            :normalizer="normalizer"
            :options="categoryOptions"
            placeholder="请选择父类"
            v-model="form.parentId"
          />
        </el-form-item>
        <el-form-item label="属性标签" prop="attrs">
          <treeselect
            :disable-branch-nodes="true"
            :multiple="true"
            :normalizer="normalizerOpt"
            :options="subjectArrtOption"
            placeholder="请选择"
            v-model="form.attrs"
            valueFormat="object"
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="cateName">
          <el-input placeholder="请输入分类名称" v-model="form.cateName" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="orderNum">
          <el-input placeholder="请输入显示顺序" v-model="form.orderNum" />
        </el-form-item>
        <el-form-item label="分类状态">
          <el-radio-group v-model="form.status">
            <el-radio :key="dict.dictValue" :label="dict.dictValue" v-for="dict in statusOption">{{
              dict.dictLabel
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input placeholder="请输入内容" type="textarea" v-model="form.remark" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer" slot="footer">
        <el-button @click="submitForm" type="primary">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- <Img-preview :imgList="previewImgs" :showPreview.sync="showImgPreview"></Img-preview> -->
  </div>
</template>

<script>
import {
  postBpmFileCategory,
  putBpmFileCategory,
  getBpmFileCategoryList,
  deleteBpmFileCategoryCateIds,
  getBpmFileCategoryCateId,
} from '@/api/v2/file/flile-category-controller';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
// import CustomUpload from "@/components/CustomUpload";
// import ImgPreview from "@/components/ImgPreview";
import { deepClone } from '@/utils/index';
export default {
  name: 'Category',
  components: {
    Treeselect,
    // CustomUpload,
    // ImgPreview
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 资源分类表格数据
      categoryList: [],
      // 资源分类树选项
      categoryOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        cateName: null,
        orderNum: null,
        cateCover: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      statusOption: Object.freeze([
        {
          dictValue: 0,
          dictLabel: '正常',
        },
        {
          dictValue: 1,
          dictLabel: '停用',
        },
      ]),
      fatherId: 0,
      expands: ['15'],
      triggerType: 'add',
      showImgPreview: false,
      previewImgs: [],
      arrtOption: [],
      treeSelectAll: [],
    };
  },
  computed: {
    subjectArrtOption() {
      // 属性标签只展示行业类型及、项目类型
      const { children = [] } = this.arrtOption[0] || [];
      const filterData = children.filter(el => el.cateId === 20 || el.cateId === 21);
      return filterData;
    },
  },
  created() {
    this.getList();
    this.getBpmFileCategoryList();
    // this.getDicts('classify_status').then(response => {
    //   this.statusOption = response.data || [];
    //   this.statusOption.forEach(item => {
    //     item.dictValue = Number(item.dictValue);
    //   });
    // });
  },
  methods: {
    getBpmFileCategoryList() {
      getBpmFileCategoryList().then(response => {
        const categoryList = this.handleTree(response.rows, 'cateId');
        // const { children = [] } = categoryList[0] || {};
        // const { children: threeChild = [] } = children[0] || {};
        // let arrtOption = threeChild[1] || {};
        // const { children: categoryChilden } = threeChild[0] || {};
        // // 文章类
        // const businessData = categoryChilden.find(el => el.cateId === 1);
        this.fatherId = categoryList.cateId;
        this.subjectTeeOptions = Object.keys(categoryList).length !== 0 ? categoryList : [];
        // this.arrtOption = Object.keys(arrtOption).length !== 0 ? [arrtOption] : [];
      });
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },

    /** 转换资源分类数据结构 */
    normalizerOpt(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.cateId,
        label: node.cateName,
        children: node.children,
      };
    },
    getCover(file) {
      const fileItme = file[0] || {};
      const { realUrl } = fileItme;
      this.form.cateCover = realUrl;
    },
    showPreview(url) {
      this.previewImgs = [url];
      this.showImgPreview = true;
    },
    getRowKeys(row) {
      return row.cateId;
    },
    /** 查询资源分类列表 */
    getList() {
      this.loading = true;
      getBpmFileCategoryList(this.queryParams)
        .then(response => {
          const categoryList = this.handleTree(response.rows, 'cateId');

          this.fatherId = categoryList.cateId;
          this.categoryList = Object.keys(categoryList).length !== 0 ? categoryList : [];
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /** 转换资源分类数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.cateId,
        label: node.cateName,
        children: node.children,
      };
    },
    /** 查询资源分类下拉树结构 */
    getTreeselect() {
      getBpmFileCategoryList().then(response => {
        this.categoryOptions = [];
        const data = { cateId: 0, cateName: '顶级节点', children: [] };
        data.children = this.handleTree(response.rows, 'cateId', 'parentId');
        const businessData = data.children[0];
        if (Object.keys(businessData).length !== 0) {
          this.categoryOptions.push(businessData);
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        cateId: null,
        parentId: null,
        ancestors: null,
        cateName: null,
        orderNum: null,
        cateCover: null,
        status: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.cateId) {
        this.form.parentId = row.cateId;
      } else {
        this.form.parentId = this.fatherId;
      }
      this.open = true;
      this.title = '添加资源分类';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      if (row != null) {
        this.form.parentId = row.cateId;
      }
      getBpmFileCategoryCateId(row.cateId).then(response => {
        this.form = response.data;
        let attrs = this.form.attrs || [];
        attrs = attrs.map(el => {
          return {
            ...el,
            cateId: el.attribute,
            cateName: el.name,
          };
        });
        this.treeSelectAll = attrs;
        this.form.attrs = attrs;
        this.open = true;
        this.title = '修改资源分类';
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const parame = deepClone(this.form);
          if (this.form.attrs && this.form.attrs.length !== 0) {
            parame.attrs = this.form.attrs || [];
            parame.attrs = parame.attrs.map(el => {
              return {
                attrId: el.attrId,
                deptId: this.form.deptId,
                attribute: el.cateId,
                name: el.cateName,
              };
            });
            parame.attrs.forEach(e => {
              for (let index = 0; index < this.treeSelectAll.length; index++) {
                const el = this.treeSelectAll[index];
                if (e.attribute === el.cateId) {
                  e.attrId = el.attrId;
                  break;
                }
              }
            });
          }

          if (this.form.cateId != null) {
            putBpmFileCategory(parame).then(response => {
              this.$message.success('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            postBpmFileCategory(parame).then(response => {
              this.$message.success('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除"' + row.cateName + '"?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(function () {
          return deleteBpmFileCategoryCateIds(row.cateId);
        })
        .then(() => {
          this.getList();
          this.msgSuccess('删除成功');
        })
        .catch(() => {});
    },
  },
};
</script>
