<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="80px">
      <el-form-item label="表单名称" prop="formName">
        <el-input
          v-model="queryParams.formName"
          clearable
          placeholder="请输入表单名称"
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['workflow:form:add']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['workflow:form:edit']"
          :disabled="single"
          icon="el-icon-edit"
          plain
          size="mini"
          type="success"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['workflow:form:remove']"
          :disabled="multiple"
          icon="el-icon-delete"
          plain
          size="mini"
          type="danger"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['workflow:form:export']"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="formList" @selection-change="handleSelectionChange">
      <el-table-column align="left" type="selection" width="55" />
      <el-table-column align="left" label="表单主键" prop="formId" />
      <el-table-column align="left" label="表单名称" prop="formName" />
      <el-table-column align="left" label="备注" prop="remark" />
      <el-table-column align="left" label="状态" prop="formStatus" width="100">
        <template slot-scope="scope">
          <span :style="'color:'+ statusMap[scope.row.formStatus].color">{{ statusMap[scope.row.formStatus].text }}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" class-name="small-padding fixed-width" width="220" label="操作">
        <template slot-scope="scope">
          <el-button icon="el-icon-view" size="mini" type="text" @click="handleDetail(scope.row)">详情</el-button>
          <el-button
            v-hasPermi="['workflow:form:edit']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['workflow:form:release']"
            icon="el-icon-s-promotion"
            size="mini"
            type="text"
            :disabled="![0].includes(scope.row.formStatus)"
            @click="handleRelease(scope.row)"
          >发布</el-button>
          <el-button
            v-hasPermi="['workflow:form:remove']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            :disabled="![0,2].includes(scope.row.formStatus)"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />

    <!-- 预览表单对话框 -->
    <el-dialog :visible.sync="renderFormOpen" append-to-body title="表单预览" width="60%">
      <v-form-render ref="vFormRef" :form-data="{}" :form-json="{}" />
    </el-dialog>
  </div>
</template>

<script>
import { listForm, delForm, releaseForm } from '@/api/workflow/form';

export default {
  name: 'Form',
  components: {},
  data() {
    return {
      statusMap: {
        0: {
          text: '草稿',
          color: '#E6A23C',
        },
        1: {
          text: '已发布',
          color: '#67C23A',
        },
        2: {
          text: '停用',
          color: '#909399',
        }
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 流程表单表格数据
      formList: [],
      // 弹出层标题
      title: '',
      designerFormOpen: false,
      renderFormOpen: false,
      formTitle: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        formName: null,
        content: null,
        // formStatus: 1,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        formName: [{ required: true, message: '表单名称不能为空', trigger: 'blur' }],
      },
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询流程表单列表 */
    getList() {
      this.loading = true;
      listForm(this.queryParams).then(response => {
        this.formList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        formId: null,
        formName: null,
        content: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        remark: null,
      };
      this.resetForm('form');
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.formId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 表单配置信息 */
    handleDetail(row) {
      this.renderFormOpen = true;
      this.$nextTick(() => {
        this.$refs.vFormRef.setFormJson(row.content || { formConfig: {}, widgetList: [] });
      });
    },

    goFormCreate(formId) {
      const query = {};
      if (formId) {
        query.formId = formId;
      }
      this.$router.push({
        path: '/carbon-flowable/process/form/create',
        query,
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.goFormCreate();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const formId = row.formId || this.ids;
      this.goFormCreate(formId);
    },
    /** 发布按钮操作 */
    handleRelease(row) {
      this.reset();
      const formId = row.formId;
      this.$modal
      .confirm('表单发布后将无法编辑，是否发布？')
      .then(async () => {
        // this.loading = false;
        const result = await releaseForm(formId);
        if (result.code === 200 ) {
          // this.loading = false;
          this.resetQuery()
          this.$modal.msgSuccess('发布成功');
        }
      })
      .finally(() => {
        // this.loading = false;
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const formIds = row.formId || this.ids;
      this.$confirm('是否确认删除流程表单编号为"' + formIds + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(function() {
          return delForm(formIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const _this = this;
      this.$confirm('是否确认导出所有流程表单数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(function() {
        _this.download(
          '/workflow/form/export',
          {
            ..._this.queryParams,
          },
          `form_${new Date().getTime()}.xlsx`,
        );
      });
    },
  },
};
</script>

<style lang="scss">
.vf-designer .el-dialog__body {
  padding: 0;
  box-sizing: border-box;
  overflow-y: auto;
}
</style>
