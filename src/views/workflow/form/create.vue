<template>
  <div class="formDesignerLayout">
    <v-form-designer
      ref="vfDesigner"
      :designer-config="{ externalLink: false, toolbarMaxWidth: 480 }"
      :reset-form-json="true"
      :property-list="propertyList"
    >
      <!-- 自定义按钮插槽 -->
      <template #customToolButtons>
        <el-button
          type="text"
          :disabled="formId && ![0].includes(form.formStatus)"
          @click="onSaveFormDesigner"
        >
          <i class="el-icon-finished" />保存
        </el-button>
        <el-button
          type="text"
          @click="onReleaseForm"
          :disabled="![0].includes(form.formStatus)"
          v-if="formId"
        >
          <i class="el-icon-s-promotion" />发布
        </el-button>
      </template>
    </v-form-designer>
    <!-- 添加或修改流程表单对话框 -->
    <el-dialog :visible.sync="open" append-to-body title="表单信息" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="表单名称" prop="formName">
          <el-input v-model="form.formName" placeholder="请输入表单名称" />
        </el-form-item>
        <el-form-item label="关联包" prop="packageName">
          <el-select
            style="width: 100%"
            filterable
            clearable
            :disabled="isSave"
            v-model="form.packageName"
            placeholder="请选择包名"
            @change="getClassList"
          >
            <el-option
              v-for="item in packageList"
              :key="item.packageName"
              :label="item.packageDispname"
              :value="item.packageName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联类" prop="className">
          <el-select
            style="width: 100%"
            :disabled="isSave"
            clearable
            filterable
            v-model="form.className"
            placeholder="请选择类"
          >
            <el-option
              v-for="item in classList"
              :key="item.className"
              :label="item.classDispname"
              :value="item.className"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm()">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listForm, getForm, delForm, addForm, updateForm, releaseForm } from '@/api/workflow/form';
import { getIotPackagesList } from '@/api/iot/v2/packages/packages-controller';
import { getIotClassesList } from '@/api/iot/v2/classes/classes-controller';
import { getIotPropertiesList } from '@/api/iot/v2/properties/classes-property-controller';
export default {
  components: {},
  data() {
    return {
      loading: false,
      formId: null,
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {
        className: null,
      },
      // 表单校验
      rules: {
        formName: [{ required: true, message: '表单名称不能为空', trigger: 'blur' }],
      },
      packageList: [],
      classList: [],
      isSave: false,
      propertyList: [],
    };
  },
  computed: {},
  created() {
    this.formId = this.$route.query.formId;
    this.resetForm('form');
    this.getPackageList();
  },
  mounted() {
    this.init();
  },
  methods: {
    getPackageList() {
      getIotPackagesList({
        pageNum: 1,
        pageSize: 9999,
      }).then(response => {
        this.packageList = response.rows || [];
      });
    },

    getClassList(packageName) {
      this.form.className = null;
      getIotClassesList({
        pageNum: 1,
        pageSize: 9999,
        packageName,
      }).then(response => {
        this.classList = response.rows || [];
      });
    },
    getPropertyList(className) {
      if (!className) return
      getIotPropertiesList({
        className: className,
        pageNum: 1,
        pageSize: 9999,
      }).then(response => {
        this.propertyList = response.rows || [];
      });
    },
    onReleaseForm() {
      if (this.loading) return;
      this.loading = true;
      this.$modal
        .confirm('表单发布后将无法编辑，是否发布？')
        .then(async () => {
          this.loading = false;
          const result = await releaseForm(this.formId);
          if (result.code === 200) {
            this.loading = false;
            this.$modal.msgSuccess('发布成功');
            this.$router.go(-1);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    onSaveFormDesigner() {
      this.open = true;
      this.isSave = true;
    },
    init() {
      // 清理表单设计器
      this.clearDesigner();
      if (!this.formId) {
        this.open = true;
        return;
      }
      getForm(this.formId).then(response => {
        this.form = response.data;
        this.$nextTick(() => {
          const { content } = this.form;
          if (content) {
            this.$refs.vfDesigner.setFormJson(content, this.form.formName);
          }
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      if (!this.formId && !this.isSave) {
        this.open = false;
        this.getPropertyList(this.form.className);
        return;
      }

      this.$refs['form'].validate(valid => {
        if (valid) {
          const { getFormJson, getFormIotJson } = this.$refs.vfDesigner;
          const formJson = getFormJson();
          const formIotJson = getFormIotJson(this.form.formName);

          this.form.content = JSON.stringify(formJson);
          // this.form.iotJson = JSON.stringify(formIotJson);
          this.form.classContent = JSON.stringify(formIotJson);

          if (this.form.formId != null) {
            updateForm(this.form).then(response => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.designerFormOpen = false;
              this.$router.go(-1);
              // this.getList();
            });
          } else {
            addForm(this.form).then(response => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.designerFormOpen = false;
              this.$router.go(-1);
              // this.getList();
            });
          }
        }
      });
    },
    clearDesigner() {
      this.$nextTick(() => {
        this.$refs.vfDesigner.clearDesigner();
      });
    },
  },
};
</script>
<style scoped lang="scss">
.formDesignerLayout {
  padding-top: 4px;
  .main-container {
    margin-left: 0 !important;
  }
}
</style>
