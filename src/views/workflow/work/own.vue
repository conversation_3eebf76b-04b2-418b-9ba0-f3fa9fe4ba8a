<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="流程标识" prop="processKey">
        <el-input
          v-model="queryParams.processKey"
          placeholder="请输入流程标识"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程名称" prop="processName">
        <el-input
          v-model="queryParams.processName"
          placeholder="请输入流程名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程分类" prop="category">
        <el-select v-model="queryParams.category" clearable placeholder="请选择" size="small">
          <el-option
            v-for="item in categoryOptions"
            :key="item.categoryId"
            :label="item.categoryName"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提交时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['workflow:process:ownExport']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="ownProcessList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="流程编号"
        align="center"
        prop="procInsId"
        min-width="120"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="流程名称"
        align="center"
        prop="procDefName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="流程类别"
        align="center"
        prop="category"
        :formatter="categoryFormat"
      />
      <el-table-column label="流程版本" align="center" width="80px">
        <template slot-scope="scope">
          <el-tag size="medium">v{{ scope.row.procDefVersion }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="当前节点" align="center" prop="taskName"  width="120" />
      <el-table-column label="提交时间" align="center" prop="createTime" width="180" />
      <el-table-column label="流程状态" align="center" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.wf_process_status" :value="scope.row.processStatus" />
        </template>
      </el-table-column>
      <el-table-column label="耗时" align="center" prop="duration" width="140" />
      <el-table-column label="操作" align="left" width="280" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['workflow:process:query']"
            type="text"
            size="mini"
            icon="el-icon-tickets"
            @click="handleFlowRecord(scope.row)"
            >详情</el-button
          >
          <el-button
            v-if="scope.row.finishTime"
            v-hasPermi="['workflow:process:remove']"
            type="text"
            size="mini"
            icon="el-icon-view"
            @click="handlePreview(scope.row)"
            >预览</el-button
          >
          <el-button
            v-if="scope.row.finishTime"
            v-hasPermi="['workflow:process:remove']"
            type="text"
            size="mini"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
          <el-button
            v-hasPermi="['workflow:process:cancel']"
            type="text"
            size="mini"
            icon="el-icon-circle-close"
            @click="handleStop(scope.row)"
            >取消</el-button
          >
          <el-button
            v-hasPermi="['workflow:process:start']"
            type="text"
            size="mini"
            icon="el-icon-refresh-right"
            @click="handleAgain(scope.row)"
            >重新发起</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <form-preview
      :visible.sync="previewVisible"
      :proc-node-list="procNodeList"
      :form-data="previewformData"
    />
  </div>
</template>

<script>
import { listOwnProcess, stopProcess, delProcess } from '@/api/workflow/process';
import { listAllCategory } from '@/api/workflow/category';
import FormPreview from './components/formPreview';
import { detailProcess } from '@/api/workflow/process';
import { handleDynamicsForm } from '@/utils/formRender.js';
export default {
  name: 'Own',
  dicts: ['wf_process_status'],
  components: {
    FormPreview,
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.getList();
    });
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      processLoading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      categoryOptions: [],
      processTotal: 0,
      // 我发起的流程列表数据
      ownProcessList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      src: '',
      definitionList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        processKey: undefined,
        processName: undefined,
        category: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},

      previewVisible: false,
      // 预览表单数据
      previewformData: {},
      // 流程审核数据
      procNodeList: [],
    };
  },
  created() {
    this.getCategoryList();
  },
  methods: {
    async handlePreview(row) {
      if (this.loading) return;
      this.loading = true;

      const { procInsId, taskId } = row;
      const params = { procInsId, taskId };
      try {
        const { data = {} } = await detailProcess(params);
        // TODO: 需关注多表单情况
        // 暂时取第一个表单
        let processFormList = [data.processFormList[0]];
        processFormList = await processFormList.reduce(async (accPromise, item) => {
          const acc = await accPromise;
          const processedElement = await handleDynamicsForm(
            item.formModel,
            item.formData.formDataPath,
            false,
          );
          acc.push({
            ...item,
            formModel: processedElement,
          });
          return acc;
        }, Promise.resolve([]));

        this.previewformData = processFormList[0];
        this.previewformData = processFormList[0];

        const historyProcNodeList = data.historyProcNodeList
          .reverse()
          .reduce((previous, current) => {
            const arr = previous ? previous : [];
            if (
              current.comments &&
              current.comments[0].userSign &&
              arr.findIndex(item => item.activityName === current.activityName) === -1
            ) {
              arr.push(current.comments);
              return arr;
            } else {
              return arr;
            }
          }, []);

        this.procNodeList = historyProcNodeList;

        this.previewVisible = true;
        this.loading = false;
      } catch (error) {
        console.log('error', error)
        this.previewVisible = false;
        this.loading = false;
      }
    },

    /** 查询流程分类列表 */
    getCategoryList() {
      listAllCategory().then(response => (this.categoryOptions = response.data));
    },
    /** 查询流程定义列表 */
    getList() {
      this.loading = true;
      listOwnProcess(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.ownProcessList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        category: null,
        key: null,
        tenantId: null,
        deployTime: null,
        derivedFrom: null,
        derivedFromRoot: null,
        parentDeploymentId: null,
        engineVersion: null,
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.procInsId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleAgain(row) {
      this.$router.push({
        path: '/carbon-flowable/work/start',
        query: {
          deployId: row.deployId,
          definitionId: row.procDefId,
          procInsId: row.procInsId,
        },
      });
      console.log(row);
    },
    /**  取消流程申请 */
    handleStop(row) {
      const params = {
        procInsId: row.procInsId,
      };
      stopProcess(params).then(res => {
        this.$modal.msgSuccess(res.msg);
        this.getList();
      });
    },
    /** 流程流转记录 */
    handleFlowRecord(row) {
      this.$router.push({
        path: '/carbon-flowable/work/detail',
        query: {
          procInsId: row.procInsId,
          processed: false,
        },
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.procInsId || this.ids;
      this.$confirm('是否确认删除流程定义编号为"' + ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(function () {
          return delProcess(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'workflow/process/ownExport',
        {
          ...this.queryParams,
        },
        `wf_own_process_${new Date().getTime()}.xlsx`,
      );
    },
    categoryFormat(row, column) {
      return this.categoryOptions.find(k => k.code === row.category)?.categoryName ?? '';
    },
  },
};
</script>
