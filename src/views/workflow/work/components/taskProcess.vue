<template>
  <div class="">
    <el-divider class="dividerCls" content-position="left">审批流程</el-divider>
    <el-timeline>
      <el-timeline-item
        v-for="(item, index) in activities"
        :key="index"
        placement="top"
        :timestamp="item.content"
      >
        <template v-if="item.showUser">
          <div>
            <el-button
              v-if="item.showAdd"
              class="button-new-tag"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              circle
              @click="openSelectUser(index)"
            />
            <el-tag
              v-for="(useItem, uIndex) in item.userList"
              :key="uIndex"
              :closable="item.showAdd"
              style="margin-right: 10px"
              :disable-transitions="false"
              @close="handleClose(uIndex)"
            >
              {{ useItem.nickName }}
            </el-tag>
          </div>
        </template>
      </el-timeline-item>
    </el-timeline>

    <user-dialog
      :visible.sync="visible"
      :selectUsers="selectActItem.userList || []"
      :isMultiple="selectActItem.isMultiple"
      @submit="handleUser"
    ></user-dialog>
  </div>
</template>
<script>
import convert from 'xml-js';
import { getBpmnXml } from '@/api/workflow/process';
import UserDialog from './userDialog';
import { getUserProfile } from '@/api/system/user';
export default {
  name: 'TaskProcess',
  components: {
    UserDialog,
  },
  props: {
    modelId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      activities: [
        {
          content: '发起',
        },
        {
          content: '结束',
        },
      ],
      visible: false,
      selectIndex: 0,
      userInfo: null,
    };
  },
  computed: {
    selectActItem() {
      return this.selectIndex !== 0 ? this.activities[this.selectIndex] : [];
    },
  },
  async mounted() {
    await this.getUserInfo();
    this.handlerXml();
  },
  methods: {
    async getUserInfo() {
      const res = await getUserProfile();
      if (res.code === 200) {
        this.userInfo = res.data || {};
      }
    },
    getUserJson() {
      const cloneAct = JSON.parse(JSON.stringify(this.activities));
      return cloneAct.splice(1, this.activities.length - 2);
    },
    async handlerXml() {
      const result = await getBpmnXml(this.modelId);
      const { code, data: xml } = result || {};
      if (code === 200) {
        // xml-js 文档地址： https://www.npmjs.com/package/xml-js
        const bpmnJson = convert.xml2js(xml, { compact: true, spaces: 2 });
        console.log('object :>>userTask.map  ', bpmnJson);
        // 从 bpmn 中拿出用户事件，发起流程时生成 审批流程
        let userTask = bpmnJson['bpmn2:definitions']['bpmn2:process']['bpmn2:userTask'] || '';
        userTask = Array.isArray(userTask) ? userTask : [userTask];
        const formatUserTask = userTask?.map?.(item => {
          const bpmnAttr = item['_attributes'] || {};
          let id = bpmnAttr.id;
          let showAdd = false;
          let dataType = bpmnAttr['flowable:dataType'];
          console.log('object :>> bpmnAttr123', bpmnAttr, bpmnAttr['flowable:selectType']);

          let userList = [];
          if (dataType === 'USERS') {
            const userIds = (
              bpmnAttr['flowable:candidateUsers'] ||
              bpmnAttr['flowable:assignee'] ||
              ''
            ).split(',');
            const userNames = (bpmnAttr['flowable:text'] || '').split(',');
            userList = userNames.map((item, index) => {
              return {
                nickName: item,
                userId: Number(userIds[index]),
              };
            });
          }

          if (['ROLES', 'DEPTS'].includes(dataType)) {
            const flowableText = bpmnAttr['flowable:text'].split(',');
            const flowableIds = bpmnAttr['flowable:candidateGroups'].split(',');
            userList = flowableText.map((item, index) => {
              return {
                nickName: item,
                userId: flowableIds[index],
              };
            });
          }

          if (['INITIATOR'].includes(dataType)) {
            const { nickName, userId } = this.userInfo || {};
            userList = [
              {
                nickName,
                userId,
              },
            ];
          }

          if (['INITIATOR_OPTIONAL'].includes(dataType)) {
            id = `${bpmnAttr.id}_userId`;
            showAdd = true;
          }

          return {
            id,
            dataType,
            content: bpmnAttr.name || '审批',
            showUser: true,
            isMultiple: bpmnAttr['flowable:selectType']
              ? bpmnAttr['flowable:selectType'] === 'MULTIPLE'
              : true,
            userList: userList,
            showAdd,
          };
        });
        this.activities.splice(1, 0, ...formatUserTask);
        console.log('object :>> ', this.activities);
      }
    },

    handleClose(index) {
      this.activities[this.selectIndex].userList.splice(index, 1);
    },

    handleUser(selectUser) {
      this.activities[this.selectIndex].userList = selectUser;
    },

    openSelectUser(index) {
      this.selectIndex = index;
      this.visible = true;
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .el-timeline-item__timestamp {
  font-size: 14px;
}

.button-new-tag {
  margin-right: 10px;
}

.dividerCls {
  margin: 56px 0 48px;
}
</style>
