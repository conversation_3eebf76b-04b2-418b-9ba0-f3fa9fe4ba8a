<template>
  <el-dialog
    title="表单预览"
    :visible.sync="dialogVisible"
    width="834px"
    :before-close="handleClose"
  >
    <div class="form-preview-content" ref="previewContent">
      <!-- 这里放置表单预览的具体内容 -->
      <v-form-render
        ref="taskFormParser"
        :isPreview="true"
        :form-json="formData.formModel"
        :form-data="formData.formData"
        :proc-node-list="procNodeList"
      />
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleExportPDF" v-loading="loading">导出PDF</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import ExportPdf from '@/mixin/exportPdf';
export default {
  name: 'FormPreview',
  mixins: [ ExportPdf ],
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
    procNodeList: {
      type: Array,
      default: () => [],
    },
    // 添加PDF文件名属性
    pdfName: {
      type: String,
      default: '表单预览',
    },
  },
  data() {
    return {
      loading: false,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    handleConfirm() {
      this.$emit('confirm');
      this.handleClose();
    },
    handleExportPDF() {
      const element = this.$refs.previewContent;
      this.exportPDF(element)
    },
  },
};
</script>

<style lang="scss" scoped>
.form-preview-content {
  // height: 75vh;
  // overflow-y: auto;
  padding: 0;
  white-space: pre-wrap; // 保留空格和换行
  word-wrap: break-word; // 允许长单词换行
  word-break: break-all; // 在任意字符间断行

  .preview-item {
    margin-bottom: 15px;
    page-break-inside: avoid;
    // 添加以下样式
    max-width: 100%;
    overflow-wrap: break-word;
  }

  .label {
    font-weight: bold;
    margin-right: 10px;
  }

  .value {
    color: #666;
  }

  .td {
    min-width: 60px;
    text-align: center;
    word-wrap: break-word;
    word-break: break-all;
    //border: 1px solid #000;
  }
}

::v-deep .el-dialog__body {
  padding: 0 20px;
}
</style>
