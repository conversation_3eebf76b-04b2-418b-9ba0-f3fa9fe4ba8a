<template>
  <el-dialog
    :title="userData.title"
    :visible.sync="dialogvVsible"
    width="66%"
    append-to-body
    destroy-on-close
    @close="handlerClose"
  >
    <el-row type="flex" :gutter="20">
      <!--部门数据-->
      <el-col :span="5">
        <el-card shadow="never" style="height: 100%">
          <div slot="header">
            <span>部门列表</span>
          </div>
          <div class="head-container">
            <el-input
              v-model="deptName"
              placeholder="请输入部门名称"
              clearable
              size="small"
              prefix-icon="el-icon-search"
            />
            <el-tree
              ref="tree"
              :data="deptOptions"
              :props="deptProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              default-expand-all
              @node-click="handleNodeClick"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-table
          v-if="dialogvVsible"
          ref="userTable"
          :key="userData.type"
          v-loading="userLoading"
          height="500"
          :data="userList"
          highlight-current-row
          @current-change="changeCurrentUser"
          @selection-change="handleSelectionChange"
        >
          <el-table-column width="55" type="selection" v-if="isMultiple" />
          <el-table-column v-else width="30">
            <template slot-scope="scope">
              <el-radio v-model="currentUserId" :label="scope.row.userId">{{ '' }}</el-radio>
            </template>
          </el-table-column>
          <el-table-column label="用户名" align="center" prop="nickName" />
          <el-table-column label="手机" align="center" prop="phonenumber" />
          <el-table-column label="部门" align="center" prop="deptName" />
        </el-table>
        <pagination
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handlerClose">取 消</el-button>
      <el-button type="primary" @click="submitUserData">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { listUser, deptTreeSelect } from '@/api/system/user';
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    selectUsers: {
      type: Array,
      default: [],
    },
    isMultiple: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      currentUserId: null,
      nextUser: [],
      // 部门名称
      deptName: undefined,
      // 部门树选项
      deptOptions: undefined,
      userLoading: false,
      userData: {
        title: '',
        type: '',
        open: false,
      },
      // 用户表格数据
      userList: null,
      deptProps: {
        children: 'children',
        label: 'label',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: undefined,
      },
      total: 0,
      taskForm: {
        comment: '', // 意见内容
        procInsId: '', // 流程实例编号
        taskId: '', // 流程任务编号
        copyUserIds: '', // 抄送人Id
        vars: '',
        targetKey: '',
      },
      userMultipleSelection: [],
      dialogvVsible: false,
    };
  },
  watch: {
    visible(val) {
      this.dialogvVsible = val
      if (val) {
        this.onSelectUsers('指定审批人');
        if (!!this.selectUsers.length) {
          this.userMultipleSelection = this.selectUsers
          if (!this.isMultiple) {
             this.currentUserId = this.selectUsers[0].userId
          }
        }
      }
    }
  },
  methods: {
    handlerClose() {
      this.$emit('update:visible', false);
      this.$refs.userTable.clearSelection()
      this.userMultipleSelection = []
      this.currentUserId = ''
    },
    onSelectUsers(title, type) {
      this.userData.title = title;
      this.userData.type = type;
      this.getTreeSelect();
      this.getList();
      this.userData.open = true;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.userMultipleSelection = selection;
    },
    changeCurrentUser(val) {
      this.userMultipleSelection = [val]
      this.currentUserId = val.userId;
    },
    /** 查询部门下拉树结构 */
    getTreeSelect() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    toggleSelection(selection) {
      if (selection && selection.length > 0) {
        this.$nextTick(() => {
          selection.forEach(item => {
            const row = this.userList.find(k => k.userId === item.userId);
            this.$refs.userTable.toggleRowSelection(row);
          });
        });
      } else {
        this.$nextTick(() => {
          this.$refs.userTable.clearSelection();
        });
      }
    },
    /** 查询用户列表 */
    getList() {
      this.userLoading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.toggleSelection(this.userMultipleSelection);
        this.userLoading = false;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.getList();
    },
    submitUserData() {
      this.$emit('submit', this.userMultipleSelection);
      this.handlerClose()
    },
  },
};
</script>
<style scoped lang="scss"></style>
