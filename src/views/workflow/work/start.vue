<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>发起流程</span>
      </div>
      <div v-if="formOpen" class="form-conf">
        <v-form-render ref="vFormRef" :form-json="formModel" />

        <TaskProcess :modelId="query.definitionId" ref="taskProcess"></TaskProcess>
        <div class="cu-submit">
          <el-button type="primary" :loading="submitLoading"  @click="submit">提交</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getProcessForm, startProcess } from '@/api/workflow/process';
import { handleDynamicsForm, getIndex } from '@/utils/formRender.js';
import TaskProcess from './components/taskProcess';

export default {
  name: 'WorkStart',
  components: {
    TaskProcess,
  },
  data() {
    return {
      formOpen: false,
      formModel: {},
      formData: {},
      query: {},
      submitLoading: false,
    };
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      this.query = this.$route.query;
      getProcessForm({
        definitionId: this.query.definitionId,
        deployId: this.query.deployId,
        procInsId: this.query.procInsId,
      }).then(async response => {
        const data = response.data;
        const formModel = data.formModel || { formConfig: {}, widgetList: [] };
        this.formModel = await handleDynamicsForm(formModel, this.query.formDataPath);

        console.log('object :>>  this.formModel ',  this.formModel );

        this.formData = data.formData || {};
        this.formOpen = true;
        this.$nextTick(() => {
          this.$refs.vFormRef.setFormJson(this.formModel);
        });
      });
    },
    submit() {
      let userTaskData = this.$refs.taskProcess.getUserJson();
      userTaskData = userTaskData.filter(item => item.dataType === 'INITIATOR_OPTIONAL');
      if (userTaskData.some(item => !item.userList.length)) {
        this.$message.error('请选择审批人员');
        return;
      }
      if (this.submitLoading) return
      this.submitLoading = true;
      this.$refs.vFormRef
        .getFormData()
        .then(formData => {
          if (this.query.definitionId) {
            const userMap = {};
            userTaskData.forEach(item => {
              let ids = item.userList.map(item => String(item.userId));
              if (!item.isMultiple) {
                ids = ids[0];
              } else {
                ids = JSON.stringify(ids);
              }
              userMap[item.id] = ids;
            });

            // 业务组件-检验项目 传值特殊处理
            let widgetList = this.formModel.widgetList || [];

            const standardTableIndex = getIndex(widgetList, 'standardTable')
            if (standardTableIndex !== -1) {
              widgetList = widgetList[standardTableIndex].widgetList
            }
            const inspectionItemsIndex = getIndex(widgetList, 'inspectionItemsTable')

            let items = [];
            if (inspectionItemsIndex !== -1) {
              const inspectionItems = widgetList[inspectionItemsIndex] || [];
              inspectionItems.rows.map((rowItem, rowIndex) => {
                const rows = {};
                // 忽略表头
                if (rowIndex === 0) return;
                rowItem.cols.map(item => {
                  const widgetItem = item.widgetList[0];
                  // TODO: 此处需要根据字段处理
                  if (['input','textarea'].includes(widgetItem.type)) {
                    const key = widgetItem.id.split('-')[0];
                    rows[key] = formData[widgetItem.id];
                    rows.itemId = widgetItem.options.itemId;
                  }
                });
                items.push(rows);
              });
            }

            if (!!items.length) {
              formData.items = items;
            }

            // 地址栏参数回传
            const queryKeys = Object.keys(this.query)
            const checkQuery = ['fdId', 'formDataPath', 'processService', 'cateId', 'classId', 'className']
            queryKeys.forEach(item => {
              if (checkQuery.includes(item)) {
                formData[item] = this.query[item];
              }
            })

            // console.log('object :>>  formData.items',  formData);

            // return

            // 启动流程并将表单数据加入流程变量
            startProcess(
              this.query.definitionId,
              JSON.stringify({
                ...formData,
                ...userMap,
              }),
            ).then(res => {
              this.$modal.msgSuccess(res.msg);
              this.$tab.closeOpenPage({
                path: '/carbon-flowable/work/own',
              });
            });
          }
          this.submitLoading = false;
        })
        .catch(err => {
          this.submitLoading = false;
          this.$modal.msgError(err);
        });
    },
    reset() {
      this.$refs.vFormRef.resetForm();
    },
  },
};
</script>

<style lang="scss" scoped>
.form-conf {
  margin: 15px auto;
  width: 80%;
  /*padding: 15px;*/
}

.cu-submit {
  margin-top: 15px;
  text-align: center;
}
</style>
