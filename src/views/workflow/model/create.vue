<template>
  <process-designer
    :key="designerOpen"
    ref="modelDesigner"
    v-loading="designerData.loading"
    :bpmn-xml="designerData.bpmnXml"
    :designer-form="designerData.form"
    style="border: 1px solid rgba(0, 0, 0, 0.1)"
    @save="onSaveDesigner"
  />
</template>
<script>
import ProcessDesigner from '@/components/ProcessDesigner';
import {
  getBpmnXml,
  saveModel,
} from '@/api/workflow/model';
export default {
  components: {
    ProcessDesigner
  },
  data() {
    return {
      designerOpen: false,
      designerData: {
        loading: false,
        bpmnXml: '',
        modelId: null,
        form: {
          processName: null,
          processKey: null,
        },
      },
    };
  },
  computed: {
  },
  created() {
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      const { modelName, modelId, modelKey } = this.$route.query;
      this.designerData.title = '流程设计 - ' + modelName;
      this.designerData.modelId = modelId;
      this.designerData.form = {
        processName: modelName,
        processKey: modelKey,
      };
      if (modelId) {
        this.designerData.loading = true;
        getBpmnXml(modelId).then(response => {
          this.designerData.bpmnXml = response.data || '';
          this.designerData.loading = false;
          this.designerOpen = true;
        });
      }
    },

    onSaveDesigner(bpmnXml) {
      this.bpmnXml = bpmnXml;
      const dataBody = {
        modelId: this.designerData.modelId,
        bpmnXml: this.bpmnXml,
      };
      this.$confirm('是否将此模型保存为新版本？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '是',
        cancelButtonText: '否',
      })
        .then(() => {
          this.confirmSave(dataBody, true);
        })
        .catch(action => {
          // if (action === 'cancel') {
          //   this.confirmSave(dataBody, false);
          // }
        });
    },
    confirmSave(body, newVersion) {
      this.designerData.loading = true;
      saveModel(
        Object.assign(body, {
          newVersion: newVersion,
        }),
      )
        .then(() => {
          this.$router.go(-1);
        })
        .finally(() => {
          this.designerData.loading = false;
        });
    },
  },
};
</script>
<style scoped lang='scss'>
</style>
