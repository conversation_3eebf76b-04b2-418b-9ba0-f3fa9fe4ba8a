<template>
  <div class="app-container">
    <CoustomTable
      :page-buttons-json="pageButtonsJson"
      :paginations="defaultQueryParams"
      :query-form-json="queryFormJson"
      :table-data="tableData"
      :table-json="tableJson"
      :total="total"
      @onButton="handlerButton"
      @onPageButton="handlerPageButton"
      @onPagination="handlerPagination"
      @onSearch="handlerSearch"
      @onTableRowClick="handlerTableRowClick"
    />

    <CoustomFormDialog
      ref="formDialog"
      :dialog-json="dialogJson"
      :init-data="initFormData"
      :visible.sync="dialogVisible"
      @onDialogClose="handlerDialogClose"
      @onSubmit="handlerSubmit"
    />
  </div>
</template>
<script>
import CoustomTable from '@/components/CoustomTable';
import CoustomFormDialog from '@/components/CoustomFormDialog';
import { Table<PERSON><PERSON>, Query<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Dialog<PERSON><PERSON> } from './constants';
import {
  getDevApiUiFormDesigner<PERSON>ist,
  postDevApiUiFormDesigner,
  getDevApiUiFormDesignerFormId,
  deleteDevApiUiFormDesignerFormId,
  putDevApiUiFormDesigner,
  getDevApiUiFormDesignerReleaseFormId,
  getDevApiUiFormDesignerStopFormId,
} from '@/api/ui/form-designer-controller.js';

import pageMixin from '@/mixin/page';
export default {
  name: 'FormList',
  components: {
    CoustomTable,
    CoustomFormDialog,
  },
  mixins: [pageMixin],
  dicts: ['iot_class_type', 'iot_access_type'],
  provide() {
    return {
      dictData: this.dict,
    };
  },
  data() {
    return {
      queryFormJson: QueryFormJson,
      pageButtonsJson: PageButtonsJson,
      tableJson: TableJson,
      dialogJson: DialogJson,
      associationFields: ['packageName'],
    };
  },
  computed: {
    packageName() {
      const { query } = this.$route;
      return query.packageName;
    },
  },
  created() {},
  mounted() {
    this.getList({ packageName: this.packageName }, getDevApiUiFormDesignerList);
  },

  methods: {
    async handlerSubmit(formData) {
      this.handlerSave({
        formData,
        queryParams: { packageName: this.packageName },
        apis: {
          addApi: postDevApiUiFormDesigner,
          editApi: putDevApiUiFormDesigner,
        },
        editParams: ['formId'],
      });
    },

    handlerTableRowClick(item) {
      this.goToPage({
        path: '/carbon-iot/packages/classes/detail',
        query: {
          className: item.className,
        },
      });
    },

    onAdd() {
      this.goToPage({
        path: '/form/custom/create',
      });
    },

    // 新增按钮事件
    handlerPageButton(item) {
      this.pageButton({ item, onAdd: this.onAdd() });
    },

    async getInfo(id) {
      this.getInfoData({ id, api: getDevApiUiFormDesignerFormId });
    },

    deleteItem(id) {
      this.deleteTableItem({ id, api: deleteDevApiUiFormDesignerFormId });
    },

    onEdit(row) {
      this.goToPage({
        path: '/form/custom/create',
        query: {
          formId: row.formId,
        },
      });
    },

    async onLive(row) {
      const { code } = await getDevApiUiFormDesignerReleaseFormId(row.formId);
      if (code === 200) {
        this.$message.success('发布成功！');
        this.getList({ packageName: this.packageName }, getDevApiUiFormDesignerList);
      }
    },

    async onStop(row) {
      const { code } = await getDevApiUiFormDesignerStopFormId(row.formId);
      if (code === 200) {
        this.$message.success('停止成功！');
        this.getList({ packageName: this.packageName }, getDevApiUiFormDesignerList);
      }
    },

    async onInfo(row) {
      this.goToPage({
        path: '/form/custom/info',
        query: {
          formId: row.formId,
        },
      });
    },

    handlerButton(btnItem) {
      this.tableButton({
        btnItem,
        idKey: 'formId',
        onEdit: this.onEdit,
        onLive: this.onLive,
        onStop: this.onStop,
        onInfo: this.onInfo,
      });
    },
  },
};
</script>
<style scoped lang="scss"></style>
