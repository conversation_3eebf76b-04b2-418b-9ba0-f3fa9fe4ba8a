import { getDevApiIotClassesList } from '@/api/iot/classes-controller.js';

export const fields = {
  status: {
    label: '表单状态',
    prop: 'status',
  },

  updateTime: {
    label: '更新时间',
    prop: 'updateTime',
  },

  dispName: {
    label: '表单名称',
    prop: 'dispName',
  },
  className: {
    label: '类英文名称',
    prop: 'className',
  },

  classesType: {
    label: '类类型',
    prop: 'classesType',
  },

  classesExtends: {
    label: '继承父类code',
    prop: 'classesExtends',
  },

  accessType: {
    label: '访问类型',
    prop: 'accessType',
  },

  createTime: {
    label: '创建时间',
    prop: 'createTime',
  },

  viewStyle: {
    label: '视图样式',
    prop: 'viewStyle',
  },

  remark: {
    label: '描述',
    prop: 'remark',
  },
};

export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: 'auto',
    labelPosition: 'left',
    size: 'small',
  },
  formItemJson: [
    {
      label: fields.dispName.label,
      prop: fields.dispName.prop,
      type: 'Input',
      isEnter: true,
      options: {
        clearable: true,
        placeholder: '请输入' + fields.dispName.label,
      },
    },
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary',
        },
      },
      {
        label: '重置',
        type: 'Reset',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini',
        },
      },
    ],
  },
};

export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: '',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};

export const TableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号',
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.dispName.label,
        prop: fields.dispName.prop,
        showOverflowTooltip: true,
        isLink: false,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.status.label,
        prop: fields.status.prop,
        showOverflowTooltip: true,
      },
      // {
      //   align: "left",
      //   headerAlign: "left",
      //   label: "描述",
      //   prop: "remark",
      //   showOverflowTooltip: true,
      // },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.createTime.label,
        prop: fields.createTime.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.updateTime.label,
        prop: fields.updateTime.prop,
        showOverflowTooltip: true,
      },
      {
        align: 'center',
        headerAlign: 'center',
        label: '操作',
        type: 'func',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: '',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text',
            },
            hiddenOptions: {
              prop: fields.status.prop,
              values: ['0'],
            },
          },
          {
            label: '详情',
            type: 'Info',
            permi: '',
            hiddenOptions: {
              prop: fields.status.prop,
              values: ['0', '1', '2'],
            },
            options: {
              icon: 'el-icon-tickets',
              size: 'mini',
              type: 'text',
            },
          },
          {
            label: '发布',
            type: 'Live',
            permi: '',
            hiddenOptions: {
              prop: fields.status.prop,
              values: ['0'],
            },
            options: {
              icon: 'el-icon-position',
              size: 'mini',
              type: 'text',
            },
          },
          {
            label: '停止',
            type: 'Stop',
            permi: '',
            hiddenOptions: {
              prop: fields.status.prop,
              values: ['1'],
            },
            options: {
              icon: 'el-icon-remove-outline',
              size: 'mini',
              type: 'text',
            },
          },
          {
            label: '删除',
            type: 'Delete',
            permi: '',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text',
            },
          },
        ],
      },
    ],
  },
};

export const InputDialogDialogJson = {
  title: '继承父类',
  api: getDevApiIotClassesList,
  options: {
    width: '980px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  tableConfig: {
    queryFormJson: QueryFormJson,
    pageButtonsJson: {},
    tableJson: {
      columnJson: {
        showSelect: false,
        showIndex: false,
        selectIdKey: 'className',
        data: [
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.dispName.label,
            prop: fields.dispName.prop,
            showOverflowTooltip: true,
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.className.label,
            prop: fields.className.prop,
            showOverflowTooltip: true,
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.classesType.label,
            prop: fields.classesType.prop,
            showOverflowTooltip: true,
            dictType: 'iot_class_type',
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.accessType.label,
            prop: fields.accessType.prop,
            showOverflowTooltip: true,
            dictType: 'iot_access_type',
          },
          {
            align: 'left',
            headerAlign: 'left',
            label: fields.createTime.label,
            prop: fields.createTime.prop,
            showOverflowTooltip: true,
          },
        ],
      },
    },
  },
};

export const DialogJson = {
  type: 'Add',
  title: '类',
  options: {
    width: '600px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary',
      },
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: '',
      },
    },
  ],
  formJson: {
    // 表单设置
    formOption: {
      labelPosition: 'top',
      inline: false,
      labelWidth: '120px',
      size: 'small',
    },
    // 默认表单值
    defaultFormData: {
      classesType: 'NOT',
      accessType: 'public',
    },
    // 表单项
    formItemJson: [
      {
        label: fields.dispName.label,
        prop: fields.dispName.prop,
        type: 'Input',
        rules: [
          {
            required: true,
            message: '请输入' + fields.dispName.label,
            trigger: 'blur',
          },
        ],
        options: {
          clearable: true,
          placeholder: '请输入' + fields.dispName.label,
        },
      },
      {
        label: fields.className.label,
        prop: fields.className.prop,
        type: 'Input',
        rules: [
          {
            required: true,
            message: '请输入' + fields.className.label,
            trigger: 'blur',
          },
          {
            required: true,
            validator: (rule, value, callback) => {
              const reg = /^[a-zA-Z]{1}\w*$/;
              if (!reg.test(value)) {
                callback(new Error('请输入以字母(不区分大小写)、数字、下划线(_)随意组合的字符串'));
              }
              callback();
            },
            trigger: ['blur'],
          },
        ],
        options: {
          clearable: true,
          placeholder: '请输入' + fields.className.label,
        },
      },
      {
        label: fields.classesType.label,
        prop: fields.classesType.prop,
        type: 'Select',
        rules: [
          {
            required: true,
            message: '请选择' + fields.classesType.label,
            trigger: 'change',
          },
        ],
        options: {
          clearable: true,
          placeholder: '请选择' + fields.classesType.label,
        },
        dictType: 'iot_class_type',
        option: [],
        association: [
          {
            //  whiteValue: ['EXTENDS'], / 当前字段满足此值时，显示关联字段配置
            blackValue: ['NOT'], // 当前字段不满足此值时，隐藏关联字段字段配置
            childItems: [
              {
                label: fields.classesExtends.label,
                prop: fields.classesExtends.prop,
                type: 'InputDialog',
                rules: [
                  {
                    required: true,
                    message: '请输入' + fields.classesExtends.label,
                    trigger: 'blur',
                  },
                ],
                options: {
                  clearable: true,
                  placeholder: '请输入' + fields.classesExtends.label,
                },
                dialogJson: InputDialogDialogJson,
              },
            ],
          },
        ],
      },
      {
        label: fields.accessType.label,
        prop: fields.accessType.prop,
        type: 'Select',
        rules: [
          {
            required: true,
            message: '请选择' + fields.accessType.label,
            trigger: 'change',
          },
        ],
        options: {
          clearable: true,
          placeholder: '请选择' + fields.accessType.label,
        },
        dictType: 'iot_access_type',
        option: [],
      },
      {
        label: fields.viewStyle.label,
        prop: fields.viewStyle.prop,
        type: 'Input',
        rules: [
          {
            required: false,
            message: '请输入' + fields.viewStyle.label,
            trigger: 'blur',
          },
        ],
        options: {
          clearable: true,
          placeholder: '请输入' + fields.viewStyle.label,
        },
      },
      {
        label: fields.remark.label,
        prop: fields.remark.prop,
        type: 'Input',
        rules: [],
        options: {
          clearable: true,
          placeholder: '请输入' + fields.remark.label,
          type: 'textarea',
          maxlength: '100',
          showWordLimit: true,
          rows: 4,
        },
      },
    ],
  },
};
