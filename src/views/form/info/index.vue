<template>
  <FormDesignerView :form-value="formValue" />
</template>
<script>
import FormDesignerView from '@/components/FormDesigner/src/components/form-render';
import { getDevApiUiFormDesignerFormId } from '@/api/ui/form-designer-controller.js';
export default {
  name: 'FormInfo',
  components: {
    FormDesignerView,
  },
  data() {
    return {
      formValue: null,
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.handlerInfo();
  },
  methods: {
    async handlerInfo() {
      const { formId } = this.$route.query;
      if (!formId) return;
      this.isEdit = true;
      const { data } = await getDevApiUiFormDesignerFormId(formId);
      this.info = data;
      if (data?.json) {
        try {
          this.formValue = JSON.parse(data.json);
        } catch (error) {
          console.log('data.json 解析失败', error);
        }
      }

      console.log('object :>>this.formValue  ', this.formValue);
    },
  },
};
</script>
<style scoped lang="scss"></style>
