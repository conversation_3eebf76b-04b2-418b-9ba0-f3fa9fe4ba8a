<template>
  <div class="app-container">
    <div v-if="!isEdit" class="createHeader">
      <span />
      <el-steps :active="active" simple>
        <el-step icon="el-icon-edit" title="设置表单名称" />
        <el-step icon="el-icon-edit-outline" title="创建表单" />
      </el-steps>
      <div>
        <el-button v-if="active === 1" type="primary" @click="onNext">下一步</el-button>
        <template v-if="active === 2">
          <el-button type="primary" @click="onBack">上一步</el-button>
          <el-button type="primary" @click="onSave">保存</el-button>
        </template>
      </div>
    </div>
    <div v-else class="createHeader">
      <span />
      <div>{{ info.dispName }}</div>
      <div>
        <el-button type="primary" @click="onSave">保存</el-button>
      </div>
    </div>
    <template v-if="!isEdit">
      <div v-show="active === 1" class="createContent">
        <el-card shadow="never">
          <CoustomForm ref="createForm" :form-json="formJson" :init-data="initFormData" />
        </el-card>
      </div>

      <div v-show="active === 2" class="createDesigner">
        <FormDesigner @onChange="formDesignerChange" />
      </div>
    </template>
    <template v-else>
      <div class="createDesigner">
        <FormDesigner :value="this.formConfig" @onChange="formDesignerChange" />
      </div>
    </template>
  </div>
</template>
<script>
import CoustomForm from '@/components/CoustomForm';
import FormDesigner from '@/components/FormDesigner/src/components/form-designer';
import { FormJson } from './constants';
import {
  postDevApiUiFormDesigner,
  getDevApiUiFormDesignerFormId,
  putDevApiUiFormDesigner,
} from '@/api/ui/form-designer-controller.js';
export default {
  name: 'FormCreate',
  components: {
    CoustomForm,
    FormDesigner,
  },
  data() {
    return {
      active: 1,
      formJson: FormJson,
      initFormData: {},
      formConfig: null,
      formData: null,
      info: {},
      isEdit: false,
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.handlerInfo();
  },
  methods: {
    async handlerInfo() {
      const { formId } = this.$route.query;
      if (!formId) return;
      this.isEdit = true;
      const { data } = await getDevApiUiFormDesignerFormId(formId);
      this.info = data;
      if (data?.json) {
        try {
          this.formConfig = JSON.parse(data.json);
        } catch (error) {
          console.log('data.json 解析失败', error);
        }
      }
    },

    formDesignerChange(config) {
      this.formConfig = config;
    },

    async onSave() {
      if (!this.formConfig || this.formConfig?.list?.length === 0) {
        this.$message.error('自定义表单不能为空！');
        return;
      }
      const func = !this.isEdit ? postDevApiUiFormDesigner : putDevApiUiFormDesigner;

      const result = await func({
        formId: this.info?.formId,
        dispName: this.formData.dispName,
        json: JSON.stringify(this.formConfig),
      });

      this.$message.success('保存成功！');
      this.$router.go(-1);
    },

    onBack() {
      this.active = 1;
    },

    async onNext() {
      const result = await this.$refs.createForm.validateForm();
      if (result) {
        if (this.active === 2) return;
        this.active = this.active + 1;
        this.formData = result;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.createHeader {
  height: 50px;
  width: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 16px;

  ::v-deep {
    .el-steps--simple {
      width: 312px;
      background-color: #fff;
      padding: 0;
    }
  }
}

.createContent {
  width: 680px;
  margin: 18px auto 0;
}

.createDesigner {
  position: relative;

  .main-container {
    margin-left: 0 !important;
  }
}
</style>
