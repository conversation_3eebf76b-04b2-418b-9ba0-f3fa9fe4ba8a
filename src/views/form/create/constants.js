import { formItemJsonTemplate } from '@/constants/jsonTemplate.js';
import { fields } from '@/views/form/list/constants.js';

export const FormJson = {
  formOption: {
    inline: false,
    labelWidth: '130px',
    labelPosition: 'left',
    size: 'small',
  },
  // 默认表单值
  defaultFormData: {},
  formItemJson: [
    formItemJsonTemplate({
      label: fields.dispName.label,
      prop: fields.dispName.prop,
    }),
  ],
};
