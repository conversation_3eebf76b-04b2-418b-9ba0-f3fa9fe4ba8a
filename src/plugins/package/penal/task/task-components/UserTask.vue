<template>
  <div>
    <el-row>
      <h4><b>审批人设置</b></h4>
      <el-radio-group v-model="dataType" @change="changeDataType">
        <el-radio label="USERS">指定用户</el-radio>
        <el-radio label="ROLES">角色</el-radio>
        <el-radio label="DEPTS">部门</el-radio>
        <el-radio label="INITIATOR">发起人</el-radio>
        <el-radio label="INITIATOR_OPTIONAL">发起人自选</el-radio>
      </el-radio-group>
    </el-row>
    <el-row>
      <div v-if="dataType === 'USERS'">
        <el-tag v-for="userText in selectedUser.text" :key="userText" effect="plain">
          {{ userText }}
        </el-tag>
        <div class="element-drawer__button">
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="onSelectUsers()"
            >添加用户</el-button
          >
        </div>
      </div>
      <div v-if="dataType === 'ROLES'">
        <el-select
          v-model="roleIds"
          multiple
          size="mini"
          placeholder="请选择 角色"
          @change="changeSelectRoles"
        >
          <el-option
            v-for="item in roleOptions"
            :key="item.roleId"
            :label="item.roleName"
            :value="`ROLE${item.roleId}`"
            :disabled="item.status === 1"
          />
        </el-select>
      </div>
      <div v-if="dataType === 'DEPTS'">
        <tree-select
          :width="320"
          :height="400"
          size="mini"
          :data="deptTreeData"
          :default-props="deptProps"
          multiple
          clearable
          check-strictly
          node-key="id"
          :checked-keys="deptIds"
          @change="checkedDeptChange"
        />
      </div>
      <div v-if="dataType === 'INITIATOR_OPTIONAL'">
        <h4><b>选择方式</b></h4>
        <el-radio-group v-model="selectType" @change="handlerChange">
          <el-radio label="MULTIPLE">多选</el-radio>
          <el-radio label="SINGLE">单选</el-radio>
        </el-radio-group>
        <!-- <h4><b>选择范围</b></h4>
        <el-radio-group v-model="selectRange">
          <el-radio label="MULTIPLE">全公司</el-radio>
          <el-radio label="USERS">指定成员</el-radio>
          <el-radio label="ROLES">指定角色</el-radio>
        </el-radio-group> -->
        <!-- <div v-if="selectRange === 'USERS'">
          <el-tag v-for="userText in selectedUser.text" :key="userText" effect="plain">
            {{ userText }}
          </el-tag>
          <div class="element-drawer__button">
            <el-button size="mini" type="primary" icon="el-icon-plus" @click="onSelectUsers()"
              >添加用户</el-button
            >
          </div>
        </div>
        <div v-if="selectRange === 'ROLES'">
          <el-select
            v-model="roleIds"
            multiple
            size="mini"
            placeholder="请选择 角色"
            @change="changeSelectRoles"
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.roleId"
              :label="item.roleName"
              :value="`ROLE${item.roleId}`"
              :disabled="item.status === 1"
            />
          </el-select>
        </div> -->
      </div>
    </el-row>
    <el-row>
      <div v-show="showMultiFlog">
        <el-divider />
        <h4><b>多实例审批方式</b></h4>
        <el-row>
          <el-radio-group v-model="multiLoopType" @change="changeMultiLoopType()">
            <!-- <el-row><el-radio label="Null">无</el-radio></el-row> -->
            <el-row
              ><el-radio label="SequentialMultiInstance">会签（需所有审批人同意）</el-radio></el-row
            >
            <el-row
              ><el-radio label="ParallelMultiInstance">或签（一名审批人同意即可）</el-radio></el-row
            >
          </el-radio-group>
        </el-row>
        <el-row v-if="multiLoopType !== 'Null'">
          <el-tooltip
            content="开启后，实例需按顺序轮流审批"
            placement="top-start"
            @click.stop.prevent
          >
            <i class="header-icon el-icon-info" />
          </el-tooltip>
          <span class="custom-label">顺序审批：</span>
          <el-switch v-model="isSequential" @change="changeMultiLoopType()" />
        </el-row>
      </div>
    </el-row>

    <!-- 候选用户弹窗 -->
    <el-dialog title="候选用户" :visible.sync="userOpen" width="60%" append-to-body>
      <el-row type="flex" :gutter="20">
        <!--部门数据-->
        <el-col :span="7">
          <el-card shadow="never" style="height: 100%">
            <div slot="header">
              <span>部门列表</span>
            </div>
            <div class="head-container">
              <el-input
                v-model="deptName"
                placeholder="请输入部门名称"
                clearable
                size="small"
                prefix-icon="el-icon-search"
                style="margin-bottom: 20px"
              />
              <el-tree
                ref="tree"
                :data="deptOptions"
                :props="deptProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                default-expand-all
                @node-click="handleNodeClick"
              />
            </div>
          </el-card>
        </el-col>
        <el-col :span="17">
          <el-table
            ref="multipleTable"
            height="600"
            :data="userTableList"
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="用户名" align="center" prop="nickName" />
            <el-table-column label="部门" align="center" prop="dept.deptName" />
          </el-table>
          <pagination
            :total="userTotal"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getUserList"
          />
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleTaskUserComplete">确 定</el-button>
        <el-button @click="userOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, deptTreeSelect } from '@/api/system/user';
import { listRole } from '@/api/system/role';
import TreeSelect from '@/components/TreeSelect';

export default {
  name: 'UserTask',
  components: { TreeSelect },
  props: {
    id: String,
    type: String,
  },
  data() {
    return {
      loading: false,
      dataType: 'USERS',
      selectedUser: {
        ids: [],
        text: [],
      },
      userOpen: false,
      deptName: undefined,
      deptOptions: [],
      deptProps: {
        children: 'children',
        label: 'label',
      },
      deptTempOptions: [],
      userTableList: [],
      userTotal: 0,
      selectedUserDate: [],
      roleOptions: [],
      roleIds: [],
      deptTreeData: [],
      deptIds: [],
      // 查询参数
      queryParams: {
        deptId: undefined,
      },
      showMultiFlog: false,
      isSequential: false,
      multiLoopType: 'SequentialMultiInstance',

      selectType: 'MULTIPLE',
      selectRange: 'USERS',
      userTaskForm: {
        dataType: '',
        assignee: '',
        candidateUsers: '',
        candidateGroups: '',
        text: '',
        selectType: 'MULTIPLE',
        // dueDate: '',
        // followUpDate: '',
        // priority: ''
      },
    };
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        this.$nextTick(() => this.resetTaskForm());
      },
    },
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
    multiLoopType() {
      this.changeMultiLoopType();
    },
  },
  beforeDestroy() {
    this.bpmnElement = null;
  },
  methods: {
    // 初始化
    async resetTaskForm() {
      const bpmnElementObj = this.bpmnElement?.businessObject;
      if (!bpmnElementObj) return;

      this.clearOptionsData();
      this.dataType = bpmnElementObj['dataType'];
      this.userTaskForm.dataType = this.dataType;

      switch (this.dataType) {
        case 'USERS':
          const userIdData = bpmnElementObj['candidateUsers'] || bpmnElementObj['assignee'];
          const userText = bpmnElementObj['text'] || [];
          if (userIdData && userIdData.toString().length > 0 && userText && userText.length > 0) {
            this.selectedUser.ids = userIdData?.toString().split(',');
            this.selectedUser.text = userText?.split(',');
          }
          this.handlerUseTaskForm();
          break;
        case 'ROLES':
          const roleIdData = bpmnElementObj['candidateGroups'] || [];
          if (roleIdData && roleIdData.length > 0) {
            this.roleIds = roleIdData.split(',');
          }
          await this.handlerRolesTaskForm();
          break;
        case 'DEPTS':
          const deptIdData = bpmnElementObj['candidateGroups'] || [];
          if (deptIdData && deptIdData.length > 0) {
            this.deptIds = deptIdData.split(',');
          }
          await this.handlerDeptTaskForm();
          break;
        case 'INITIATOR':
          this.handlerInitiatorTaskForm();
          break;
        case 'INITIATOR_OPTIONAL':
          const selectType = bpmnElementObj['selectType'] || '';
          this.selectType = selectType;
          this.handlerInitiatorOptionalTaskform();
          break;
        default:
          break;
      }

      this.getElementLoop(bpmnElementObj);
      this.updateElementTask();
    },
    // 审批人设置切换
    async changeDataType(val) {
      this.resetUserTaskForm();
      this.userTaskForm.dataType = val;
      switch (val) {
        case 'USERS':
          this.handlerUseTaskForm();
          break;
        case 'ROLES':
        await this.handlerRolesTaskForm();
          break;
        case 'DEPTS':
        await this.handlerDeptTaskForm();
          break;
        case 'INITIATOR':
          this.handlerInitiatorTaskForm();
          break;
        case 'INITIATOR_OPTIONAL':
          this.handlerInitiatorOptionalTaskform();
          break;
        default:
          break;
      }
      this.updateElementTask();
    },
    // 审批人设置-指定用户表单操作
    handlerUseTaskForm() {
      if (this.selectedUser && this.selectedUser.ids && this.selectedUser.ids.length > 0) {
        if (this.selectedUser.ids === 1) {
          this.userTaskForm.assignee = this.selectedUser.ids[0];
          this.userTaskForm.candidateUsers = null;
          this.multiLoopType = 'Null';
        } else {
          this.userTaskForm.assignee = null;
          this.userTaskForm.candidateUsers = this.selectedUser.ids.join();
          this.multiLoopType = 'SequentialMultiInstance';
        }
        this.userTaskForm.text = this.selectedUser.text?.join() || null;
        this.userTaskForm.candidateGroups = null;
      }

      if (this.selectedUser.ids.length > 1) {
        this.showMultiFlog = true;
      } else {
        this.showMultiFlog = false;
      }
    },
    // 指定用户-添加用户操作
    handleTaskUserComplete() {
      if (!this.selectedUserDate || this.selectedUserDate.length <= 0) {
        this.$modal.msgError('请选择用户');
        return;
      }
      this.resetUserTaskForm();
      this.userTaskForm.dataType = 'USERS';
      this.selectedUser.text = this.selectedUserDate.map(k => k.nickName) || [];
      this.selectedUser.ids = this.selectedUserDate.map(k => k.userId) || [];
      this.handlerUseTaskForm();
      this.updateElementTask();
      this.changeMultiLoopType();
      this.userOpen = false;
    },
    // 审批人设置-角色操作
    async handlerRolesTaskForm(ids, hasGetData) {
      if (!hasGetData) {
        await this.getRoleOptions();
      }
      const roleIds = ids ? ids : this.roleIds;
      this.showMultiFlog = true;
      this.multiLoopType = 'SequentialMultiInstance';
      if (roleIds && roleIds.length > 0) {
        this.userTaskForm.candidateGroups = roleIds.join() || null;
        const textArr = this.roleOptions.filter(k => roleIds.indexOf(`ROLE${k.roleId}`) >= 0);
        this.userTaskForm.text = textArr?.map(k => k.roleName).join() || null;
      }
    },

    async changeSelectRoles(val) {
      this.resetUserTaskForm();
      this.userTaskForm.dataType = 'ROLES';
      await this.handlerRolesTaskForm(val, true);
      this.updateElementTask();
      this.changeMultiLoopType();
    },
    // 审批人设置-部门操作
    async handlerDeptTaskForm(hasGetData) {
      if (!hasGetData) {
        await this.getDeptTreeData();
      }
      this.showMultiFlog = true;
      this.multiLoopType = 'SequentialMultiInstance';
      if (this.deptIds && this.deptIds.length > 0) {
        this.userTaskForm.candidateGroups = this.deptIds.join() || null;
        const textArr = [];
        const treeStarkData = JSON.parse(JSON.stringify(this.deptTreeData));
        this.deptIds.forEach(id => {
          let stark = [];
          stark = stark.concat(treeStarkData);
          while (stark.length) {
            const temp = stark.shift();
            if (temp.children) {
              stark = temp.children.concat(stark);
            }
            if (id === temp.id) {
              textArr.push(temp);
            }
          }
        });
        this.userTaskForm.text = textArr?.map(k => k.label).join() || null;
      }
    },
    async checkedDeptChange(checkedIds) {
      this.resetUserTaskForm();
      this.deptIds = checkedIds;
      this.userTaskForm.dataType = 'DEPTS';
      await this.handlerDeptTaskForm();
      this.updateElementTask();
      this.changeMultiLoopType();
    },
    // 审批人设置-发起人操作
    handlerInitiatorTaskForm() {
      this.userTaskForm.assignee = '${initiator}';
      this.userTaskForm.text = '流程发起人';
      this.userTaskForm.selectType = null;
      this.userTaskForm.candidateUsers = null;
      this.multiLoopType = 'Null';
      this.showMultiFlog = false;
    },
    // 审批人设置-发起人自选操作
    handlerInitiatorOptionalTaskform() {
      this.userTaskForm.selectType = this.selectType;
      if (this.selectType === 'MULTIPLE') {
        this.showMultiFlog = true;
        this.userTaskForm.candidateUsers = null;
        this.userTaskForm.assignee = '${assignee}';
        this.multiLoopType = 'SequentialMultiInstance';
        this.userTaskForm.candidateGroups = null;
        this.userTaskForm.text = '发起人自选';
      } else {
        this.showMultiFlog = false;
        this.multiLoopType = 'Null';
        this.userTaskForm.assignee = '${' + this.id + '_userId}';
      }
    },
    // 更新多实例配置（会签、或签）
    changeMultiLoopType() {
      // 取消多实例配置
      if (this.multiLoopType === 'Null') {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          loopCharacteristics: null,
          assignee: this.userTaskForm.assignee,
        });
        return;
      }
      this.multiLoopInstance = window.bpmnInstances.moddle.create(
        'bpmn:MultiInstanceLoopCharacteristics',
        {
          isSequential: this.isSequential,
        },
      );
      // 更新多实例配置
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        loopCharacteristics: this.multiLoopInstance,
        assignee: '${assignee}',
      });
      // 完成条件
      let completionCondition = null;
      // 会签
      if (this.multiLoopType === 'SequentialMultiInstance') {
        completionCondition = window.bpmnInstances.moddle.create('bpmn:FormalExpression', {
          body: '${nrOfCompletedInstances >= nrOfInstances}',
        });
      }
      // 或签
      if (this.multiLoopType === 'ParallelMultiInstance') {
        completionCondition = window.bpmnInstances.moddle.create('bpmn:FormalExpression', {
          body: '${nrOfCompletedInstances > 0}',
        });
      }
      // 更新模块属性信息
      window.bpmnInstances.modeling.updateModdleProperties(
        this.bpmnElement,
        this.multiLoopInstance,
        {
          collection: '${multiInstanceHandler.getUserIds(execution)}',
          elementVariable: 'assignee',
          completionCondition,
        },
      );
    },
    // 会签、或签处理
    getElementLoop(businessObject) {
      if (!businessObject.loopCharacteristics) {
        this.multiLoopType = 'Null';
        return;
      }
      this.isSequential = businessObject.loopCharacteristics.isSequential;
      if (businessObject.loopCharacteristics.completionCondition) {
        if (
          businessObject.loopCharacteristics.completionCondition.body ===
          '${nrOfCompletedInstances >= nrOfInstances}'
        ) {
          this.multiLoopType = 'SequentialMultiInstance';
        } else {
          this.multiLoopType = 'ParallelMultiInstance';
        }
      }
    },
    /**
     * 清空选项数据
     */
    clearOptionsData() {
      this.showMultiFlog = false;
      this.selectedUser.ids = [];
      this.selectedUser.text = [];
      this.roleIds = [];
      this.deptIds = [];
      this.resetUserTaskForm();
    },
    // 清空 this.userTaskForm 所有属性值
    resetUserTaskForm() {
      Object.keys(this.userTaskForm).forEach(key => (this.userTaskForm[key] = null));
    },
    /**
     * 跟新节点数据
     */
    updateElementTask() {
      const taskAttr = Object.create(null);
      for (const key in this.userTaskForm) {
        taskAttr[key] = this.userTaskForm[key];
      }
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, taskAttr);
    },
    /**
     * 查询部门下拉树结构
     */
    getDeptOptions() {
      return new Promise((resolve, reject) => {
        if (!this.deptOptions || this.deptOptions.length <= 0) {
          deptTreeSelect().then(response => {
            this.deptTempOptions = response.data;
            this.deptOptions = response.data;
            resolve();
          });
        } else {
          reject();
        }
      });
    },
    /**
     * 查询部门下拉树结构（含部门前缀）
     */
    getDeptTreeData() {
      function refactorTree(data) {
        return data.map(node => {
          const treeData = {
            id: `DEPT${node.id}`,
            label: node.label,
            parentId: node.parentId,
            weight: node.weight,
          };
          if (node.children && node.children.length > 0) {
            treeData.children = refactorTree(node.children);
          }
          return treeData;
        });
      }
      return new Promise((resolve, reject) => {
        if (!this.deptTreeData || this.deptTreeData.length <= 0) {
          this.getDeptOptions()
            .then(() => {
              this.deptTreeData = refactorTree(this.deptOptions);
              resolve();
            })
            .catch(() => {
              reject();
            });
        } else {
          resolve();
        }
      });
    },
    /**
     * 查询部门下拉树结构
     */
    async getRoleOptions() {
      if (!this.roleOptions || this.roleOptions.length <= 0) {
        const result = await listRole()
        this.roleOptions = result.rows || []
      }
    },
    /** 查询用户列表 */
    getUserList() {
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userTableList = response.rows;
        this.userTotal = response.total;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.getUserList();
    },
    // 关闭标签
    handleClose(tag) {
      this.selectedUserDate.splice(this.selectedUserDate.indexOf(tag), 1);
      this.$refs.multipleTable.toggleRowSelection(tag);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedUserDate = selection;
    },
    onSelectUsers() {
      this.selectedUserDate = [];
      this.$refs.multipleTable?.clearSelection();
      this.getDeptOptions();
      this.userOpen = true;
    },
    // 发起人自选-多选/单选操作
    handlerChange() {
      this.handlerInitiatorOptionalTaskform();
      this.updateElementTask();
    },
  },
};
</script>

<style scoped lang="scss">
.el-row .el-radio-group {
  margin-bottom: 15px;
  .el-radio {
    line-height: 28px;
  }
}
.el-tag {
  margin-bottom: 10px;
  + .el-tag {
    margin-left: 10px;
  }
}

.custom-label {
  padding-left: 5px;
  font-weight: 500;
  font-size: 14px;
  color: #606266;
}
</style>
