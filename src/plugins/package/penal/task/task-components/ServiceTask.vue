<template>
  <div>
    <el-form-item key="executeType" label="执行类型">
      <el-select v-model="serviceTaskForm.executeType">
        <el-option label="Java类" value="class" />
        <el-option label="表达式" value="expression" />
        <el-option label="代理表达式" value="delegateExpression" />
      </el-select>
    </el-form-item>
    <el-form-item
      v-if="serviceTaskForm.executeType === 'class'"
      key="execute-class"
      label="Java类"
      prop="class"
    >
      <el-input v-model="serviceTaskForm.class" clearable @change="updateElementTask" />
    </el-form-item>
    <el-form-item
      v-if="serviceTaskForm.executeType === 'expression'"
      key="execute-expression"
      label="表达式"
      prop="expression"
    >
      <el-input v-model="serviceTaskForm.expression" clearable @change="updateElementTask" />
    </el-form-item>
    <el-form-item
      v-if="serviceTaskForm.executeType === 'delegateExpression'"
      key="execute-delegate"
      label="代理表达式"
      prop="delegateExpression"
    >
      <el-input
        v-model="serviceTaskForm.delegateExpression"
        clearable
        @change="updateElementTask"
      />
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'ServiceTask',
  props: {
    id: String,
    type: String,
  },
  data() {
    return {
      defaultTaskForm: {
        executeType: '',
        class: '',
        expression: '',
        delegateExpression: '',
      },
      serviceTaskForm: {},
    };
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        this.$nextTick(() => this.resetTaskForm());
      },
    },
  },
  beforeDestroy() {
    this.bpmnElement = null;
  },
  methods: {
    resetTaskForm() {
      for (const key in this.defaultTaskForm) {
        const value = this.bpmnElement?.businessObject[key] || this.defaultTaskForm[key];
        if (value) this.$set(this.serviceTaskForm, 'executeType', key);
        this.$set(this.serviceTaskForm, key, value);
      }
    },
    updateElementTask() {
      const taskAttr = Object.create(null);
      const type = this.serviceTaskForm.executeType;
      for (const key in this.serviceTaskForm) {
        if (key !== 'executeType' && key !== type) taskAttr[key] = null;
      }
      taskAttr[type] = this.serviceTaskForm[type] || '';
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, taskAttr);
    },
  },
};
</script>
