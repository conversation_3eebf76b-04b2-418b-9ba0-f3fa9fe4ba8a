export const TaskZh = {
  'Task': '任务',
  'ServiceTask': '服务任务',
  'UserTask': '用户任务',
  'BusinessRuleTask': '业务规则任务',
  'ScriptTask': '脚本任务',
  'ReceiveTask': '接收任务',
  'ManualTask': '手动任务',
  'SendTask': '发送任务',
  'ExclusiveGateway': '互斥网关',
  'SequenceFlow': '流程线',
  'ParallelGateway': '并行网关',
  'InclusiveGateway': '相容网关',
  'ComplexGateway': '复杂网关',
  'EventBasedGateway': '事件网关',
  'StartEvent': '开始事件',
  'IntermediateCatchEvent': '中间捕获事件',
  'IntermediateThrowEvent': '中间抛出事件',
  'EndEvent': '结束事件',
  'BoundaryEvent': '边界事件',
  'CallActivity': '调用活动',
  'SubProcess': '子流程',
  'Process': '流程',
  'Participant': '参与者',
};
