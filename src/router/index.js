import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

/* Layout */
import Layout from '@/layout';
import AppMain from '@/layout/components/AppMain'
import ParentView from '@/components/ParentView';

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true },
      },
    ],
  },
  {
    path: '/tool',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'build/index',
        component: () => import('@/views/tool/build/index'),
        name: 'FormBuild',
        meta: { title: '表单设计', icon: '' },
      },
    ],
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' },
      },
    ],
  },
  {
    name: 'Carbon-flowable',
    path: '/carbon-flowable',
    hidden: false,
    redirect: 'noRedirect',
    component: AppMain,
    alwaysShow: true,
    meta: {
      title: '工作流管理',
      icon: 'peoples',
      noCache: false,
      link: null,
    },
    children: [
      {
        name: 'OwnerDoc',
        path: 'ownerDoc',
        hidden: false,
        component: () => import('@/views/ownerDoc/index'),
        meta: {
          title: '业主发文',
          icon: 'form',
          noCache: true,
          link: null,
        },
      },
     
      {
        name: 'ProjectManage',
        path: 'projectManage',
        hidden: false,
        component: () => import('@/views/projectManage/index'),
        meta: {
          title: '项目管理',
          icon: 'tool',
          noCache: true,
          link: null,
        },
      },
      {
        name: 'QualityManage',
        path: 'qualityManage',
        hidden: false,
        redirect: 'noRedirect',
        component: ParentView,
        alwaysShow: true,
        meta: {
          title: '质量管理',
          icon: 'job',
          noCache: false,
          link: null,
        },
        children: [
          {
            name: 'QualityVerify',
            path: 'qualityVerify',
            hidden: false,
            component: () => import('@/views/qualityVerify/index'),
            meta: {
              title: '质量验评',
              icon: 'clipboard',
              noCache: true,
              link: null,
            },
          },
        ],
      },
      {
        name: 'PlanManage',
        path: 'planManage',
        hidden: false,
        redirect: 'noRedirect',
        component: ParentView,
        alwaysShow: true,
        meta: {
          title: '任务管理',
          icon: 'job',
          noCache: false,
          link: null,
        },
        children: [
          {
            name: 'PlanList',
            path: 'planList',
            hidden: false,
            component: () => import('@/views/planManage/index'),
            meta: {
              title: '计划列表',
              icon: 'tool',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'PlanList/create',
            path: 'planList/create',
            hidden: true,
            component: () => import('@/views/planManage/planGantt'),
            meta: {
              title: '计划创建',
              icon: '#',
              noCache: false,
              link: null,
            },
          },
          {
            name: 'PlantTemplate',
            path: 'plantTemplate',
            hidden: false,
            component: () => import('@/views/planManage/plantTemplate/index'),
            meta: {
              title: '模版列表',
              icon: 'documentation',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'PlantTemplate/fields',
            path: 'plantTemplate/fields',
            hidden: true,
            component: () => import('@/views/planManage/templateField/index'),
            meta: {
              title: '模版字段',
              icon: 'dict',
              noCache: true,
              link: null,
            },
          },
        ],
      },
      {
        name: 'Process',
        path: 'process',
        hidden: false,
        redirect: 'noRedirect',
        component: ParentView,
        alwaysShow: true,
        meta: {
          title: '流程管理',
          icon: 'skill',
          noCache: false,
          link: null,
        },
        children: [
          {
            name: 'Category',
            path: 'category',
            hidden: false,
            component: () => import('@/views/workflow/category/index'),
            meta: {
              title: '流程分类',
              icon: 'cascader',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Form',
            path: 'form',
            hidden: false,
            component: () => import('@/views/workflow/form/index'),
            meta: {
              title: '表单配置',
              icon: 'build',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Form/create',
            path: 'form/create',
            hidden: true,
            component: () => import('@/views/workflow/form/create'),
            meta: {
              title: '表单创建',
              icon: 'clipboard',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Model',
            path: 'model',
            hidden: false,
            component: () => import('@/views/workflow/model/index'),
            meta: {
              title: '流程模型',
              icon: 'component',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Model/create',
            path: 'model/create',
            hidden: true,
            component: () => import('@/views/workflow/model/create'),
            meta: {
              title: '模型创建',
              icon: 'skill',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Deploy',
            path: 'deploy',
            hidden: false,
            component: () => import('@/views/workflow/deploy/index'),
            meta: {
              title: '部署管理',
              icon: 'example',
              noCache: true,
              link: null,
            },
          },
        ],
      },
      {
        name: 'Work',
        path: 'work',
        hidden: false,
        redirect: 'noRedirect',
        component: ParentView,
        alwaysShow: true,
        meta: {
          title: '办公管理',
          icon: 'job',
          noCache: false,
          link: null,
        },
        children: [
          {
            name: 'Create',
            path: 'create',
            hidden: false,
            component: () => import('@/views/workflow/work/index'),
            meta: {
              title: '新建流程',
              icon: 'guide',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Own',
            path: 'own',
            hidden: false,
            component: () => import('@/views/workflow/work/own'),
            meta: {
              title: '我的流程',
              icon: 'cascader',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Todo',
            path: 'todo',
            hidden: false,
            component: () => import('@/views/workflow/work/todo'),
            meta: {
              title: '待办任务',
              icon: 'time-range',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Claim',
            path: 'claim',
            hidden: false,
            component: () => import('@/views/workflow/work/claim'),
            meta: {
              title: '待签任务',
              icon: 'checkbox',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Finished',
            path: 'finished',
            hidden: false,
            component: () => import('@/views/workflow/work/finished'),
            meta: {
              title: '已办任务',
              icon: 'checkbox',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Copy',
            path: 'copy',
            hidden: false,
            component: () => import('@/views/workflow/work/copy'),
            meta: {
              title: '抄送我的',
              icon: 'checkbox',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Start',
            path: 'start',
            hidden: true,
            component: () => import('@/views/workflow/work/start'),
            meta: {
              title: '发起流程',
              icon: '#',
              noCache: true,
              link: null,
            },
          },
          {
            name: 'Detail',
            path: 'detail',
            hidden: true,
            component: () => import('@/views/workflow/work/detail'),
            meta: {
              title: '流程详情',
              icon: '#',
              noCache: true,
              link: null,
            },
          },
        ],
      },
      {
        name: 'Category',
        path: 'category',
        hidden: false,
        redirect: 'noRedirect',
        component: ParentView,
        alwaysShow: true,
        meta: {
          title: '分类管理',
          icon: 'dict',
          noCache: false,
          link: null,
        },
        children: [
          {
            name: 'OwnerCategory',
            path: 'ownerCategory',
            hidden: false,
            component: () => import('@/views/category/ownerCategory/index'),
            meta: {
              title: '业主文件',
              icon: 'documentation',
              noCache: true,
              link: null,
            },
          },
        ],
      },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' },
      },
    ],
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' },
      },
    ],
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' },
      },
    ],
  },
  {
    path: '/system/oss-config',
    component: Layout,
    hidden: true,
    permissions: ['system:oss:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/system/oss/config'),
        name: 'OssConfig',
        meta: { title: '配置管理', activeMenu: '/system/oss' },
      },
    ],
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' },
      },
    ],
  },
  {
    path: '/workflow/process',
    component: Layout,
    hidden: true,
    permissions: ['workflow:process:query'],
    children: [
      {
        path: 'start/:deployId([\\w|\\-]+)',
        component: () => import('@/views/workflow/work/start'),
        name: 'WorkStart',
        meta: { title: '发起流程', icon: '' },
      },
      {
        path: 'detail/:procInsId([\\w|\\-]+)',
        component: () => import('@/views/workflow/work/detail'),
        name: 'WorkDetail',
        meta: { title: '流程详情', activeMenu: '/work/own' },
      },
    ],
  },
];

// 防止连续点击多次路由报错
const routerPush = Router.prototype.push;
const routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err);
};
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err);
};

export default new Router({
  // 如果作为 qiankun 的微应用则使用主应用中配置的路由前缀，如果是独立运行则使用根目录process.env.VUE_APP_PUBLIC_PATH,
  // base: window.__POWERED_BY_QIANKUN__ ? '/flowable/' : '/',
  // mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
