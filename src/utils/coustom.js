// 获取表单关联项是否展示
export function associationIsShow(item, data) {
  const { association } = item;
  if (!association) return false;
  const value = data[item.prop];
  return item.association.some(item => {
    const { blackValue = [], whiteValue = [] } = item;
    if (blackValue && blackValue.length !== 0) {
      return !blackValue.includes(value);
    }
    if (whiteValue && whiteValue.length !== 0) {
      return whiteValue.includes(value);
    }
  });
}

// 获取表单关联项 JSON 数据
export function associationData(item, data) {
  if (!item.association) return [];
  const value = data[item.prop];
  const config = item.association.find(item => {
    const { blackValue = [], whiteValue = [] } = item;
    if (blackValue && blackValue.length !== 0) {
      return !blackValue.includes(value);
    }
    if (whiteValue && whiteValue.length !== 0) {
      return whiteValue.includes(value);
    }
  });

  return config;
}
