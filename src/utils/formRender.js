import request from '@/utils/request';
import { generateId } from '@/components/FormDesigner/src/utils/util';

// 公用请求
function commonGetRequest(url) {
  return request({
    url: `carbon-bpm${url}`,
    method: 'get',
  });
}

function getStaticTextField(textContent) {
  return {
    type: 'static-text',
    icon: 'static-text',
    formItemFlag: false,
    options: {
      name: `statictext${generateId()}`,
      columnWidth: '200px',
      hidden: false,
      textContent: textContent,
      textAlign: 'center',
      fontSize: '13px',
      preWrap: false,
      label: 'static-text',
    },
    id: `statictext${generateId()}`,
  };
}

function getTextAreaField(colsItem, itemId, rowsIndex) {
  const { label, name } = colsItem || {};
  const id = `${name}-${rowsIndex}`;
  return {
    type: 'textarea',
    icon: 'textarea-field',
    formItemFlag: true,
    options: {
      itemId: itemId, // 存储 ItemId
      name: id,
      label,
      labelAlign: '',
      rows: 3,
      defaultValue: '',
      placeholder: '请输入',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: true,
      readonly: false,
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
    },
    id,
  };
}

function getInputField(colsItem, rowsIndex) {
  const { label, name } = colsItem || {};
  const id = `${name}-${rowsIndex}`;
  return {
    "type": "input",
    "icon": "text-field",
    "formItemFlag": true,
    "options": {
      "name": id,
      "label": label,
      "labelAlign": "label-right-align",
      "contentAlign": "left",
      "type": "text",
      "defaultValue": "",
      "placeholder": "",
      "columnWidth": "200px",
      "size": "",
      "labelWidth": null,
      "labelHidden": true,
      "readonly": false,
      "disabled": false,
      "hidden": false,
      "clearable": false,
      "showPassword": false,
      "required": false,
      "requiredHint": "",
      "validation": "",
      "validationHint": "",
      "permission": "",
      "labelIconClass": null,
      "labelIconPosition": "rear",
      "labelTooltip": null,
      "minLength": null,
      "maxLength": null,
      "showWordLimit": false,
      "prefixIcon": "",
      "suffixIcon": "",
      "appendButton": false,
      "appendButtonDisabled": false,
      "buttonIcon": "el-icon-search",
      onBlur: function(val) {
        // 抽样量、容量失去焦点后，自动回显验收结果、单项结论
        const value = val.target.value;
        const name = this.field.options.name;
        const nameInfo =  name.split('-');
        const curtName = nameInfo[0]
        const curtIndex = nameInfo[1]

        let itemResult = this.formModel[`itemResult-${curtIndex}`] || ''
        const itemConclusion = this.formModel[`itemConclusion-${curtIndex}`] || ''
        itemResult = itemResult.split(',')

        if (curtName === 'itemSize') {
          if (!value) {
            itemResult[0] = ''
          } else {
            itemResult[0] = `抽查${value}处`
          }
        }

        if (curtName === 'itemCap') {
          if (!value) {
            itemResult[0] = ''
          } else {
            itemResult[1] = `合格${value}处`
          }

        }

        const itemResultValue = itemResult[0] ? itemResult.join(',') : itemResult.join('')
        const itemResultKey = `itemResult-${curtIndex}`
        this.$set(this.formModel, itemResultKey, itemResultValue )
        const itemResultEl = document.getElementById(itemResultKey)
        itemResultEl.value = itemResultValue
        if (!itemConclusion) {
          const itemConclusionValue = '√'
          const itemConclusionKey = `itemConclusion-${curtIndex}`
          this.$set(this.formModel,itemConclusionKey, itemConclusionValue)
          const itemConclusionEl = document.getElementById(itemConclusionKey)
          itemConclusionEl.value = itemConclusionValue
        }
      }
    },
    id,
  }
}

const tableCellWidth = {
  'itemType': 10,
  'serialNo': 10,
  'itemName': 28,
  'itemQs': 40,
  'unit': 12,
  'itemMethod': 30,
  'selfTestRecord': 35, // 施工单位自检记录
  'testResult': 35, // 检查结果

  'itemResult': 35, // 验收结果
  'itemConclusion': 35, // 单项结论

  'itemSize': 12, // 抽样量
  'itemCap': 12, // 容量
}

function getWidgetList(cols, itemId, rowsIndex) {
  if (['selfTestRecord', 'testResult','itemResult','itemConclusion'].includes(cols.name) && rowsIndex !== 0) {
    return getTextAreaField(cols, itemId, rowsIndex)
  } else if (['itemSize', 'itemCap'].includes(cols.name) && rowsIndex !== 0) {
    return getInputField(cols, rowsIndex)
  } else {
    return getStaticTextField(cols.value, rowsIndex)
  }

}

function handlerTableRow(inspectionItems) {
  const newTable = [];
  inspectionItems.map((rows, rowsIndex) => {
    const rowsObj = {
      id: `table-row-${generateId()}`,
      merged: false,
      cols: [],
    };
    let itemId = 0;
    rows.map((cols, colsIndex) => {
      // 首列 Id 不展示
      if (colsIndex === 0) {
        itemId = cols.value;
        return;
      }
      const id = `table-cell-${generateId()}`;
      const closObj = {
        id,
        type: 'table-cell',
        category: 'container',
        icon: 'table-cell',
        internal: true,
        merged: cols.merged || false,
        options: {
          name: id,
          // TODO: 单元格宽度需根据后端返回
          cellWidth: `${tableCellWidth[cols.name] || 10}%`,
          cellHeight: '',
          colspan: cols.colspan || 1,
          rowspan: cols.rowspan || 1,
          wordBreak: false,
          customClass: '',
        },
        widgetList: [
          getWidgetList(cols, itemId, rowsIndex)
        ],
      };
      rowsObj.cols.push(closObj);
    });

    newTable.push(rowsObj);
  });

  return newTable

}


export function getIndex(widgetList, type) {
  return widgetList.findIndex(
    item => item.type === type,
  );
}


/***
 * 如果是 inspectionItemsTable 组件则需要根据接口动态生成
 */
export async function handleDynamicsForm(formModel, formDataPath, hiddenApproval = true) {
  if (!formDataPath) return formModel

  const { widgetList, formConfig } = formModel;
  const isStandardTableIndex = getIndex(widgetList, 'standardTable')

  let isInspectionItemsIndex = -1;
  if (isStandardTableIndex !== -1) {
    // 是标准表
    const curtWidgetList = widgetList[isStandardTableIndex].widgetList;
    isInspectionItemsIndex = getIndex(curtWidgetList, 'inspectionItemsTable')
  } else {
    // 不是标准表
    isInspectionItemsIndex = getIndex(widgetList, 'inspectionItemsTable')
  }

  if (isInspectionItemsIndex === -1) {
    return formModel;
  }

  try {
    const { code, data } = await commonGetRequest(formDataPath);
    if (code === 200) {
      let inspectionItems = '';
      try {
        inspectionItems = JSON.parse(data.modelContent);
      } catch (error) {
        console.log('object :>>解析失败', error);
      }

      if (isStandardTableIndex !== -1) {
        // 是标准表
        widgetList[isStandardTableIndex].widgetList[isInspectionItemsIndex].rows =  handlerTableRow(inspectionItems);
      } else {
        // 不是标准表
        widgetList[isInspectionItemsIndex].rows =  handlerTableRow(inspectionItems);
      }

      if (hiddenApproval) {
        // TODO: 审批信息表暂时隐藏
        let approvalInfoIndex  = -1
        let curtWidgetList = widgetList
        if (isStandardTableIndex !== -1) {
          // 是标准表
          curtWidgetList = widgetList[isStandardTableIndex].widgetList
          approvalInfoIndex = curtWidgetList.findIndex(item => item.type === 'approvalInfoTable');
          approvalInfoIndex = getIndex(curtWidgetList,'approvalInfoTable')
        } else {
          // 不是标准表
          approvalInfoIndex = getIndex(widgetList, 'approvalInfoTable')
        }
        curtWidgetList.splice(approvalInfoIndex, 1)
      }

      return {
        formConfig,
        widgetList,
      }
    }
  } catch (error) {
    console.log('处理失败', error)
    return formModel
  }
}
