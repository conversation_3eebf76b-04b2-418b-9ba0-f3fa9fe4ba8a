// function getVueAppBase() {
//   const info = {};
//   const lineVersion = process.env.VUE_APP_LINE_VERSION;
//   switch (process.env.VUE_APP_PACK_ENV) {
//     case 'dev':
//       info.envText = '开发环境';
//       info.publicPath = '/dev-flowable';
//       info.baseApi = 'http://42.192.68.110/dev-flowable-api';
//       break;
//     case 'pro':
//       info.envText = '生产环境';
//       info.publicPath = '/checkwork';
//       info.baseApi = +lineVersion === 2 ? 'https://sepro.hzcens.com' : 'https://sepro.hzcens.com';
//       break;
//     case 'local':
//       info.envText = '本地环境';
//       info.publicPath = '/';
//       info.baseApi = 'http://192.168.0.110:9090';
//       break;
//     case 'mock':
//       info.envText = 'mock环境';
//       info.publicPath = '/risk-mock';
//       info.baseApi = 'https://www.dypzdh.club/data/risk-control-2';
//       break;
//     default:
//       info.envText = '默认环境（开发）';
//       info.publicPath = '/';
//       info.baseApi = 'http://42.192.68.110/dev-flowable-api';
//       break;
//   }
//   return info;
// }

module.exports = {
  envText: process.env.NODE_ENV,
  publicPath: process.env.VUE_APP_PUBLIC_PATH,
  baseApi: process.env.VUE_APP_BASE_API,
};
