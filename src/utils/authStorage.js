import Cookies from 'js-cookie';

const TokenKey = 'Admin-Token'
const ExpiresInKey = 'Admin-Expires-In'

const localStorage = window.sessionStorage || window.localStorage
export function getToken() {
  let token = localStorage.getItem(TokenKey)
  if (!token) {
    token = Cookies.get(TokenKey)
  }
  console.log('object :>>getToken ', token);
  return token
}

export function setToken(token) {
  Cookies.set(TokenKey, token);
  return localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  Cookies.remove(TokenKey);
  return localStorage.removeItem(TokenKey)
}


export function getExpiresIn() {
  let expiresIn = localStorage.getItem(ExpiresInKey)
  if (!expiresIn) {
    expiresIn = Cookies.get(ExpiresInKey)
  }
  return expiresIn || -1
}

export function setExpiresIn(time) {
  Cookies.set(ExpiresInKey, time);
  return localStorage.setItem(ExpiresInKey, time)
}

