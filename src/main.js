import './public-path';
import './qiankun/token'

import Vue from 'vue';

import Cookies from 'js-cookie';

import Element from 'element-ui';

import './assets/styles/element-variables.scss';
// import 'vform-builds/dist/VFormDesigner.css'; //引入VForm样式
import '@/assets/styles/index.scss'; // global css
import '@/assets/styles/ruoyi.scss'; // ruoyi css
import App from './App';
import store from './store';
import router from './router';
import directive from './directive'; // directive
import filter from './filter'; // directive
import plugins from './plugins'; // plugins
import { download } from '@/utils/request';

import './assets/icons'; // icon
import './permission'; // permission control
import { getDicts } from '@/api/system/dict/data';
import { getConfigKey, updateConfigByKey } from '@/api/system/config';
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree,
} from '@/utils/ruoyi';
// 分页组件
import Pagination from '@/components/Pagination';
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar';
// 富文本组件
import Editor from '@/components/Editor';
// 文件上传组件
import FileUpload from '@/components/FileUpload';
// 图片上传组件
import ImageUpload from '@/components/ImageUpload';
// 图片预览组件
import ImagePreview from '@/components/ImagePreview';
// 字典标签组件
import DictTag from '@/components/DictTag';
// 头部标签组件
import VueMeta from 'vue-meta';
// 字典数据组件
import DictData from '@/components/DictData';
// VForm表单组件
// import VForm from 'vform-builds';
import VForm from '@/components/FormDesigner/install.js';
import FilePreviewPlugin from '@/components/FilePreview'

// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.updateConfigByKey = updateConfigByKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;

Vue.prototype.$eventBus = new Vue();

// 全局组件挂载
Vue.component('DictTag', DictTag);
Vue.component('Pagination', Pagination);
Vue.component('RightToolbar', RightToolbar);
Vue.component('Editor', Editor);
Vue.component('FileUpload', FileUpload);
Vue.component('ImageUpload', ImageUpload);
Vue.component('ImagePreview', ImagePreview);

Vue.use(directive);
Vue.use(filter);
Vue.use(plugins);
Vue.use(VueMeta);
Vue.use(VForm);
Vue.use(FilePreviewPlugin);
DictData.install();

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

// 修改 el-dialog 默认点击遮照为不关闭
Element.Dialog.props.closeOnClickModal.default = false;

Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
});

Vue.config.productionTip = false;

// 重新包装 render 方法
let instance = null;
function render(props = {}) {
  const { container, actions } = props;
  if (actions) {
    actions.onGlobalStateChange((state, prev) => {
      // state: 变更后的状态; prev 变更前的状态
      console.log('子应用 state 变更：', state, prev);
    });
  }

  // 如果是作为微应用，则渲染到主应用中 qiankun 节点下的 #app 中, 如果作为独立运行时，则渲染到 #app 节点中
  const renderContainer = container ? container.querySelector('#app') : '#app';
  instance = new Vue({
    router,
    store,
    /**
     *  根节点上挂载主应用信息，可通过此方式将主元素的 router、store 传递给子应用
     *  可通过 this.$root.parentActions.getGlobalState() 获取到全局参数
     */
    data: () => {
      return {
        parentActions: actions
      };
    },
    render: (h) => h(App)
  }).$mount(renderContainer);
}

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

// 导出主应用识别所需的三个必要的生命周期钩子

/**
 * bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发 bootstrap。
 * 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等。
 */
export async function bootstrap() {
  console.log('初始化微应用 [vue] vue app bootstraped');
}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props) {
  console.log('渲染微应用 [vue] props from main framework', props);
  render(props); // 作为微应用渲染时将主应用传入的配置作为参数去渲染
}

/**
 * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
 */
export async function unmount() {
  console.log('卸载微应用');
  instance.$destroy();
  instance.$el.innerHTML = '';
  instance = null;
  // router = null;
}

/**
 * 可选生命周期钩子，仅使用 loadMicroApp 方式加载微应用时生效
 */
export async function update(props) {
  console.log('更新微应用', props);
}
