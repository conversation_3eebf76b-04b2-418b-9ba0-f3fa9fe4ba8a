import Layout from './routerView.vue'
const routes = [
  {
    menuName: '风险分级管控',
    path: '/safeManage',
    component: Layout,
    children: [
      {
        menuName: '风险基础库管理',
        path: 'basicRiskLibrary',
        component: () => import('@/views/safeManage/basicRiskLibrary/index')
      },
      {
        menuName: '风险基础库查询',
        path: 'basicRiskLibrarySearch',
        component: () => import('@/views/safeManage/basicRiskLibrarySearch/index')
      },
      {
        menuName: '风险辨识库管理',
        path: 'indentification',
        component: () => import('@/views/safeManage/indentification/index')
      },
      {
        menuName: '风险辨识库查询',
        path: 'sysIndentification',
        component: () => import('@/views/safeManage/sysIndentification/index')
      },
      {
        menuName: '风险动态管控库',
        path: 'dynamic',
        component: () => import('@/views/safeManage/dynamic/index')
      },
      {
        menuName: '项目同步',
        path: 'syncProject',
        component: () => import('@/views/safeManage/syncProject/index.vue')
      }
    ]
  },
  {
    menuName: '隐患排查治理',
    path: '/hiddenManage',
    component: Layout,
    children: [
      {
        menuName: '隐患管理',
        path: 'hiddenTrouble',
        component: () => import('@/views/hiddenManage/hiddenTrouble/index.vue')
      },
      {
        menuName: '紧急信息报送',
        path: 'emergencyInformation',
        component: () => import('@/views/emergencyInformation/index.vue')
      }
    ]
  }
]
export default routes
