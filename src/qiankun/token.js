import { getToken, setToken, removeToken } from '@/utils/authStorage'
// import { getToken, setToken, removeToken } from '@/utils/auth'

/**
 * iframe微前端通信通道
 * 提供主应用与iframe微前端之间的安全通信机制
 * 支持令牌同步、自定义消息类型和事件监听
 */
const IframeCommunicator = {
  // 初始化通信
  init() {
    window.addEventListener('message', this.handleMessage.bind(this))
    console.log('[IframeCommunicator] 通信通道已初始化')
  },

  // 销毁通信
  destroy() {
    window.removeEventListener('message', this.handleMessage.bind(this))
    console.log('[IframeCommunicator] 通信通道已销毁')
  },

  // 处理接收到的消息
  handleMessage(event) {
    // 验证消息来源
    if (!this.isValidOrigin(event.origin)) {
      console.warn(`[IframeCommunicator] 来自未授权源的消息: ${event.origin}`)
      return
    }

    const { type, payload } = event.data

    // 处理内置消息类型
    switch (type) {
      case 'TOKEN_UPDATE':
        setToken(payload.token)
        console.log('[IframeCommunicator] 令牌已更新', payload.token)
        break
      case 'TOKEN_REQUEST':
        this.sendToken()
        break
      case 'TOKEN_REMOVE':
        removeToken()
        console.log('[IframeCommunicator] 令牌已移除')
        break
      default:
        // 处理自定义消息
        if (this.customHandlers[type]) {
          this.customHandlers[type](payload, event.source)
        } else {
          console.log(`[IframeCommunicator] 未处理的消息类型: ${type}`)
        }
    }
  },

  // 自定义消息处理器
  customHandlers: {},

  // 注册自定义消息处理器
  on(messageType, handler) {
    this.customHandlers[messageType] = handler
  },

  // 移除自定义消息处理器
  off(messageType) {
    delete this.customHandlers[messageType]
  },

  // 向iframe发送token
  sendToken(targetOrigin = '*') {
    const token = getToken()
    this.postMessage({
      type: 'TOKEN_UPDATE',
      payload: { token }
    }, targetOrigin)
  },

  // 向父应用请求token
  requestToken(targetOrigin = '*') {
    this.postMessage({
      type: 'TOKEN_REQUEST'
    }, targetOrigin)
   

     // 刷新页面
     // TODO: 临时操作，根本原因还是 与父应用通讯问题
     const timer = setTimeout(() => {
        window.location.reload();
        clearTimeout(timer);
    }, 200);
  },

  // 通知移除token
  removeToken(targetOrigin = '*') {
    this.postMessage({
      type: 'TOKEN_REMOVE'
    }, targetOrigin)
  },

  // 向父/子应用发送消息
  postMessage(message, targetOrigin = '*') {
    try {
      if (window.parent !== window) {
        // 微前端上下文 - 发送给父应用
        window.parent.postMessage(message, targetOrigin)
        // window.opnenr.postMessage(message, targetOrigin)
        // window.top.postMessage(message, targetOrigin)
        console.log('object :>>已发送消息 ', message, targetOrigin);
      } else {
        // 主应用上下文 - 发送给所有iframe
        document.querySelectorAll('iframe').forEach(iframe => {
          const iframeOrigin = new URL(iframe.src).origin
          iframe.contentWindow.postMessage(message, iframeOrigin)
        })
      }
    } catch (error) {
      console.error('[IframeCommunicator] 消息发送失败:', error)
    }
  },

  // 验证消息来源（添加允许的域名）
  isValidOrigin(origin) {
    const allowedOrigins = [
      window.location.origin,
      // 在此添加其他允许的域名
    ]
    return true  // allowedOrigins.includes(origin)
  }
}

// 自动初始化
IframeCommunicator.init()

// 导出以便手动控制
export default IframeCommunicator
