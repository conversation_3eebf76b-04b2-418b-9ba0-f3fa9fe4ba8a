import request from '@/utils/request'

// 查询字典数据列表
export function listData(query) {
  return request({
    url: '/system/dict-data/page',
    method: 'get',
    params: query
  })
}

// 查询字典数据详细
export function getData(dictCode) {
  return request({
    url: '/system/dict-data/get?id=' + dictCode,
    method: 'get'
  })
}

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return request({
    url: '/system/dict-data/page?pageNo=1&pageSize=100&dictType=' + dictType,
    method: 'get'
  })
}


// // 查询字典数据（精简)列表
// export const getDicts = (dictType) => {
//   return request.get({ url: '/system/dict-data/simple-list' })
// }


// 新增字典数据
export function addData(data) {
  return request({
    url: '/system/dict-data/create',
    method: 'post',
    data: data
  })
}

// 修改字典数据
export function updateData(data) {
  return request({
    url: '/system/dict-data/update',
    method: 'put',
    data: data
  })
}

// 删除字典数据
export function delData(dictCode) {
  return request({
    url: '/system/dict-data/delete?id='+ dictCode,
    method: 'delete'
  })
}
