/**
 * 新能源发电项目施工包API
 */
import request from '@/utils/request'

/**
* 新增新能源发电项目施工包
* @param {undefined} projNewEnergyPackage projNewEnergyPackage 
* @returns 
*/
export function postBpmProjPackage(data) {
  return request({
    url: '/carbon-bpm/proj/package',
    method: 'post', 
    data: data
  })
}

/**
* 修改新能源发电项目施工包
* @param {undefined} projNewEnergyPackage projNewEnergyPackage 
* @returns 
*/
export function putBpmProjPackage(data) {
  return request({
    url: '/carbon-bpm/proj/package',
    method: 'put', 
    data: data
  })
}

/**
* 导出新能源发电项目施工包列表
* @param {string} ancestors 组级别 
* @param {integer} constructionCompany 施工单位 
* @param {integer} constructionCompanyLeader 施工单位项目负责人 
* @param {string} constructionCompanyLeaderName undefined 
* @param {string} constructionCompanyName undefined 
* @param {integer} constructionLeader 建设单位项目负责人id 
* @param {string} constructionLeaderName undefined 
* @param {integer} constructionUnit 建设单位id 
* @param {string} constructionUnitName undefined 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} designLeader 设计单位项目负责人id 
* @param {string} designLeaderName undefined 
* @param {integer} designUnit 设计单位id 
* @param {string} designUnitName undefined 
* @param {integer} divisionId 工程质量验收范围划分表Id 
* @param {string} divisionType 类型：01：单位工程；02：子单位工程； 03：分部工程；04：子分部工程；05：分项工程；06：检验批 
* @param {integer} generalContractor 总包单位 
* @param {integer} generalContractorLeader 总包单位项目负责人 
* @param {string} generalContractorLeaderName undefined 
* @param {string} generalContractorName undefined 
* @param {integer} orderNum 排序 
* @param {object} params undefined 
* @param {integer} parentId 父类 
* @param {string} projCode 工程编码 
* @param {integer} projectId 项目Id 
* @param {integer} projId 工程Id 
* @param {string} projName 工程名称 
* @param {string} projOverview 工程简介 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} supervisionLeader 监理单位项目负责人id 
* @param {string} supervisionLeaderName undefined 
* @param {integer} supervisionUnit 监理单位 
* @param {string} supervisionUnitName undefined 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmProjPackageExport(data) {
  return request({
    url: '/carbon-bpm/proj/package/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询新能源发电项目施工包列表
* @param {string} ancestors 组级别 
* @param {integer} constructionCompany 施工单位 
* @param {integer} constructionCompanyLeader 施工单位项目负责人 
* @param {string} constructionCompanyLeaderName undefined 
* @param {string} constructionCompanyName undefined 
* @param {integer} constructionLeader 建设单位项目负责人id 
* @param {string} constructionLeaderName undefined 
* @param {integer} constructionUnit 建设单位id 
* @param {string} constructionUnitName undefined 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} designLeader 设计单位项目负责人id 
* @param {string} designLeaderName undefined 
* @param {integer} designUnit 设计单位id 
* @param {string} designUnitName undefined 
* @param {integer} divisionId 工程质量验收范围划分表Id 
* @param {string} divisionType 类型：01：单位工程；02：子单位工程； 03：分部工程；04：子分部工程；05：分项工程；06：检验批 
* @param {integer} generalContractor 总包单位 
* @param {integer} generalContractorLeader 总包单位项目负责人 
* @param {string} generalContractorLeaderName undefined 
* @param {string} generalContractorName undefined 
* @param {integer} orderNum 排序 
* @param {object} params undefined 
* @param {integer} parentId 父类 
* @param {string} projCode 工程编码 
* @param {integer} projectId 项目Id 
* @param {integer} projId 工程Id 
* @param {string} projName 工程名称 
* @param {string} projOverview 工程简介 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} supervisionLeader 监理单位项目负责人id 
* @param {string} supervisionLeaderName undefined 
* @param {integer} supervisionUnit 监理单位 
* @param {string} supervisionUnitName undefined 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getBpmProjPackageList(query) {
  return request({
    url: '/carbon-bpm/proj/package/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取新能源发电项目施工包详细信息
* @param {integer} projId projId 
* @returns 
*/
export function getBpmProjPackageProjId(projId) {
  return request({
    url: '/carbon-bpm/proj/package/' + projId,
    method: 'get', 
  })
}

/**
* 删除新能源发电项目施工包
* @param {integer} projId projId 
* @returns 
*/
export function deleteBpmProjPackageProjId(projId) {
  return request({
    url: '/carbon-bpm/proj/package/' + projId,
    method: 'delete', 
  })
}

