/**
 * 项目工程部位进度API
 */
import request from '@/utils/request'

/**
* 修改项目工程部位进度
* @param {undefined} workSections workSections 
* @returns 
*/
export function putBpmProjProgressWorkstation(data) {
  return request({
    url: '/carbon-bpm/proj/progress/workstation',
    method: 'put', 
    data: data
  })
}

/**
* 导出项目工程部位进度列表
* @param {integer} actualDuration 实际工期 
* @param {string} actualEndDate 实际结束日期 
* @param {number} actualInvestment 实际投资 
* @param {string} actualStartDate 实际开始日期 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 最后修改时间 
* @param {integer} deptId 部门id 
* @param {integer} durationComparison 工期对比 
* @param {number} investmentComparison 投资对比 
* @param {string} lastUpdateTime 最后修改时间 
* @param {object} params undefined 
* @param {integer} parentId 父类 
* @param {integer} plannedDuration 计划工期 
* @param {string} plannedEndDate 计划结束日期 
* @param {number} plannedInvestment 计划投资 
* @param {string} plannedStartDate 计划开始日期 
* @param {string} projCode 工程编码 
* @param {integer} projectId 项目Id 
* @param {integer} projId Id 
* @param {string} projName 工程名称 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmProjProgressWorkstationExport(data) {
  return request({
    url: '/carbon-bpm/proj/progress/workstation/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询项目工程部位进度列表
* @param {integer} actualDuration 实际工期 
* @param {string} actualEndDate 实际结束日期 
* @param {number} actualInvestment 实际投资 
* @param {string} actualStartDate 实际开始日期 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 最后修改时间 
* @param {integer} deptId 部门id 
* @param {integer} durationComparison 工期对比 
* @param {number} investmentComparison 投资对比 
* @param {string} lastUpdateTime 最后修改时间 
* @param {object} params undefined 
* @param {integer} parentId 父类 
* @param {integer} plannedDuration 计划工期 
* @param {string} plannedEndDate 计划结束日期 
* @param {number} plannedInvestment 计划投资 
* @param {string} plannedStartDate 计划开始日期 
* @param {string} projCode 工程编码 
* @param {integer} projectId 项目Id 
* @param {integer} projId Id 
* @param {string} projName 工程名称 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getBpmProjProgressWorkstationList(query) {
  return request({
    url: '/carbon-bpm/proj/progress/workstation/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取项目工程部位进度详细信息
* @param {integer} projId projId 
* @returns 
*/
export function getBpmProjProgressWorkstationProjId(projId) {
  return request({
    url: '/carbon-bpm/proj/progress/workstation/' + projId,
    method: 'get', 
  })
}

