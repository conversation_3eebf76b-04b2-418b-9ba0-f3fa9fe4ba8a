/**
 * 流程分类API
 */
import request from '@/utils/request'

/**
* add
* @param {undefined} category category 
* @returns 
*/
export function postBpmWorkflowCategory(data) {
  return request({
    url: '/carbon-bpm/workflow/category',
    method: 'post', 
    data: data
  })
}

/**
* edit
* @param {undefined} category category 
* @returns 
*/
export function putBpmWorkflowCategory(data) {
  return request({
    url: '/carbon-bpm/workflow/category',
    method: 'put', 
    data: data
  })
}

/**
* export
* @param {integer} categoryId undefined 
* @param {string} categoryName undefined 
* @param {string} code undefined 
* @param {string} createBy undefined 
* @param {string} createTime undefined 
* @param {string} delFlag undefined 
* @param {object} params undefined 
* @param {string} remark undefined 
* @param {string} searchValue undefined 
* @param {string} updateBy undefined 
* @param {string} updateTime undefined 
* @returns 
*/
export function postBpmWorkflowCategoryExport(data) {
  return request({
    url: '/carbon-bpm/workflow/category/export',
    method: 'post', 
    data: data
  })
}

/**
* list
* @param {integer} categoryId undefined 
* @param {string} categoryName undefined 
* @param {string} code undefined 
* @param {string} createBy undefined 
* @param {string} createTime undefined 
* @param {string} delFlag undefined 
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {object} params undefined 
* @param {string} remark undefined 
* @param {string} searchValue undefined 
* @param {string} updateBy undefined 
* @param {string} updateTime undefined 
* @returns 
*/
export function getBpmWorkflowCategoryList(query) {
  return request({
    url: '/carbon-bpm/workflow/category/list',
    method: 'get', 
    params: query
  })
}

/**
* listAll
* @param {integer} categoryId undefined 
* @param {string} categoryName undefined 
* @param {string} code undefined 
* @param {string} createBy undefined 
* @param {string} createTime undefined 
* @param {string} delFlag undefined 
* @param {object} params undefined 
* @param {string} remark undefined 
* @param {string} searchValue undefined 
* @param {string} updateBy undefined 
* @param {string} updateTime undefined 
* @returns 
*/
export function getBpmWorkflowCategoryListAll(query) {
  return request({
    url: '/carbon-bpm/workflow/category/listAll',
    method: 'get', 
    params: query
  })
}

/**
* remove
* @param {string} categoryIds categoryIds 
* @returns 
*/
export function deleteBpmWorkflowCategoryCategoryIds(categoryIds) {
  return request({
    url: '/carbon-bpm/workflow/category/' + categoryIds,
    method: 'delete', 
  })
}

/**
* getInfo
* @param {integer} categoryId categoryId 
* @returns 
*/
export function getBpmWorkflowCategoryCategoryId(categoryId) {
  return request({
    url: '/carbon-bpm/workflow/category/' + categoryId,
    method: 'get', 
  })
}

