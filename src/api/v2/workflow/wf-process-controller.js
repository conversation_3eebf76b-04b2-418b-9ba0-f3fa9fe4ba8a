/**
 * 工作流流程管理API
 */
import request from '@/utils/request'

/**
* getBpmnXml
* @param {string} processDefId processDefId 
* @returns 
*/
export function getBpmWorkflowProcessBpmnXmlProcessDefId(processDefId) {
  return request({
    url: '/carbon-bpm/workflow/process/bpmnXml/' + processDefId,
    method: 'get', 
  })
}

/**
* claimExport
* @param {string} category undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function postBpmWorkflowProcessClaimExport(data) {
  return request({
    url: '/carbon-bpm/workflow/process/claimExport',
    method: 'post', 
    data: data
  })
}

/**
* claimProcessList
* @param {string} category undefined 
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function getBpmWorkflowProcessClaimList(query) {
  return request({
    url: '/carbon-bpm/workflow/process/claimList',
    method: 'get', 
    params: query
  })
}

/**
* copyExport
* @param {string} categoryId undefined 
* @param {integer} copyId undefined 
* @param {string} createBy undefined 
* @param {string} createTime undefined 
* @param {integer} originatorId undefined 
* @param {string} originatorName undefined 
* @param {object} params undefined 
* @param {string} processId undefined 
* @param {string} processName undefined 
* @param {string} searchValue undefined 
* @param {string} taskId undefined 
* @param {string} title undefined 
* @param {string} updateBy undefined 
* @param {string} updateTime undefined 
* @param {integer} userId undefined 
* @returns 
*/
export function postBpmWorkflowProcessCopyExport(data) {
  return request({
    url: '/carbon-bpm/workflow/process/copyExport',
    method: 'post', 
    data: data
  })
}

/**
* copyProcessList
* @param {string} categoryId undefined 
* @param {integer} copyId undefined 
* @param {string} createBy undefined 
* @param {string} createTime undefined 
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} originatorId undefined 
* @param {string} originatorName undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {object} params undefined 
* @param {string} processId undefined 
* @param {string} processName undefined 
* @param {string} searchValue undefined 
* @param {string} taskId undefined 
* @param {string} title undefined 
* @param {string} updateBy undefined 
* @param {string} updateTime undefined 
* @param {integer} userId undefined 
* @returns 
*/
export function getBpmWorkflowProcessCopyList(query) {
  return request({
    url: '/carbon-bpm/workflow/process/copyList',
    method: 'get', 
    params: query
  })
}

/**
* detail
* @param {string} procInsId procInsId 
* @param {string} taskId taskId 
* @returns 
*/
export function getBpmWorkflowProcessDetail(query) {
  return request({
    url: '/carbon-bpm/workflow/process/detail',
    method: 'get', 
    params: query
  })
}

/**
* finishedExport
* @param {string} category undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function postBpmWorkflowProcessFinishedExport(data) {
  return request({
    url: '/carbon-bpm/workflow/process/finishedExport',
    method: 'post', 
    data: data
  })
}

/**
* finishedProcessList
* @param {string} category undefined 
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function getBpmWorkflowProcessFinishedList(query) {
  return request({
    url: '/carbon-bpm/workflow/process/finishedList',
    method: 'get', 
    params: query
  })
}

/**
* getForm
* @param {string} definitionId definitionId 
* @param {string} deployId deployId 
* @param {string} procInsId procInsId 
* @returns 
*/
export function getBpmWorkflowProcessGetProcessForm(query) {
  return request({
    url: '/carbon-bpm/workflow/process/getProcessForm',
    method: 'get', 
    params: query
  })
}

/**
* delete
* @param {string} instanceIds instanceIds 
* @returns 
*/
export function deleteBpmWorkflowProcessInstanceInstanceIds(instanceIds) {
  return request({
    url: '/carbon-bpm/workflow/process/instance/' + instanceIds,
    method: 'delete', 
  })
}

/**
* startProcessList
* @param {string} category undefined 
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function getBpmWorkflowProcessList(query) {
  return request({
    url: '/carbon-bpm/workflow/process/list',
    method: 'get', 
    params: query
  })
}

/**
* ownExport
* @param {string} category undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function postBpmWorkflowProcessOwnExport(data) {
  return request({
    url: '/carbon-bpm/workflow/process/ownExport',
    method: 'post', 
    data: data
  })
}

/**
* ownProcessList
* @param {string} category undefined 
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function getBpmWorkflowProcessOwnList(query) {
  return request({
    url: '/carbon-bpm/workflow/process/ownList',
    method: 'get', 
    params: query
  })
}

/**
* start
* @param {string} processDefId processDefId 
* @param {undefined} variables variables 
* @returns 
*/
export function postBpmWorkflowProcessStartProcessDefId(processDefId) {
  return request({
    url: '/carbon-bpm/workflow/process/start/' + processDefId,
    method: 'post', 
  })
}

/**
* startExport
* @param {string} category undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function postBpmWorkflowProcessStartExport(data) {
  return request({
    url: '/carbon-bpm/workflow/process/startExport',
    method: 'post', 
    data: data
  })
}

/**
* todoExport
* @param {string} category undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function postBpmWorkflowProcessTodoExport(data) {
  return request({
    url: '/carbon-bpm/workflow/process/todoExport',
    method: 'post', 
    data: data
  })
}

/**
* todoProcessList
* @param {string} category undefined 
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function getBpmWorkflowProcessTodoList(query) {
  return request({
    url: '/carbon-bpm/workflow/process/todoList',
    method: 'get', 
    params: query
  })
}

