/**
 * 工作流流程实例管理API
 */
import request from '@/utils/request'

/**
* delete
* @param {string} deleteReason deleteReason 
* @param {string} instanceId instanceId 
* @returns 
*/
export function deleteBpmWorkflowInstanceDelete(query) {
  return request({
    url: '/carbon-bpm/workflow/instance/delete',
    method: 'delete', 
    params: query
  })
}

/**
* detail
* @param {string} deployId deployId 
* @param {string} procInsId procInsId 
* @returns 
*/
export function getBpmWorkflowInstanceDetail(query) {
  return request({
    url: '/carbon-bpm/workflow/instance/detail',
    method: 'get', 
    params: query
  })
}

/**
* stopProcessInstance
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowInstanceStopProcessInstance(data) {
  return request({
    url: '/carbon-bpm/workflow/instance/stopProcessInstance',
    method: 'post', 
    data: data
  })
}

/**
* updateState
* @param {string} instanceId instanceId 
* @param {integer} state state 
* @returns 
*/
export function postBpmWorkflowInstanceUpdateState(data) {
  return request({
    url: '/carbon-bpm/workflow/instance/updateState',
    method: 'post', 
    data: data
  })
}

