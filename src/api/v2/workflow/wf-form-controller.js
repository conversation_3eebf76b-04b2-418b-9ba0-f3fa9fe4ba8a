/**
 * 流程表单API
 */
import request from '@/utils/request'

/**
* add
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowForm(data) {
  return request({
    url: '/carbon-bpm/workflow/form',
    method: 'post', 
    data: data
  })
}

/**
* edit
* @param {undefined} bo bo 
* @returns 
*/
export function putBpmWorkflowForm(data) {
  return request({
    url: '/carbon-bpm/workflow/form',
    method: 'put', 
    data: data
  })
}

/**
* addDeployForm
* @param {undefined} deployForm deployForm 
* @returns 
*/
export function postBpmWorkflowFormAddDeployForm(data) {
  return request({
    url: '/carbon-bpm/workflow/form/addDeployForm',
    method: 'post', 
    data: data
  })
}

/**
* export
* @param {string} classContent undefined 
* @param {string} content undefined 
* @param {string} createBy undefined 
* @param {string} createTime undefined 
* @param {integer} formId undefined 
* @param {string} formName undefined 
* @param {integer} formStatus undefined 
* @param {object} params undefined 
* @param {string} remark undefined 
* @param {string} searchValue undefined 
* @param {string} updateBy undefined 
* @param {string} updateTime undefined 
* @returns 
*/
export function postBpmWorkflowFormExport(data) {
  return request({
    url: '/carbon-bpm/workflow/form/export',
    method: 'post', 
    data: data
  })
}

/**
* list
* @param {string} classContent undefined 
* @param {string} content undefined 
* @param {string} createBy undefined 
* @param {string} createTime undefined 
* @param {integer} formId undefined 
* @param {string} formName undefined 
* @param {integer} formStatus undefined 
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {object} params undefined 
* @param {string} remark undefined 
* @param {string} searchValue undefined 
* @param {string} updateBy undefined 
* @param {string} updateTime undefined 
* @returns 
*/
export function getBpmWorkflowFormList(query) {
  return request({
    url: '/carbon-bpm/workflow/form/list',
    method: 'get', 
    params: query
  })
}

/**
* release
* @param {integer} formId formId 
* @returns 
*/
export function getBpmWorkflowFormReleaseFormId(formId) {
  return request({
    url: '/carbon-bpm/workflow/form/release/' + formId,
    method: 'get', 
  })
}

/**
* remove
* @param {string} formIds formIds 
* @returns 
*/
export function deleteBpmWorkflowFormFormIds(formIds) {
  return request({
    url: '/carbon-bpm/workflow/form/' + formIds,
    method: 'delete', 
  })
}

/**
* getInfo
* @param {integer} formId formId 
* @returns 
*/
export function getBpmWorkflowFormFormId(formId) {
  return request({
    url: '/carbon-bpm/workflow/form/' + formId,
    method: 'get', 
  })
}

