/**
 * 工作流流程模型管理API
 */
import request from '@/utils/request'

/**
* add
* @param {undefined} modelBo modelBo 
* @returns 
*/
export function postBpmWorkflowModel(data) {
  return request({
    url: '/carbon-bpm/workflow/model',
    method: 'post', 
    data: data
  })
}

/**
* edit
* @param {undefined} modelBo modelBo 
* @returns 
*/
export function putBpmWorkflowModel(data) {
  return request({
    url: '/carbon-bpm/workflow/model',
    method: 'put', 
    data: data
  })
}

/**
* getBpmnXml
* @param {string} modelId modelId 
* @returns 
*/
export function getBpmWorkflowModelBpmnXmlModelId(modelId) {
  return request({
    url: '/carbon-bpm/workflow/model/bpmnXml/' + modelId,
    method: 'get', 
  })
}

/**
* deployModel
* @param {string} modelId modelId 
* @returns 
*/
export function postBpmWorkflowModelDeploy(data) {
  return request({
    url: '/carbon-bpm/workflow/model/deploy',
    method: 'post', 
    data: data
  })
}

/**
* export
* @param {string} bpmnXml undefined 
* @param {string} category undefined 
* @param {string} description undefined 
* @param {integer} formId undefined 
* @param {integer} formType undefined 
* @param {string} modelId undefined 
* @param {string} modelKey undefined 
* @param {string} modelName undefined 
* @param {boolean} newVersion undefined 
* @returns 
*/
export function postBpmWorkflowModelExport(data) {
  return request({
    url: '/carbon-bpm/workflow/model/export',
    method: 'post', 
    data: data
  })
}

/**
* historyList
* @param {string} bpmnXml undefined 
* @param {string} category undefined 
* @param {string} description undefined 
* @param {integer} formId undefined 
* @param {integer} formType undefined 
* @param {string} isAsc undefined 
* @param {string} modelId undefined 
* @param {string} modelKey undefined 
* @param {string} modelName undefined 
* @param {boolean} newVersion undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @returns 
*/
export function getBpmWorkflowModelHistoryList(query) {
  return request({
    url: '/carbon-bpm/workflow/model/historyList',
    method: 'get', 
    params: query
  })
}

/**
* latest
* @param {string} modelId modelId 
* @returns 
*/
export function postBpmWorkflowModelLatest(data) {
  return request({
    url: '/carbon-bpm/workflow/model/latest',
    method: 'post', 
    data: data
  })
}

/**
* list
* @param {string} bpmnXml undefined 
* @param {string} category undefined 
* @param {string} description undefined 
* @param {integer} formId undefined 
* @param {integer} formType undefined 
* @param {string} isAsc undefined 
* @param {string} modelId undefined 
* @param {string} modelKey undefined 
* @param {string} modelName undefined 
* @param {boolean} newVersion undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @returns 
*/
export function getBpmWorkflowModelList(query) {
  return request({
    url: '/carbon-bpm/workflow/model/list',
    method: 'get', 
    params: query
  })
}

/**
* save
* @param {undefined} modelBo modelBo 
* @returns 
*/
export function postBpmWorkflowModelSave(data) {
  return request({
    url: '/carbon-bpm/workflow/model/save',
    method: 'post', 
    data: data
  })
}

/**
* remove
* @param {string} modelIds modelIds 
* @returns 
*/
export function deleteBpmWorkflowModelModelIds(modelIds) {
  return request({
    url: '/carbon-bpm/workflow/model/' + modelIds,
    method: 'delete', 
  })
}

/**
* getInfo
* @param {string} modelId modelId 
* @returns 
*/
export function getBpmWorkflowModelModelId(modelId) {
  return request({
    url: '/carbon-bpm/workflow/model/' + modelId,
    method: 'get', 
  })
}

