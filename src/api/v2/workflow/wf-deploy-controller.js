/**
 * 流程部署API
 */
import request from '@/utils/request'

/**
* getBpmnXml
* @param {string} definitionId definitionId 
* @returns 
*/
export function getBpmWorkflowDeployBpmnXmlDefinitionId(definitionId) {
  return request({
    url: '/carbon-bpm/workflow/deploy/bpmnXml/' + definitionId,
    method: 'get', 
  })
}

/**
* changeState
* @param {string} definitionId definitionId 
* @param {string} state state 
* @returns 
*/
export function putBpmWorkflowDeployChangeState(data) {
  return request({
    url: '/carbon-bpm/workflow/deploy/changeState',
    method: 'put', 
    data: data
  })
}

/**
* start
* @param {string} deployId deployId 
* @returns 
*/
export function getBpmWorkflowDeployFormDeployId(deployId) {
  return request({
    url: '/carbon-bpm/workflow/deploy/form/' + deployId,
    method: 'get', 
  })
}

/**
* list
* @param {string} category undefined 
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {object} params undefined 
* @param {string} processKey undefined 
* @param {string} processName undefined 
* @param {string} state undefined 
* @returns 
*/
export function getBpmWorkflowDeployList(query) {
  return request({
    url: '/carbon-bpm/workflow/deploy/list',
    method: 'get', 
    params: query
  })
}

/**
* publishList
* @param {string} isAsc undefined 
* @param {string} orderByColumn undefined 
* @param {integer} pageNum undefined 
* @param {integer} pageSize undefined 
* @param {string} processKey processKey 
* @returns 
*/
export function getBpmWorkflowDeployPublishList(query) {
  return request({
    url: '/carbon-bpm/workflow/deploy/publishList',
    method: 'get', 
    params: query
  })
}

/**
* remove
* @param {string} deployIds deployIds 
* @returns 
*/
export function deleteBpmWorkflowDeployDeployIds(deployIds) {
  return request({
    url: '/carbon-bpm/workflow/deploy/' + deployIds,
    method: 'delete', 
  })
}

