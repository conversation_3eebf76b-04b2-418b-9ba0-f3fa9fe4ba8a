/**
 * 工作流任务管理API
 */
import request from '@/utils/request'

/**
* claim
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowTaskClaim(data) {
  return request({
    url: '/carbon-bpm/workflow/task/claim',
    method: 'post', 
    data: data
  })
}

/**
* complete
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowTaskComplete(data) {
  return request({
    url: '/carbon-bpm/workflow/task/complete',
    method: 'post', 
    data: data
  })
}

/**
* delegate
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowTaskDelegate(data) {
  return request({
    url: '/carbon-bpm/workflow/task/delegate',
    method: 'post', 
    data: data
  })
}

/**
* delete
* @param {undefined} bo bo 
* @returns 
*/
export function deleteBpmWorkflowTaskDelete(query) {
  return request({
    url: '/carbon-bpm/workflow/task/delete',
    method: 'delete', 
    params: query
  })
}

/**
* genProcessDiagram
* @param {string} processId processId 
* @returns 
*/
export function getBpmWorkflowTaskDiagramProcessId(processId) {
  return request({
    url: '/carbon-bpm/workflow/task/diagram/' + processId,
    method: 'get', 
  })
}

/**
* genProcessDiagram
* @param {string} processId processId 
* @returns 
*/
export function headBpmWorkflowTaskDiagramProcessId(processId) {
  return request({
    url: '/carbon-bpm/workflow/task/diagram/' + processId,
    method: 'head', 
  })
}

/**
* genProcessDiagram
* @param {string} processId processId 
* @returns 
*/
export function postBpmWorkflowTaskDiagramProcessId(processId) {
  return request({
    url: '/carbon-bpm/workflow/task/diagram/' + processId,
    method: 'post', 
  })
}

/**
* genProcessDiagram
* @param {string} processId processId 
* @returns 
*/
export function putBpmWorkflowTaskDiagramProcessId(processId) {
  return request({
    url: '/carbon-bpm/workflow/task/diagram/' + processId,
    method: 'put', 
  })
}

/**
* genProcessDiagram
* @param {string} processId processId 
* @returns 
*/
export function deleteBpmWorkflowTaskDiagramProcessId(processId) {
  return request({
    url: '/carbon-bpm/workflow/task/diagram/' + processId,
    method: 'delete', 
  })
}

/**
* genProcessDiagram
* @param {string} processId processId 
* @returns 
*/
export function optionsBpmWorkflowTaskDiagramProcessId(processId) {
  return request({
    url: '/carbon-bpm/workflow/task/diagram/' + processId,
    method: 'options', 
  })
}

/**
* genProcessDiagram
* @param {string} processId processId 
* @returns 
*/
export function patchBpmWorkflowTaskDiagramProcessId(processId) {
  return request({
    url: '/carbon-bpm/workflow/task/diagram/' + processId,
    method: 'patch', 
  })
}

/**
* processVariables
* @param {string} taskId taskId 
* @returns 
*/
export function getBpmWorkflowTaskProcessVariablesTaskId(taskId) {
  return request({
    url: '/carbon-bpm/workflow/task/processVariables/' + taskId,
    method: 'get', 
  })
}

/**
* taskReject
* @param {undefined} taskBo taskBo 
* @returns 
*/
export function postBpmWorkflowTaskReject(data) {
  return request({
    url: '/carbon-bpm/workflow/task/reject',
    method: 'post', 
    data: data
  })
}

/**
* taskReturn
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowTaskReturn(data) {
  return request({
    url: '/carbon-bpm/workflow/task/return',
    method: 'post', 
    data: data
  })
}

/**
* findReturnTaskList
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowTaskReturnList(data) {
  return request({
    url: '/carbon-bpm/workflow/task/returnList',
    method: 'post', 
    data: data
  })
}

/**
* revokeProcess
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowTaskRevokeProcess(data) {
  return request({
    url: '/carbon-bpm/workflow/task/revokeProcess',
    method: 'post', 
    data: data
  })
}

/**
* stopProcess
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowTaskStopProcess(data) {
  return request({
    url: '/carbon-bpm/workflow/task/stopProcess',
    method: 'post', 
    data: data
  })
}

/**
* transfer
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowTaskTransfer(data) {
  return request({
    url: '/carbon-bpm/workflow/task/transfer',
    method: 'post', 
    data: data
  })
}

/**
* unClaim
* @param {undefined} bo bo 
* @returns 
*/
export function postBpmWorkflowTaskUnClaim(data) {
  return request({
    url: '/carbon-bpm/workflow/task/unClaim',
    method: 'post', 
    data: data
  })
}

