/**
 * 新能源发电项目概况API
 */
import request from '@/utils/request'

/**
* 新增新能源发电项目概况
* @param {undefined} projNewEnergy projNewEnergy
* @returns
*/
export function postBpmEnergy(data) {
  return request({
    url: '/carbon-bpm/proj/energy',
    method: 'post',
    data: data
  })
}

/**
* 修改新能源发电项目概况
* @param {undefined} projNewEnergy projNewEnergy
* @returns
*/
export function putBpmEnergy(data) {
  return request({
    url: '/carbon-bpm/proj/energy',
    method: 'put',
    data: data
  })
}

/**
* 导出新能源发电项目概况列表
* @param {string} annualEquivalentHours 年等效利用小时数
* @param {number} annualPowerGeneration 年发电量
* @param {string} benefitAnalysis 效益分析
* @param {string} constructionCompany 施工单位
* @param {string} constructionCompanyLeader 施工单位项目负责人
* @param {string} constructionLeader 建设单位项目负责人
* @param {string} constructionNature 建设性质
* @param {string} constructionUnit 建设单位
* @param {string} createBy 创建者
* @param {string} createTime 创建时间
* @param {string} delFlag 施工单位项目负责人
* @param {integer} deptId 部门id
* @param {string} designLeader 设计单位项目负责人
* @param {integer} designLifespan 设计寿命(数字)
* @param {string} designUnit 设计单位
* @param {string} environmentalImpact 环境影响
* @param {string} fundingSource 资金来源
* @param {string} generalContractor 总包单位
* @param {string} generalContractorLeader 总包单位项目负责人
* @param {string} gridConnectionMode 并网方式
* @param {number} landArea 占地面积
* @param {string} landNature 土地性质
* @param {object} params undefined
* @param {string} plannedCompletionDate 计划竣工时间
* @param {string} plannedStartDate 计划开工时间
* @param {integer} projectId ${comment}
* @param {number} projectInvestment 项目投资
* @param {string} projectLocation 项目地点
* @param {string} projectName 项目名称
* @param {string} projectOwner 项目业主
* @param {number} projectScale 项目规模
* @param {string} projectType 项目类型
* @param {string} remark 备注
* @param {string} searchValue 搜索值
* @param {string} supervisionLeader 监理单位项目负责人
* @param {string} supervisionUnit 监理单位
* @param {string} updateBy 更新者
* @param {string} updateTime 更新时间
* @returns
*/
export function postBpmEnergyExport(data) {
  return request({
    url: '/carbon-bpm/proj/energy/export',
    method: 'post',
    data: data
  })
}

/**
* 查询新能源发电项目概况列表
* @param {string} annualEquivalentHours 年等效利用小时数
* @param {number} annualPowerGeneration 年发电量
* @param {string} benefitAnalysis 效益分析
* @param {string} constructionCompany 施工单位
* @param {string} constructionCompanyLeader 施工单位项目负责人
* @param {string} constructionLeader 建设单位项目负责人
* @param {string} constructionNature 建设性质
* @param {string} constructionUnit 建设单位
* @param {string} createBy 创建者
* @param {string} createTime 创建时间
* @param {string} delFlag 施工单位项目负责人
* @param {integer} deptId 部门id
* @param {string} designLeader 设计单位项目负责人
* @param {integer} designLifespan 设计寿命(数字)
* @param {string} designUnit 设计单位
* @param {string} environmentalImpact 环境影响
* @param {string} fundingSource 资金来源
* @param {string} generalContractor 总包单位
* @param {string} generalContractorLeader 总包单位项目负责人
* @param {string} gridConnectionMode 并网方式
* @param {number} landArea 占地面积
* @param {string} landNature 土地性质
* @param {object} params undefined
* @param {string} plannedCompletionDate 计划竣工时间
* @param {string} plannedStartDate 计划开工时间
* @param {integer} projectId ${comment}
* @param {number} projectInvestment 项目投资
* @param {string} projectLocation 项目地点
* @param {string} projectName 项目名称
* @param {string} projectOwner 项目业主
* @param {number} projectScale 项目规模
* @param {string} projectType 项目类型
* @param {string} remark 备注
* @param {string} searchValue 搜索值
* @param {string} supervisionLeader 监理单位项目负责人
* @param {string} supervisionUnit 监理单位
* @param {string} updateBy 更新者
* @param {string} updateTime 更新时间
* @returns
*/
export function getBpmEnergyList(query) {
  return request({
    url: '/carbon-bpm/proj/energy/list',
    method: 'get',
    params: query
  })
}

/**
* 删除新能源发电项目概况
* @param {string} projectIds projectIds
* @returns
*/
export function deleteBpmEnergyProjectIds(projectIds) {
  return request({
    url: '/carbon-bpm/proj/energy/' + projectIds,
    method: 'delete',
  })
}

/**
* 获取新能源发电项目概况详细信息
* @param {integer} projectId projectId
* @returns
*/
export function getBpmEnergyProjectId(projectId) {
  return request({
    url: '/carbon-bpm/proj/energy/' + projectId,
    method: 'get',
  })
}