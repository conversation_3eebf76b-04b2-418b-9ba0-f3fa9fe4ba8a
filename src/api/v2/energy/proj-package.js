import request from '@/utils/request'

// 查询新能源发电项目施工包列表
export function listPackage(query) {
  return request({
    url: '/carbon-bpm/proj/package/list',
    method: 'get',
    params: query
  })
}

// 查询新能源发电项目施工包详细
export function getPackage(projId) {
  return request({
    url: '/carbon-bpm/proj/package/' + projId,
    method: 'get'
  })
}

// 新增新能源发电项目施工包
export function addPackage(data) {
  return request({
    url: '/carbon-bpm/proj/package',
    method: 'post',
    data: data
  })
}

// 修改新能源发电项目施工包
export function updatePackage(data) {
  return request({
    url: '/carbon-bpm/proj/package',
    method: 'put',
    data: data
  })
}

// 删除新能源发电项目施工包
export function delPackage(projId) {
  return request({
    url: '/carbon-bpm/proj/package/' + projId,
    method: 'delete'
  })
}
