/**
 * dlt-5210-record-test-batch-controller
 */
import request from '@/utils/request'

/**
* add
* @param {undefined} dlt5210RecordTestBatch dlt5210RecordTestBatch 
* @returns 
*/
export function postBpmRecord(data) {
  return request({
    url: '/carbon-bpm/record',
    method: 'post', 
    data: data
  })
}

/**
* edit
* @param {undefined} dlt5210RecordTestBatch dlt5210RecordTestBatch 
* @returns 
*/
export function putBpmRecord(data) {
  return request({
    url: '/carbon-bpm/record',
    method: 'put', 
    data: data
  })
}

/**
* export
* @param {integer} acptCnLeaderId 施工单位-项目负责人id 
* @param {string} acptCnLeaderName 施工单位-项目负责人名称 
* @param {integer} acptConstructionId 施工单位id 
* @param {string} acptConstructionName 施工单位名称 
* @param {integer} acptGeneralContractId 总承包单位id 
* @param {string} acptGeneralContractName 总承包单位名称 
* @param {integer} acptGlctLeaderId 总承包单位-项目负责人id 
* @param {string} acptGlctLeaderName 总承包单位-项目负责人名称 
* @param {integer} acptProjId 验收部位id 
* @param {string} acptProjName 验收部位 
* @param {string} acptStandards 验收依据标准名称及编号 
* @param {string} acptStandardsDesc 验收依据备注 
* @param {integer} acptSubContractId 分包单位id 
* @param {integer} acptSubContractLeaderId 分包单位-项目负责人id 
* @param {string} acptSubContractLeaderName 分包单位-项目负责人名称 
* @param {string} acptSubContractName 分包单位名称 
* @param {integer} applyId ${comment} 
* @param {string} applyType 申请类型：检验批(DLT5210_TEST_BATCH) 
* @param {string} clientType 客户端 PC WECHAT_APPLET 
* @param {string} constructionStandards 施工执行标准名称及编号 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} divisionProjId 分部（子分部）工程编码Id 
* @param {string} divisionProjName 分部（子分部）工程名称 
* @param {integer} fdId dlt标准表单与流程关联关系id 
* @param {string} items[0].createBy 创建者 
* @param {string} items[0].createTime 创建时间 
* @param {string} items[0].delFlag 删除标志 
* @param {integer} items[0].deptId 部门id 
* @param {string} items[0].itemCap 容量 
* @param {integer} items[0].itemId 检查项目id 
* @param {string} items[0].itemMethod 检验方法与器具 
* @param {string} items[0].itemName 检查项目 
* @param {string} items[0].itemQs 质量标准 
* @param {string} items[0].itemSize 抽样量 
* @param {integer} items[0].itemType 类别：1:主控项目；2:一般项目 
* @param {object} items[0].params undefined 
* @param {integer} items[0].recordId DLT-5210 检验批质量验收记录id 
* @param {string} items[0].remark 备注 
* @param {string} items[0].searchValue 搜索值 
* @param {string} items[0].selfTestRecord 施工单位自检记录 
* @param {integer} items[0].serialNo 序号 
* @param {string} items[0].testResult 自检结果 
* @param {string} items[0].unit 单位 
* @param {string} items[0].updateBy 更新者 
* @param {string} items[0].updateTime 更新时间 
* @param {object} params undefined 
* @param {string} procInstId 流程实例id 
* @param {string} projCode 工程变编号 
* @param {integer} recordId 记录id 
* @param {string} recordTitle 记录标题 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} status 状态 0：草稿；1：提交； 
* @param {integer} subItemProjId 分项工程名称编码 
* @param {string} subItemProjName 分项工程名称 
* @param {string} tableNo 表号 
* @param {integer} unitProjId 单位（子单位）工程编码 
* @param {string} unitProjName 单位（子单位）工程名称 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @param {integer} workerId 专业工长 
* @param {string} workerName 专业工长 
* @returns 
*/
export function postBpmRecordExport(data) {
  return request({
    url: '/carbon-bpm/record/export',
    method: 'post', 
    data: data
  })
}

/**
* list
* @param {integer} acptCnLeaderId 施工单位-项目负责人id 
* @param {string} acptCnLeaderName 施工单位-项目负责人名称 
* @param {integer} acptConstructionId 施工单位id 
* @param {string} acptConstructionName 施工单位名称 
* @param {integer} acptGeneralContractId 总承包单位id 
* @param {string} acptGeneralContractName 总承包单位名称 
* @param {integer} acptGlctLeaderId 总承包单位-项目负责人id 
* @param {string} acptGlctLeaderName 总承包单位-项目负责人名称 
* @param {integer} acptProjId 验收部位id 
* @param {string} acptProjName 验收部位 
* @param {string} acptStandards 验收依据标准名称及编号 
* @param {string} acptStandardsDesc 验收依据备注 
* @param {integer} acptSubContractId 分包单位id 
* @param {integer} acptSubContractLeaderId 分包单位-项目负责人id 
* @param {string} acptSubContractLeaderName 分包单位-项目负责人名称 
* @param {string} acptSubContractName 分包单位名称 
* @param {integer} applyId ${comment} 
* @param {string} applyType 申请类型：检验批(DLT5210_TEST_BATCH) 
* @param {string} clientType 客户端 PC WECHAT_APPLET 
* @param {string} constructionStandards 施工执行标准名称及编号 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} divisionProjId 分部（子分部）工程编码Id 
* @param {string} divisionProjName 分部（子分部）工程名称 
* @param {integer} fdId dlt标准表单与流程关联关系id 
* @param {string} items[0].createBy 创建者 
* @param {string} items[0].createTime 创建时间 
* @param {string} items[0].delFlag 删除标志 
* @param {integer} items[0].deptId 部门id 
* @param {string} items[0].itemCap 容量 
* @param {integer} items[0].itemId 检查项目id 
* @param {string} items[0].itemMethod 检验方法与器具 
* @param {string} items[0].itemName 检查项目 
* @param {string} items[0].itemQs 质量标准 
* @param {string} items[0].itemSize 抽样量 
* @param {integer} items[0].itemType 类别：1:主控项目；2:一般项目 
* @param {object} items[0].params undefined 
* @param {integer} items[0].recordId DLT-5210 检验批质量验收记录id 
* @param {string} items[0].remark 备注 
* @param {string} items[0].searchValue 搜索值 
* @param {string} items[0].selfTestRecord 施工单位自检记录 
* @param {integer} items[0].serialNo 序号 
* @param {string} items[0].testResult 自检结果 
* @param {string} items[0].unit 单位 
* @param {string} items[0].updateBy 更新者 
* @param {string} items[0].updateTime 更新时间 
* @param {object} params undefined 
* @param {string} procInstId 流程实例id 
* @param {string} projCode 工程变编号 
* @param {integer} recordId 记录id 
* @param {string} recordTitle 记录标题 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} status 状态 0：草稿；1：提交； 
* @param {integer} subItemProjId 分项工程名称编码 
* @param {string} subItemProjName 分项工程名称 
* @param {string} tableNo 表号 
* @param {integer} unitProjId 单位（子单位）工程编码 
* @param {string} unitProjName 单位（子单位）工程名称 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @param {integer} workerId 专业工长 
* @param {string} workerName 专业工长 
* @returns 
*/
export function getBpmRecordList(query) {
  return request({
    url: '/carbon-bpm/record/list',
    method: 'get', 
    params: query
  })
}

/**
* getInfo
* @param {integer} recordId recordId 
* @returns 
*/
export function getBpmRecordRecordId(recordId) {
  return request({
    url: '/carbon-bpm/record/' + recordId,
    method: 'get', 
  })
}

