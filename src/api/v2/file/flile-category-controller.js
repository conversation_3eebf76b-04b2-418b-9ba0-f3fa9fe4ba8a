/**
 * 发文类型API
 */
import request from '@/utils/request'

/**
* 新增发文类型
* @param {undefined} flileCategory flileCategory 
* @returns 
*/
export function postBpmFileCategory(data) {
  return request({
    url: '/carbon-bpm/file/category',
    method: 'post', 
    data: data
  })
}

/**
* 修改发文类型
* @param {undefined} flileCategory flileCategory 
* @returns 
*/
export function putBpmFileCategory(data) {
  return request({
    url: '/carbon-bpm/file/category',
    method: 'put', 
    data: data
  })
}

/**
* 导出发文类型列表
* @param {string} ancestors 祖级列表 
* @param {string} cateCover 封面 
* @param {integer} cateId ${comment} 
* @param {string} cateName 分类名称 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 分类状态 
* @param {integer} deptId 部门id 
* @param {integer} fdId 流程表单主键 
* @param {integer} orderNum 显示顺序 
* @param {object} params undefined 
* @param {integer} parentId 父类 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} status 分类状态 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmFileCategoryExport(data) {
  return request({
    url: '/carbon-bpm/file/category/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询发文类型列表
* @param {string} ancestors 祖级列表 
* @param {string} cateCover 封面 
* @param {integer} cateId ${comment} 
* @param {string} cateName 分类名称 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 分类状态 
* @param {integer} deptId 部门id 
* @param {integer} fdId 流程表单主键 
* @param {integer} orderNum 显示顺序 
* @param {object} params undefined 
* @param {integer} parentId 父类 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} status 分类状态 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getBpmFileCategoryList(query) {
  return request({
    url: '/carbon-bpm/file/category/list',
    method: 'get', 
    params: query
  })
}

/**
* 删除发文类型
* @param {string} cateIds cateIds 
* @returns 
*/
export function deleteBpmFileCategoryCateIds(cateIds) {
  return request({
    url: '/carbon-bpm/file/category/' + cateIds,
    method: 'delete', 
  })
}

/**
* 获取发文类型详细信息
* @param {integer} cateId cateId 
* @returns 
*/
export function getBpmFileCategoryCateId(cateId) {
  return request({
    url: '/carbon-bpm/file/category/' + cateId,
    method: 'get', 
  })
}

