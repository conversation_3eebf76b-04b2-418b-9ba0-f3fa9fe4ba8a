/**
 * 文件类型API
 */
import request from '@/utils/request'

/**
* 新增文件类型
* @param {undefined} fileCategory fileCategory 
* @returns 
*/
export function postBpmFileCategory(data) {
  return request({
    url: '/carbon-bpm/file/category',
    method: 'post', 
    data: data
  })
}

/**
* 修改文件类型
* @param {undefined} fileCategory fileCategory 
* @returns 
*/
export function putBpmFileCategory(data) {
  return request({
    url: '/carbon-bpm/file/category',
    method: 'put', 
    data: data
  })
}

/**
* 导出文件类型列表
* @param {string} ancestors 祖级列表 
* @param {string} cateCover 封面 
* @param {integer} cateId id 
* @param {string} cateName 分类名称 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} fdId 流程表单主键 
* @param {integer} orderNum 显示顺序 
* @param {object} params undefined 
* @param {integer} parentId 父类 
* @param {integer} procdef.classId 存储实例classId，关联物联模型类Id 
* @param {string} procdef.className 存储实例className，关联物联模型类class_name 
* @param {string} procdef.createBy 创建者 
* @param {string} procdef.createTime 创建时间 
* @param {string} procdef.delFlag 存储实例className，关联物联模型类class_name 
* @param {string} procdef.deploymentId 流程部署id 
* @param {integer} procdef.deptId 部门id 
* @param {integer} procdef.fdId ${comment} 
* @param {string} procdef.formName 表单名称 
* @param {object} procdef.params undefined 
* @param {string} procdef.procdefId 流程id 
* @param {string} procdef.processKey 流程key 
* @param {string} procdef.processService 表单处理bean 
* @param {string} procdef.remark 备注 
* @param {string} procdef.searchValue 搜索值 
* @param {string} procdef.updateBy 更新者 
* @param {string} procdef.updateTime 更新时间 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} status 分类状态 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmFileCategoryExport(data) {
  return request({
    url: '/carbon-bpm/file/category/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询文件类型列表
* @param {string} ancestors 祖级列表 
* @param {string} cateCover 封面 
* @param {integer} cateId id 
* @param {string} cateName 分类名称 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} fdId 流程表单主键 
* @param {integer} orderNum 显示顺序 
* @param {object} params undefined 
* @param {integer} parentId 父类 
* @param {integer} procdef.classId 存储实例classId，关联物联模型类Id 
* @param {string} procdef.className 存储实例className，关联物联模型类class_name 
* @param {string} procdef.createBy 创建者 
* @param {string} procdef.createTime 创建时间 
* @param {string} procdef.delFlag 存储实例className，关联物联模型类class_name 
* @param {string} procdef.deploymentId 流程部署id 
* @param {integer} procdef.deptId 部门id 
* @param {integer} procdef.fdId ${comment} 
* @param {string} procdef.formName 表单名称 
* @param {object} procdef.params undefined 
* @param {string} procdef.procdefId 流程id 
* @param {string} procdef.processKey 流程key 
* @param {string} procdef.processService 表单处理bean 
* @param {string} procdef.remark 备注 
* @param {string} procdef.searchValue 搜索值 
* @param {string} procdef.updateBy 更新者 
* @param {string} procdef.updateTime 更新时间 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} status 分类状态 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getBpmFileCategoryList(query) {
  return request({
    url: '/carbon-bpm/file/category/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取文件类型详细信息
* @param {integer} cateId cateId 
* @returns 
*/
export function getBpmFileCategoryCateId(cateId) {
  return request({
    url: '/carbon-bpm/file/category/' + cateId,
    method: 'get', 
  })
}

/**
* 删除文件类型
* @param {integer} cateId cateId 
* @returns 
*/
export function deleteBpmFileCategoryCateId(cateId) {
  return request({
    url: '/carbon-bpm/file/category/' + cateId,
    method: 'delete', 
  })
}

