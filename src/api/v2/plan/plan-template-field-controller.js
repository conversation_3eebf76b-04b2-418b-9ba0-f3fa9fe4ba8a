/**
 * 计划模板-字段API
 */
import request from '@/utils/request'

/**
* 新增计划模板-字段
* @param {undefined} planTemplateField planTemplateField 
* @returns 
*/
export function postBpmPlanTemplateField(data) {
  return request({
    url: '/carbon-bpm/plan/template/field',
    method: 'post', 
    data: data
  })
}

/**
* 修改计划模板-字段
* @param {undefined} planTemplateField planTemplateField 
* @returns 
*/
export function putBpmPlanTemplateField(data) {
  return request({
    url: '/carbon-bpm/plan/template/field',
    method: 'put', 
    data: data
  })
}

/**
* 导出计划模板-字段列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} displayOrder 显示顺序 
* @param {integer} displayStatus 显示状态(0-禁用,1-启用) 
* @param {string} fieldDesc 字段描述 
* @param {integer} fieldId ${comment} 
* @param {string} fieldLabel 字段显示名称 
* @param {string} fieldName 字段名称 
* @param {string} fieldOwner 字段拥有者 
* @param {string} fieldType  COMMENT '字段类型(varchar,int,decimal,text,datetime,date,bigint等)' 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} rwFlag 读写标识：READ_WRITE：读写；READ_ONLY：只读 
* @param {string} searchValue 搜索值 
* @param {integer} templateId 模板ID 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmPlanTemplateFieldExport(data) {
  return request({
    url: '/carbon-bpm/plan/template/field/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询计划模板-字段列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} displayOrder 显示顺序 
* @param {integer} displayStatus 显示状态(0-禁用,1-启用) 
* @param {string} fieldDesc 字段描述 
* @param {integer} fieldId ${comment} 
* @param {string} fieldLabel 字段显示名称 
* @param {string} fieldName 字段名称 
* @param {string} fieldOwner 字段拥有者 
* @param {string} fieldType  COMMENT '字段类型(varchar,int,decimal,text,datetime,date,bigint等)' 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} rwFlag 读写标识：READ_WRITE：读写；READ_ONLY：只读 
* @param {string} searchValue 搜索值 
* @param {integer} templateId 模板ID 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getBpmPlanTemplateFieldList(query) {
  return request({
    url: '/carbon-bpm/plan/template/field/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取计划模板-字段详细信息
* @param {integer} fieldId fieldId 
* @returns 
*/
export function getBpmPlanTemplateFieldFieldId(fieldId) {
  return request({
    url: '/carbon-bpm/plan/template/field/' + fieldId,
    method: 'get', 
  })
}

/**
* 删除计划模板-字段
* @param {integer} fieldId fieldId 
* @returns 
*/
export function deleteBpmPlanTemplateFieldFieldId(fieldId) {
  return request({
    url: '/carbon-bpm/plan/template/field/' + fieldId,
    method: 'delete', 
  })
}

