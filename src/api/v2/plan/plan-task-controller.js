/**
 * 计划任务API
 */
import request from '@/utils/request'

/**
* 新增计划任务
* @param {undefined} planTask planTask 
* @returns 
*/
export function postBpmPlanTask(data) {
  return request({
    url: '/carbon-bpm/plan/task',
    method: 'post', 
    data: data
  })
}

/**
* 修改计划任务
* @param {undefined} planTask planTask 
* @returns 
*/
export function putBpmPlanTask(data) {
  return request({
    url: '/carbon-bpm/plan/task',
    method: 'put', 
    data: data
  })
}

/**
* 导出计划任务列表
* @param {integer} actualDuration 实际工期(天) 
* @param {string} actualEndDate 实际结束日期 
* @param {string} actualStartDate 实际开始日期 
* @param {string} ancestors 祖级 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} extendedFieldValue 扩展字段值，存放扩展字段JSON 
* @param {integer} isMilestone 是否里程碑(0否1是) 
* @param {integer} offsetDays 偏移量(天) 
* @param {string} offsetType 偏移类型 
* @param {object} params undefined 
* @param {integer} parentTaskId 上级任务ID 
* @param {integer} planId 计划id 
* @param {integer} plannedDuration 计划工期(天) 
* @param {string} plannedEndDate 计划结束日期 
* @param {string} plannedStartDate 计划开始日期 
* @param {integer} predecessorTaskId 前置任务ID 
* @param {string} predecessorType 前置类型 
* @param {integer} relatedPlanId 关联计划表ID 
* @param {integer} relatedTaskId 关联任务ID 
* @param {string} remark 备注 
* @param {string} responsiblePerson 责任人 
* @param {string} searchValue 搜索值 
* @param {integer} serialNo 序号 
* @param {string} taskCode 工作编码 
* @param {string} taskContent 任务内容详情 
* @param {integer} taskId ${comment} 
* @param {integer} taskLevel 任务层级 
* @param {string} taskName 任务名称 
* @param {string} taskStatus 任务状态 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmPlanTaskExport(data) {
  return request({
    url: '/carbon-bpm/plan/task/export',
    method: 'post', 
    data: data
  })
}

/**
* 根据施工包导入计划任务
* @param {undefined} planInst planInst 
* @returns 
*/
export function postBpmPlanTaskImportProjpackage(data) {
  return request({
    url: '/carbon-bpm/plan/task/import/projpackage',
    method: 'post', 
    data: data
  })
}

/**
* 查询计划任务列表
* @param {integer} actualDuration 实际工期(天) 
* @param {string} actualEndDate 实际结束日期 
* @param {string} actualStartDate 实际开始日期 
* @param {string} ancestors 祖级 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} extendedFieldValue 扩展字段值，存放扩展字段JSON 
* @param {integer} isMilestone 是否里程碑(0否1是) 
* @param {integer} offsetDays 偏移量(天) 
* @param {string} offsetType 偏移类型 
* @param {object} params undefined 
* @param {integer} parentTaskId 上级任务ID 
* @param {integer} planId 计划id 
* @param {integer} plannedDuration 计划工期(天) 
* @param {string} plannedEndDate 计划结束日期 
* @param {string} plannedStartDate 计划开始日期 
* @param {integer} predecessorTaskId 前置任务ID 
* @param {string} predecessorType 前置类型 
* @param {integer} relatedPlanId 关联计划表ID 
* @param {integer} relatedTaskId 关联任务ID 
* @param {string} remark 备注 
* @param {string} responsiblePerson 责任人 
* @param {string} searchValue 搜索值 
* @param {integer} serialNo 序号 
* @param {string} taskCode 工作编码 
* @param {string} taskContent 任务内容详情 
* @param {integer} taskId ${comment} 
* @param {integer} taskLevel 任务层级 
* @param {string} taskName 任务名称 
* @param {string} taskStatus 任务状态 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getBpmPlanTaskList(query) {
  return request({
    url: '/carbon-bpm/plan/task/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取计划任务详细信息
* @param {integer} taskId taskId 
* @returns 
*/
export function getBpmPlanTaskTaskId(taskId) {
  return request({
    url: '/carbon-bpm/plan/task/' + taskId,
    method: 'get', 
  })
}

/**
* 删除计划任务
* @param {integer} taskId taskId 
* @returns 
*/
export function deleteBpmPlanTaskTaskId(taskId) {
  return request({
    url: '/carbon-bpm/plan/task/' + taskId,
    method: 'delete', 
  })
}

