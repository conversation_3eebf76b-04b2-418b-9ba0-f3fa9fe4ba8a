/**
 * 计划模板API
 */
import request from '@/utils/request'

/**
* 新增计划模板
* @param {undefined} planTemplate planTemplate 
* @returns 
*/
export function postBpmPlanTemplate(data) {
  return request({
    url: '/carbon-bpm/plan/template',
    method: 'post', 
    data: data
  })
}

/**
* 修改计划模板
* @param {undefined} planTemplate planTemplate 
* @returns 
*/
export function putBpmPlanTemplate(data) {
  return request({
    url: '/carbon-bpm/plan/template',
    method: 'put', 
    data: data
  })
}

/**
* 导出计划模板列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} enableStatus 启用状态(0-禁用,1-启用) 
* @param {object} params undefined 
* @param {integer} projectId 项目id 
* @param {integer} publishStatus 启用状态(0-未发布,1-已发布) 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} templateId ${comment} 
* @param {string} templateName 模板名称 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmPlanTemplateExport(data) {
  return request({
    url: '/carbon-bpm/plan/template/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询计划模板列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {integer} enableStatus 启用状态(0-禁用,1-启用) 
* @param {object} params undefined 
* @param {integer} projectId 项目id 
* @param {integer} publishStatus 启用状态(0-未发布,1-已发布) 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} templateId ${comment} 
* @param {string} templateName 模板名称 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getBpmPlanTemplateList(query) {
  return request({
    url: '/carbon-bpm/plan/template/list',
    method: 'get', 
    params: query
  })
}

/**
* 发布计划模板
* @param {integer} templateId templateId 
* @returns 
*/
export function getBpmPlanTemplatePublishTemplateId(templateId) {
  return request({
    url: '/carbon-bpm/plan/template/publish/' + templateId,
    method: 'get', 
  })
}

/**
* 获取计划模板详细信息
* @param {integer} templateId templateId 
* @returns 
*/
export function getBpmPlanTemplateTemplateId(templateId) {
  return request({
    url: '/carbon-bpm/plan/template/' + templateId,
    method: 'get', 
  })
}

/**
* 删除计划模板
* @param {integer} templateId templateId 
* @returns 
*/
export function deleteBpmPlanTemplateTemplateId(templateId) {
  return request({
    url: '/carbon-bpm/plan/template/' + templateId,
    method: 'delete', 
  })
}

