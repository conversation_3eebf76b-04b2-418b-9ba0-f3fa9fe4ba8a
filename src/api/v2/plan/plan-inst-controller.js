/**
 * 计划主API
 */
import request from '@/utils/request'

/**
* 新增计划主
* @param {undefined} planInst planInst 
* @returns 
*/
export function postBpmPlanInst(data) {
  return request({
    url: '/carbon-bpm/plan/inst',
    method: 'post', 
    data: data
  })
}

/**
* 修改计划主
* @param {undefined} planInst planInst 
* @returns 
*/
export function putBpmPlanInst(data) {
  return request({
    url: '/carbon-bpm/plan/inst',
    method: 'put', 
    data: data
  })
}

/**
* 导出计划主列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} ganttConfig 根特图配置json字符串 
* @param {object} params undefined 
* @param {integer} planId ${comment} 
* @param {string} planName 计划名称 
* @param {integer} projectId 项目id 
* @param {array} projPackageIds undefined 
* @param {string} remark 备注 
* @param {integer} rootTaskId 根任务ID 
* @param {string} searchValue 搜索值 
* @param {integer} templateId 模板ID 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmPlanInstExport(data) {
  return request({
    url: '/carbon-bpm/plan/inst/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询计划主列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} ganttConfig 根特图配置json字符串 
* @param {object} params undefined 
* @param {integer} planId ${comment} 
* @param {string} planName 计划名称 
* @param {integer} projectId 项目id 
* @param {array} projPackageIds undefined 
* @param {string} remark 备注 
* @param {integer} rootTaskId 根任务ID 
* @param {string} searchValue 搜索值 
* @param {integer} templateId 模板ID 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getBpmPlanInstList(query) {
  return request({
    url: '/carbon-bpm/plan/inst/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取计划主详细信息
* @param {integer} planId planId 
* @returns 
*/
export function getBpmPlanInstPlanId(planId) {
  return request({
    url: '/carbon-bpm/plan/inst/' + planId,
    method: 'get', 
  })
}

/**
* 删除计划主
* @param {integer} planId planId 
* @returns 
*/
export function deleteBpmPlanInstPlanId(planId) {
  return request({
    url: '/carbon-bpm/plan/inst/' + planId,
    method: 'delete', 
  })
}

