/**
 * 工程质量验收范围划分API
 */
import request from '@/utils/request'

/**
* add
* @param {undefined} projectDivision projectDivision 
* @returns 
*/
export function postBpmQaDivision(data) {
  return request({
    url: '/carbon-bpm/qa/division',
    method: 'post', 
    data: data
  })
}

/**
* edit
* @param {undefined} projectDivision projectDivision 
* @returns 
*/
export function putBpmQaDivision(data) {
  return request({
    url: '/carbon-bpm/qa/division',
    method: 'put', 
    data: data
  })
}

/**
* export
* @param {string} ancestors 组级别 
* @param {string} c01 施工单位， 1：打钩，0：不打钩 
* @param {string} c02 总承包单位，1：打钩，0：不打钩 
* @param {string} c03 勘察单位，1：打钩，0：不打钩 
* @param {string} c04 设计单位，1：打钩，0：不打钩 
* @param {string} c05 监理单位，1：打钩，0：不打钩 
* @param {string} c06 建设单位：1：打钩，0：不打钩 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 排序 
* @param {integer} deptId 部门id 
* @param {integer} divisionId ${comment} 
* @param {string} divisionType 类型：01：单位工程；02：子单位工程； 03：分部工程；04：子分部工程；05：分项工程；06：检验批 
* @param {integer} fdId 标准编号 
* @param {string} formProcess.createBy 创建者 
* @param {string} formProcess.createTime 创建时间 
* @param {string} formProcess.delFlag 删除标志 
* @param {string} formProcess.deploymentId 流程部署id 
* @param {integer} formProcess.deptId 部门id 
* @param {integer} formProcess.fdId ${comment} 
* @param {integer} formProcess.formDataId 表单初始化数据 主键 
* @param {string} formProcess.formDataPath 表单初始化数据地址 
* @param {string} formProcess.formDataType 表单数据类型 
* @param {string} formProcess.formName 表单名称 
* @param {object} formProcess.params undefined 
* @param {string} formProcess.procdefId 流程id 
* @param {string} formProcess.processKey 流程key 
* @param {string} formProcess.processService 表单处理bean 
* @param {string} formProcess.remark 备注 
* @param {string} formProcess.searchValue 搜索值 
* @param {string} formProcess.updateBy 更新者 
* @param {string} formProcess.updateTime 更新时间 
* @param {integer} orderNum 排序 
* @param {object} params undefined 
* @param {integer} parentId 父类 
* @param {string} projCode 工程编码 
* @param {integer} projId 项目id 
* @param {string} projName 工程名称 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmQaDivisionExport(data) {
  return request({
    url: '/carbon-bpm/qa/division/export',
    method: 'post', 
    data: data
  })
}

/**
* list
* @param {undefined} projectDivision projectDivision 
* @returns 
*/
export function postBpmQaDivisionListAll(data) {
  return request({
    url: '/carbon-bpm/qa/division/list/all',
    method: 'post', 
    data: data
  })
}

/**
* getInfo
* @param {integer} divisionId divisionId 
* @returns 
*/
export function getBpmQaDivisionDivisionId(divisionId) {
  return request({
    url: '/carbon-bpm/qa/division/' + divisionId,
    method: 'get', 
  })
}

/**
* remove
* @param {integer} divisionId divisionId 
* @returns 
*/
export function deleteBpmQaDivisionDivisionId(divisionId) {
  return request({
    url: '/carbon-bpm/qa/division/' + divisionId,
    method: 'delete', 
  })
}

