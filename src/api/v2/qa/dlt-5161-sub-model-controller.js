/**
 * DLT-5161 分项工程质量验收API
 */
import request from '@/utils/request'

/**
* add
* @param {undefined} dlt5161SubModel dlt5161SubModel 
* @returns 
*/
export function postBpmQaDtl5161Model(data) {
  return request({
    url: '/carbon-bpm/qa/dtl5161/model',
    method: 'post', 
    data: data
  })
}

/**
* edit
* @param {undefined} dlt5161SubModel dlt5161SubModel 
* @returns 
*/
export function putBpmQaDtl5161Model(data) {
  return request({
    url: '/carbon-bpm/qa/dtl5161/model',
    method: 'put', 
    data: data
  })
}

/**
* export
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} modelCode 编号 
* @param {string} modelContent 标准表单json数据 
* @param {string} modelDesc 说明 
* @param {integer} modelId ${comment} 
* @param {string} modelName 名称 
* @param {string} modelSource 质量标准来源文件名 
* @param {integer} modelVersion 版本 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} serialNo 序号 
* @param {string} status 状态 0：草稿；1：发布；2：停用 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postBpmQaDtl5161ModelExport(data) {
  return request({
    url: '/carbon-bpm/qa/dtl5161/model/export',
    method: 'post', 
    data: data
  })
}

/**
* list
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} modelCode 编号 
* @param {string} modelContent 标准表单json数据 
* @param {string} modelDesc 说明 
* @param {integer} modelId ${comment} 
* @param {string} modelName 名称 
* @param {string} modelSource 质量标准来源文件名 
* @param {integer} modelVersion 版本 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {integer} serialNo 序号 
* @param {string} status 状态 0：草稿；1：发布；2：停用 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getBpmQaDtl5161ModelList(query) {
  return request({
    url: '/carbon-bpm/qa/dtl5161/model/list',
    method: 'get', 
    params: query
  })
}

/**
* getInfo
* @param {integer} modelId modelId 
* @returns 
*/
export function getBpmQaDtl5161ModelModelId(modelId) {
  return request({
    url: '/carbon-bpm/qa/dtl5161/model/' + modelId,
    method: 'get', 
  })
}

/**
* remove
* @param {integer} modelId modelId 
* @returns 
*/
export function deleteBpmQaDtl5161ModelModelId(modelId) {
  return request({
    url: '/carbon-bpm/qa/dtl5161/model/' + modelId,
    method: 'delete', 
  })
}

