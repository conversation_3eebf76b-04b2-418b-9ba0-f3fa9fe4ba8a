import request from '@/utils/request';

// 修改
export function putDevApiUiFormDesigner(data) {
  return request({
    url: '/ui/form/designer',
    method: 'put',
    data: data,
  });
}

// 新增
export function postDevApiUiFormDesigner(data) {
  return request({
    url: '/ui/form/designer',
    method: 'post',
    data: data,
  });
}

// 导出列表
export function postDevApiUiFormDesignerExport(data) {
  return request({
    url: '/ui/form/designer/export',
    method: 'post',
    data: data,
  });
}

// 查询列表
export function getDevApiUiFormDesignerList(query) {
  return request({
    url: '/ui/form/designer/list',
    method: 'get',
    params: query,
  });
}

// 发布
export function getDevApiUiFormDesignerReleaseFormId(formId) {
  return request({
    url: '/ui/form/designer/release/' + formId,
    method: 'get',
  });
}

// 停止
export function getDevApiUiFormDesignerStopFormId(formId) {
  return request({
    url: '/ui/form/designer/stop/' + formId,
    method: 'get',
  });
}

// 获取详细信息
export function getDevApiUiFormDesignerFormId(formId) {
  return request({
    url: '/ui/form/designer/' + formId,
    method: 'get',
  });
}

// 删除
export function deleteDevApiUiFormDesignerFormId(formId) {
  return request({
    url: '/ui/form/designer/' + formId,
    method: 'delete',
  });
}
