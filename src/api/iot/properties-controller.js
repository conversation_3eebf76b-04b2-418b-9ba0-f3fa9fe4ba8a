import request from '@/utils/request';

// 修改
export function putDevApiIotProperties(data) {
  return request({
    url: '/iot/properties',
    method: 'put',
    data: data,
  });
}

// 新增
export function postDevApiIotProperties(data) {
  return request({
    url: '/iot/properties',
    method: 'post',
    data: data,
  });
}

// 导出列表
export function postDevApiIotPropertiesExport(data) {
  return request({
    url: '/iot/properties/export',
    method: 'post',
    data: data,
  });
}

// 查询列表
export function getDevApiIotPropertiesList(query) {
  return request({
    url: '/iot/properties/list',
    method: 'get',
    params: query,
  });
}

// 获取详细信息
export function getDevApiIotPropertiesPropertyId(propertyId) {
  return request({
    url: '/iot/properties/' + propertyId,
    method: 'get',
  });
}

// 删除
export function deleteDevApiIotPropertiesPropertyId(propertyId) {
  return request({
    url: '/iot/properties/' + propertyId,
    method: 'delete',
  });
}
