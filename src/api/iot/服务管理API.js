import request from '@/utils/request';

// 修改
export function putDevApiIotService(data) {
  return request({
    url: '/dev-api/iot/service',
    method: 'put',
    data: data,
  });
}

// 新增
export function postDevApiIotService(data) {
  return request({
    url: '/dev-api/iot/service',
    method: 'post',
    data: data,
  });
}

// 导出列表
export function postDevApiIotServiceExport(data) {
  return request({
    url: '/dev-api/iot/service/export',
    method: 'post',
    data: data,
  });
}

// 查询列表
export function getDevApiIotServiceList(query) {
  return request({
    url: '/dev-api/iot/service/list',
    method: 'get',
    params: query,
  });
}

// 获取详细信息
export function getDevApiIotServiceServiceId(serviceId) {
  return request({
    url: '/dev-api/iot/service/' + serviceId,
    method: 'get',
  });
}

// 删除
export function deleteDevApiIotServiceServiceId(serviceId) {
  return request({
    url: '/dev-api/iot/service/' + serviceId,
    method: 'delete',
  });
}
