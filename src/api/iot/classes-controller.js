import request from '@/utils/request';

// 修改
export function putDevApiIotClasses(data) {
  return request({
    url: '/iot/classes',
    method: 'put',
    data: data,
  });
}

// 新增
export function postDevApiIotClasses(data) {
  return request({
    url: '/iot/classes',
    method: 'post',
    data: data,
  });
}

// 导出列表
export function postDevApiIotClassesExport(data) {
  return request({
    url: '/iot/classes/export',
    method: 'post',
    data: data,
  });
}

// 查询列表
export function getDevApiIotClassesList(query) {
  return request({
    url: '/iot/classes/list',
    method: 'get',
    params: query,
  });
}

// 获取详细信息
export function getDevApiIotClassesClassId(classId) {
  return request({
    url: '/iot/classes/' + classId,
    method: 'get',
  });
}

// 删除
export function deleteDevApiIotClassesClassId(classId) {
  return request({
    url: '/iot/classes/' + classId,
    method: 'delete',
  });
}
