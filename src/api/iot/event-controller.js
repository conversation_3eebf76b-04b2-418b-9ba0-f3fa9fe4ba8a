import request from '@/utils/request';

// 修改
export function putDevApiIotEvent(data) {
  return request({
    url: '/iot/event',
    method: 'put',
    data: data,
  });
}

// 新增
export function postDevApiIotEvent(data) {
  return request({
    url: '/iot/event',
    method: 'post',
    data: data,
  });
}

// 导出列表
export function postDevApiIotEventExport(data) {
  return request({
    url: '/iot/event/export',
    method: 'post',
    data: data,
  });
}

// 查询列表
export function getDevApiIotEventList(query) {
  return request({
    url: '/iot/event/list',
    method: 'get',
    params: query,
  });
}

// 获取详细信息
export function getDevApiIotEventEventId(eventId) {
  return request({
    url: '/iot/event/' + eventId,
    method: 'get',
  });
}

// 删除
export function deleteDevApiIotEventEventId(eventId) {
  return request({
    url: '/iot/event/' + eventId,
    method: 'delete',
  });
}
