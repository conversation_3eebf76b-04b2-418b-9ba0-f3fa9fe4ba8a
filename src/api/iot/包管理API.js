import request from '@/utils/request';

// 修改
export function putDevApiIotPackages(data) {
  return request({
    url: '/dev-api/iot/packages',
    method: 'put',
    data: data,
  });
}

// 新增
export function postDevApiIotPackages(data) {
  return request({
    url: '/dev-api/iot/packages',
    method: 'post',
    data: data,
  });
}

// 导出列表
export function postDevApiIotPackagesExport(data) {
  return request({
    url: '/dev-api/iot/packages/export',
    method: 'post',
    data: data,
  });
}

// 查询列表
export function getDevApiIotPackagesList(query) {
  return request({
    url: '/dev-api/iot/packages/list',
    method: 'get',
    params: query,
  });
}

// 获取详细信息
export function getDevApiIotPackagesPackageId(packageId) {
  return request({
    url: '/dev-api/iot/packages/' + packageId,
    method: 'get',
  });
}

// 删除
export function deleteDevApiIotPackagesPackageId(packageId) {
  return request({
    url: '/dev-api/iot/packages/' + packageId,
    method: 'delete',
  });
}
