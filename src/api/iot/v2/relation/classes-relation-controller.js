/**
 * 类约束关系API
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} classesRelation classesRelation 
* @returns 
*/
export function postIotRelation(data) {
  return request({
    url: '/iotmodel/relation',
    method: 'post', 
    data: data
  })
}

/**
* 导出列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} leftClassDispname 左边类中文名称 
* @param {integer} leftClassId 左边类id 
* @param {string} leftClassName 左边类英文 
* @param {string} leftPackageName 左边类包名 
* @param {object} params undefined 
* @param {integer} regionId 关系域id 
* @param {integer} relationId id 
* @param {string} remark 备注 
* @param {string} rightClassDispname 右边类中文名称 
* @param {integer} rightClassId 右边类id 
* @param {string} rightClassName 右边类英文 
* @param {string} rightPackageName 右边类包名 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postIotRelationExport(data) {
  return request({
    url: '/iotmodel/relation/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} leftClassDispname 左边类中文名称 
* @param {integer} leftClassId 左边类id 
* @param {string} leftClassName 左边类英文 
* @param {string} leftPackageName 左边类包名 
* @param {object} params undefined 
* @param {integer} regionId 关系域id 
* @param {integer} relationId id 
* @param {string} remark 备注 
* @param {string} rightClassDispname 右边类中文名称 
* @param {integer} rightClassId 右边类id 
* @param {string} rightClassName 右边类英文 
* @param {string} rightPackageName 右边类包名 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getIotRelationList(query) {
  return request({
    url: '/iotmodel/relation/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取详细信息
* @param {integer} relationId relationId 
* @returns 
*/
export function getIotRelationRelationId(relationId) {
  return request({
    url: '/iotmodel/relation/' + relationId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} relationId relationId 
* @returns 
*/
export function deleteIotRelationRelationId(relationId) {
  return request({
    url: '/iotmodel/relation/' + relationId,
    method: 'delete', 
  })
}

