/**
 * 类约束关系-域接口
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} classesRelationRegion classesRelationRegion 
* @returns 
*/
export function postIotRelationRegion(data) {
  return request({
    url: '/iotmodel/relation/region',
    method: 'post', 
    data: data
  })
}

/**
* 修改
* @param {undefined} classesRelationRegion classesRelationRegion 
* @returns 
*/
export function putIotRelationRegion(data) {
  return request({
    url: '/iotmodel/relation/region',
    method: 'put', 
    data: data
  })
}

/**
* 查询列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {object} params undefined 
* @param {integer} regionId ${comment} 
* @param {string} regionName 域名称 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getIotRelationRegionList(query) {
  return request({
    url: '/iotmodel/relation/region/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取详细信息
* @param {integer} regionId regionId 
* @returns 
*/
export function getIotRelationRegionRegionId(regionId) {
  return request({
    url: '/iotmodel/relation/region/' + regionId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} regionId regionId 
* @returns 
*/
export function deleteIotRelationRegionRegionId(regionId) {
  return request({
    url: '/iotmodel/relation/region/' + regionId,
    method: 'delete', 
  })
}

