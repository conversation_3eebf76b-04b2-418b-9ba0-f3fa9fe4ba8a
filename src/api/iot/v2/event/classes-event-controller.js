/**
 * 事件管理API
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} classesEvent classesEvent 
* @returns 
*/
export function postIotEvent(data) {
  return request({
    url: '/iotmodel/event',
    method: 'post', 
    data: data
  })
}

/**
* 修改
* @param {undefined} classesEvent classesEvent 
* @returns 
*/
export function putIotEvent(data) {
  return request({
    url: '/iotmodel/event',
    method: 'put', 
    data: data
  })
}

/**
* 导出列表
* @param {string} className 类英文 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} eventDispname 服务文名称 
* @param {integer} eventId ${comment} 
* @param {string} eventName 服务英文 
* @param {integer} eventType 事件类型 1信息；2告警；3故障 
* @param {string} moduleName 模块分组 
* @param {object} params undefined 
* @param {string} properties[0].createBy 创建者 
* @param {string} properties[0].createTime 创建时间 
* @param {string} properties[0].dataSpecs 数据规格 
* @param {string} properties[0].delFlag 字段排序 
* @param {integer} properties[0].deptId 部门id 
* @param {string} properties[0].descriptions 备注 
* @param {integer} properties[0].eventId 事件id 
* @param {string} properties[0].inoutType 输入输出类型：OUT 
* @param {integer} properties[0].orderSort 字段排序 
* @param {object} properties[0].params undefined 
* @param {string} properties[0].propertyDispname 属性中文名称 
* @param {integer} properties[0].propertyId id 
* @param {string} properties[0].propertyName 属性英文 
* @param {string} properties[0].propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} properties[0].remark 备注 
* @param {string} properties[0].searchValue 搜索值 
* @param {string} properties[0].updateBy 更新者 
* @param {string} properties[0].updateTime 更新时间 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postIotEventExport(data) {
  return request({
    url: '/iotmodel/event/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询列表
* @param {string} className 类英文 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} eventDispname 服务文名称 
* @param {integer} eventId ${comment} 
* @param {string} eventName 服务英文 
* @param {integer} eventType 事件类型 1信息；2告警；3故障 
* @param {string} moduleName 模块分组 
* @param {object} params undefined 
* @param {string} properties[0].createBy 创建者 
* @param {string} properties[0].createTime 创建时间 
* @param {string} properties[0].dataSpecs 数据规格 
* @param {string} properties[0].delFlag 字段排序 
* @param {integer} properties[0].deptId 部门id 
* @param {string} properties[0].descriptions 备注 
* @param {integer} properties[0].eventId 事件id 
* @param {string} properties[0].inoutType 输入输出类型：OUT 
* @param {integer} properties[0].orderSort 字段排序 
* @param {object} properties[0].params undefined 
* @param {string} properties[0].propertyDispname 属性中文名称 
* @param {integer} properties[0].propertyId id 
* @param {string} properties[0].propertyName 属性英文 
* @param {string} properties[0].propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} properties[0].remark 备注 
* @param {string} properties[0].searchValue 搜索值 
* @param {string} properties[0].updateBy 更新者 
* @param {string} properties[0].updateTime 更新时间 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getIotEventList(query) {
  return request({
    url: '/iotmodel/event/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取详细信息
* @param {integer} eventId eventId 
* @returns 
*/
export function getIotEventEventId(eventId) {
  return request({
    url: '/iotmodel/event/' + eventId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} eventId eventId 
* @returns 
*/
export function deleteIotEventEventId(eventId) {
  return request({
    url: '/iotmodel/event/' + eventId,
    method: 'delete', 
  })
}

