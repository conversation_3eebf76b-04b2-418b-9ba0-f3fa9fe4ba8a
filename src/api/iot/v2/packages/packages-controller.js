/**
 * 包管理API
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} packages packages 
* @returns 
*/
export function postIotPackages(data) {
  return request({
    url: '/iotmodel/packages',
    method: 'post', 
    data: data
  })
}

/**
* 修改
* @param {undefined} packages packages 
* @returns 
*/
export function putIotPackages(data) {
  return request({
    url: '/iotmodel/packages',
    method: 'put', 
    data: data
  })
}

/**
* 导出列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} packageDispname 包中文名称 
* @param {integer} packageId id 
* @param {string} packageName 类英文 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @param {string} viewStyle 视图样式 
* @returns 
*/
export function postIotPackagesExport(data) {
  return request({
    url: '/iotmodel/packages/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询列表
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} packageDispname 包中文名称 
* @param {integer} packageId id 
* @param {string} packageName 类英文 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @param {string} viewStyle 视图样式 
* @returns 
*/
export function getIotPackagesList(query) {
  return request({
    url: '/iotmodel/packages/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取详细信息
* @param {integer} packageId packageId 
* @returns 
*/
export function getIotPackagesPackageId(packageId) {
  return request({
    url: '/iotmodel/packages/' + packageId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} packageId packageId 
* @returns 
*/
export function deleteIotPackagesPackageId(packageId) {
  return request({
    url: '/iotmodel/packages/' + packageId,
    method: 'delete', 
  })
}

