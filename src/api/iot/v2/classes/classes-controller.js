/**
 * 类管理API
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} classes classes 
* @returns 
*/
export function postIotClasses(data) {
  return request({
    url: '/iotmodel/classes',
    method: 'post', 
    data: data
  })
}

/**
* 修改
* @param {undefined} classes classes 
* @returns 
*/
export function putIotClasses(data) {
  return request({
    url: '/iotmodel/classes',
    method: 'put', 
    data: data
  })
}

/**
* 导出列表
* @param {string} accessType 访问类型 
* @param {string} classDispname 类中文名称 
* @param {string} classesExtends 继承父类code 
* @param {string} classesType 类类型 
* @param {integer} classId 类Id 
* @param {string} className 类英文 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} defineJson 类定义json字段 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {integer} formId undefined 
* @param {string} objTableName 实例存储表名称 
* @param {string} packageName 包编码 
* @param {object} params undefined 
* @param {string} properties[0].accessType 访问类型：default、public、protected、private 
* @param {string} properties[0].childClasses.accessType 访问类型 
* @param {string} properties[0].childClasses.classDispname 类中文名称 
* @param {string} properties[0].childClasses.classesExtends 继承父类code 
* @param {string} properties[0].childClasses.classesType 类类型 
* @param {integer} properties[0].childClasses.classId 类Id 
* @param {string} properties[0].childClasses.className 类英文 
* @param {string} properties[0].childClasses.createBy 创建者 
* @param {string} properties[0].childClasses.createTime 创建时间 
* @param {string} properties[0].childClasses.defineJson 类定义json字段 
* @param {string} properties[0].childClasses.delFlag 删除标志 
* @param {integer} properties[0].childClasses.deptId 部门id 
* @param {string} properties[0].childClasses.descriptions 备注 
* @param {integer} properties[0].childClasses.formId undefined 
* @param {string} properties[0].childClasses.objTableName 实例存储表名称 
* @param {string} properties[0].childClasses.packageName 包编码 
* @param {object} properties[0].childClasses.params undefined 
* @param {string} properties[0].childClasses.remark 备注 
* @param {string} properties[0].childClasses.searchValue 搜索值 
* @param {string} properties[0].childClasses.solidworks 视图选择器 
* @param {string} properties[0].childClasses.updateBy 更新者 
* @param {string} properties[0].childClasses.updateTime 更新时间 
* @param {string} properties[0].childClasses.viewStyle 视图样式 
* @param {string} properties[0].className 类英文名 
* @param {string} properties[0].createBy 创建者 
* @param {string} properties[0].createTime 创建时间 
* @param {string} properties[0].dataSpecs 数据规格 
* @param {string} properties[0].delFlag 删除标志 
* @param {integer} properties[0].deptId 部门id 
* @param {string} properties[0].descriptions 备注 
* @param {string} properties[0].moduleName 模块分组 
* @param {integer} properties[0].orderSort 字段排序 
* @param {object} properties[0].params undefined 
* @param {string} properties[0].propertyDataSpecs.className undefined 
* @param {string} properties[0].propertyDataSpecs.itemType undefined 
* @param {string} properties[0].propertyDataSpecs.length undefined 
* @param {string} properties[0].propertyDataSpecs.max undefined 
* @param {string} properties[0].propertyDataSpecs.min undefined 
* @param {string} properties[0].propertyDataSpecs.packageName undefined 
* @param {string} properties[0].propertyDataSpecs.size undefined 
* @param {string} properties[0].propertyDataSpecs.step undefined 
* @param {string} properties[0].propertyDataSpecs.unit undefined 
* @param {string} properties[0].propertyDataSpecs.unitName undefined 
* @param {string} properties[0].propertyDispname 属性中文名称 
* @param {integer} properties[0].propertyId 属性id 
* @param {string} properties[0].propertyName 属性英文 
* @param {string} properties[0].propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} properties[0].remark 备注 
* @param {string} properties[0].renderSpecs H5 渲染规格 
* @param {string} properties[0].rwFlag 读写标识：READ_WRITE：读写；READ_ONLY：只读 
* @param {string} properties[0].searchValue 搜索值 
* @param {string} properties[0].updateBy 更新者 
* @param {string} properties[0].updateTime 更新时间 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} solidworks 视图选择器 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @param {string} viewStyle 视图样式 
* @returns 
*/
export function postIotClassesExport(data) {
  return request({
    url: '/iotmodel/classes/export',
    method: 'post', 
    data: data
  })
}

/**
* generateClass
* @param {undefined} wfFormDto wfFormDto 
* @returns 
*/
export function postIotClassesGenerate(data) {
  return request({
    url: '/iotmodel/classes/generate',
    method: 'post', 
    data: data
  })
}

/**
* 查询列表
* @param {string} accessType 访问类型 
* @param {string} classDispname 类中文名称 
* @param {string} classesExtends 继承父类code 
* @param {string} classesType 类类型 
* @param {integer} classId 类Id 
* @param {string} className 类英文 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} defineJson 类定义json字段 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {integer} formId undefined 
* @param {string} objTableName 实例存储表名称 
* @param {string} packageName 包编码 
* @param {object} params undefined 
* @param {string} properties[0].accessType 访问类型：default、public、protected、private 
* @param {string} properties[0].childClasses.accessType 访问类型 
* @param {string} properties[0].childClasses.classDispname 类中文名称 
* @param {string} properties[0].childClasses.classesExtends 继承父类code 
* @param {string} properties[0].childClasses.classesType 类类型 
* @param {integer} properties[0].childClasses.classId 类Id 
* @param {string} properties[0].childClasses.className 类英文 
* @param {string} properties[0].childClasses.createBy 创建者 
* @param {string} properties[0].childClasses.createTime 创建时间 
* @param {string} properties[0].childClasses.defineJson 类定义json字段 
* @param {string} properties[0].childClasses.delFlag 删除标志 
* @param {integer} properties[0].childClasses.deptId 部门id 
* @param {string} properties[0].childClasses.descriptions 备注 
* @param {integer} properties[0].childClasses.formId undefined 
* @param {string} properties[0].childClasses.objTableName 实例存储表名称 
* @param {string} properties[0].childClasses.packageName 包编码 
* @param {object} properties[0].childClasses.params undefined 
* @param {string} properties[0].childClasses.remark 备注 
* @param {string} properties[0].childClasses.searchValue 搜索值 
* @param {string} properties[0].childClasses.solidworks 视图选择器 
* @param {string} properties[0].childClasses.updateBy 更新者 
* @param {string} properties[0].childClasses.updateTime 更新时间 
* @param {string} properties[0].childClasses.viewStyle 视图样式 
* @param {string} properties[0].className 类英文名 
* @param {string} properties[0].createBy 创建者 
* @param {string} properties[0].createTime 创建时间 
* @param {string} properties[0].dataSpecs 数据规格 
* @param {string} properties[0].delFlag 删除标志 
* @param {integer} properties[0].deptId 部门id 
* @param {string} properties[0].descriptions 备注 
* @param {string} properties[0].moduleName 模块分组 
* @param {integer} properties[0].orderSort 字段排序 
* @param {object} properties[0].params undefined 
* @param {string} properties[0].propertyDataSpecs.className undefined 
* @param {string} properties[0].propertyDataSpecs.itemType undefined 
* @param {string} properties[0].propertyDataSpecs.length undefined 
* @param {string} properties[0].propertyDataSpecs.max undefined 
* @param {string} properties[0].propertyDataSpecs.min undefined 
* @param {string} properties[0].propertyDataSpecs.packageName undefined 
* @param {string} properties[0].propertyDataSpecs.size undefined 
* @param {string} properties[0].propertyDataSpecs.step undefined 
* @param {string} properties[0].propertyDataSpecs.unit undefined 
* @param {string} properties[0].propertyDataSpecs.unitName undefined 
* @param {string} properties[0].propertyDispname 属性中文名称 
* @param {integer} properties[0].propertyId 属性id 
* @param {string} properties[0].propertyName 属性英文 
* @param {string} properties[0].propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} properties[0].remark 备注 
* @param {string} properties[0].renderSpecs H5 渲染规格 
* @param {string} properties[0].rwFlag 读写标识：READ_WRITE：读写；READ_ONLY：只读 
* @param {string} properties[0].searchValue 搜索值 
* @param {string} properties[0].updateBy 更新者 
* @param {string} properties[0].updateTime 更新时间 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} solidworks 视图选择器 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @param {string} viewStyle 视图样式 
* @returns 
*/
export function getIotClassesList(query) {
  return request({
    url: '/iotmodel/classes/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取详细信息
* @param {integer} classId classId 
* @returns 
*/
export function getIotClassesClassId(classId) {
  return request({
    url: '/iotmodel/classes/' + classId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} classId classId 
* @returns 
*/
export function deleteIotClassesClassId(classId) {
  return request({
    url: '/iotmodel/classes/' + classId,
    method: 'delete', 
  })
}

