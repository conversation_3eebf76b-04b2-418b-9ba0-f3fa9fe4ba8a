/**
 * remote-objects-controller
 */
import request from '@/utils/request'

/**
* list
* @param {undefined} objectPageDto objectPageDto 
* @returns 
*/
export function postIotRemoteObjectsList(data) {
  return request({
    url: '/iotmodel/remote/objects/list',
    method: 'post', 
    data: data
  })
}

/**
* save
* @param {undefined} objectDataDto objectDataDto 
* @returns 
*/
export function postIotRemoteObjectsSave(data) {
  return request({
    url: '/iotmodel/remote/objects/save',
    method: 'post', 
    data: data
  })
}

