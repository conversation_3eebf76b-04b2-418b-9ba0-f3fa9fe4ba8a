/**
 * 表单设计器API
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} formDesigner formDesigner 
* @returns 
*/
export function postIotUiFormDesigner(data) {
  return request({
    url: '/iotmodel/ui/form/designer',
    method: 'post', 
    data: data
  })
}

/**
* 修改
* @param {undefined} formDesigner formDesigner 
* @returns 
*/
export function putIotUiFormDesigner(data) {
  return request({
    url: '/iotmodel/ui/form/designer',
    method: 'put', 
    data: data
  })
}

/**
* 导出列表
* @param {integer} classId 物联模型classId 
* @param {string} classJson 表单物联定义json 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 物联模型classId 
* @param {integer} deptId 部门id 
* @param {string} designerJson 表单定义json 
* @param {string} formDispName 表单名称 
* @param {integer} formId 表单ID 
* @param {string} formName 表单英文名 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} status 表单状态 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @param {string} version 表单版本 
* @returns 
*/
export function postIotUiFormDesignerExport(data) {
  return request({
    url: '/iotmodel/ui/form/designer/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询列表
* @param {integer} classId 物联模型classId 
* @param {string} classJson 表单物联定义json 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 物联模型classId 
* @param {integer} deptId 部门id 
* @param {string} designerJson 表单定义json 
* @param {string} formDispName 表单名称 
* @param {integer} formId 表单ID 
* @param {string} formName 表单英文名 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} status 表单状态 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @param {string} version 表单版本 
* @returns 
*/
export function getIotUiFormDesignerList(query) {
  return request({
    url: '/iotmodel/ui/form/designer/list',
    method: 'get', 
    params: query
  })
}

/**
* 发布
* @param {integer} formId formId 
* @returns 
*/
export function getIotUiFormDesignerReleaseFormId(formId) {
  return request({
    url: '/iotmodel/ui/form/designer/release/' + formId,
    method: 'get', 
  })
}

/**
* 停止
* @param {integer} formId formId 
* @returns 
*/
export function getIotUiFormDesignerStopFormId(formId) {
  return request({
    url: '/iotmodel/ui/form/designer/stop/' + formId,
    method: 'get', 
  })
}

/**
* 获取详细信息
* @param {integer} formId formId 
* @returns 
*/
export function getIotUiFormDesignerFormId(formId) {
  return request({
    url: '/iotmodel/ui/form/designer/' + formId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} formId formId 
* @returns 
*/
export function deleteIotUiFormDesignerFormId(formId) {
  return request({
    url: '/iotmodel/ui/form/designer/' + formId,
    method: 'delete', 
  })
}

