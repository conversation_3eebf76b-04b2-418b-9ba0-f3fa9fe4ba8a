/**
 * 服务管理API
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} classesService classesService 
* @returns 
*/
export function postIotService(data) {
  return request({
    url: '/iotmodel/service',
    method: 'post', 
    data: data
  })
}

/**
* 修改
* @param {undefined} classesService classesService 
* @returns 
*/
export function putIotService(data) {
  return request({
    url: '/iotmodel/service',
    method: 'put', 
    data: data
  })
}

/**
* 导出列表
* @param {integer} callMethod 调用方式 1同步；2异步 
* @param {string} className 类英文 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 模块分组 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} moduleName 模块分组 
* @param {object} params undefined 
* @param {string} properties[0].createBy 创建者 
* @param {string} properties[0].createTime 创建时间 
* @param {string} properties[0].dataSpecs 数据规格 
* @param {string} properties[0].delFlag 字段排序 
* @param {integer} properties[0].deptId 部门id 
* @param {string} properties[0].descriptions 备注 
* @param {string} properties[0].inoutType 输入输出类型：IN、OUT 
* @param {integer} properties[0].orderSort 字段排序 
* @param {object} properties[0].params undefined 
* @param {string} properties[0].propertyDispname 属性中文名称 
* @param {integer} properties[0].propertyId ${comment} 
* @param {string} properties[0].propertyName 属性英文 
* @param {string} properties[0].propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} properties[0].remark 备注 
* @param {string} properties[0].searchValue 搜索值 
* @param {integer} properties[0].serviceId 服务id 
* @param {string} properties[0].updateBy 更新者 
* @param {string} properties[0].updateTime 更新时间 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} serviceDispname 服务文名称 
* @param {integer} serviceId ${comment} 
* @param {string} serviceName 服务英文 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postIotServiceExport(data) {
  return request({
    url: '/iotmodel/service/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询列表
* @param {integer} callMethod 调用方式 1同步；2异步 
* @param {string} className 类英文 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 模块分组 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} moduleName 模块分组 
* @param {object} params undefined 
* @param {string} properties[0].createBy 创建者 
* @param {string} properties[0].createTime 创建时间 
* @param {string} properties[0].dataSpecs 数据规格 
* @param {string} properties[0].delFlag 字段排序 
* @param {integer} properties[0].deptId 部门id 
* @param {string} properties[0].descriptions 备注 
* @param {string} properties[0].inoutType 输入输出类型：IN、OUT 
* @param {integer} properties[0].orderSort 字段排序 
* @param {object} properties[0].params undefined 
* @param {string} properties[0].propertyDispname 属性中文名称 
* @param {integer} properties[0].propertyId ${comment} 
* @param {string} properties[0].propertyName 属性英文 
* @param {string} properties[0].propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} properties[0].remark 备注 
* @param {string} properties[0].searchValue 搜索值 
* @param {integer} properties[0].serviceId 服务id 
* @param {string} properties[0].updateBy 更新者 
* @param {string} properties[0].updateTime 更新时间 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} serviceDispname 服务文名称 
* @param {integer} serviceId ${comment} 
* @param {string} serviceName 服务英文 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getIotServiceList(query) {
  return request({
    url: '/iotmodel/service/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取详细信息
* @param {integer} serviceId serviceId 
* @returns 
*/
export function getIotServiceServiceId(serviceId) {
  return request({
    url: '/iotmodel/service/' + serviceId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} serviceId serviceId 
* @returns 
*/
export function deleteIotServiceServiceId(serviceId) {
  return request({
    url: '/iotmodel/service/' + serviceId,
    method: 'delete', 
  })
}

