/**
 * 模块管理API
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} classesModule classesModule 
* @returns 
*/
export function postIotModule(data) {
  return request({
    url: '/iotmodel/module',
    method: 'post', 
    data: data
  })
}

/**
* 修改
* @param {undefined} classesModule classesModule 
* @returns 
*/
export function putIotModule(data) {
  return request({
    url: '/iotmodel/module',
    method: 'put', 
    data: data
  })
}

/**
* 导出列表
* @param {string} className 类英文名称 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 备注 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} moduleDispname 模块中文名称 
* @param {integer} moduleId idå 
* @param {string} moduleName 模块英文 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postIotModuleExport(data) {
  return request({
    url: '/iotmodel/module/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询列表
* @param {string} className 类英文名称 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} delFlag 备注 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} moduleDispname 模块中文名称 
* @param {integer} moduleId idå 
* @param {string} moduleName 模块英文 
* @param {object} params undefined 
* @param {string} remark 备注 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getIotModuleList(query) {
  return request({
    url: '/iotmodel/module/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取详细信息
* @param {integer} moduleId moduleId 
* @returns 
*/
export function getIotModuleModuleId(moduleId) {
  return request({
    url: '/iotmodel/module/' + moduleId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} moduleId moduleId 
* @returns 
*/
export function deleteIotModuleModuleId(moduleId) {
  return request({
    url: '/iotmodel/module/' + moduleId,
    method: 'delete', 
  })
}

