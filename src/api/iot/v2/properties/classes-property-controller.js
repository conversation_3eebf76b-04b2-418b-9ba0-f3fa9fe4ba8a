/**
 * 属性管理API
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} classesProperty classesProperty 
* @returns 
*/
export function postIotProperties(data) {
  return request({
    url: '/iotmodel/properties',
    method: 'post', 
    data: data
  })
}

/**
* 修改
* @param {undefined} classesProperty classesProperty 
* @returns 
*/
export function putIotProperties(data) {
  return request({
    url: '/iotmodel/properties',
    method: 'put', 
    data: data
  })
}

/**
* 导出列表
* @param {string} accessType 访问类型：default、public、protected、private 
* @param {string} childClasses.accessType 访问类型 
* @param {string} childClasses.classDispname 类中文名称 
* @param {string} childClasses.classesExtends 继承父类code 
* @param {string} childClasses.classesType 类类型 
* @param {integer} childClasses.classId 类Id 
* @param {string} childClasses.className 类英文 
* @param {string} childClasses.createBy 创建者 
* @param {string} childClasses.createTime 创建时间 
* @param {string} childClasses.defineJson 类定义json字段 
* @param {string} childClasses.delFlag 删除标志 
* @param {integer} childClasses.deptId 部门id 
* @param {string} childClasses.descriptions 备注 
* @param {integer} childClasses.formId undefined 
* @param {string} childClasses.objTableName 实例存储表名称 
* @param {string} childClasses.packageName 包编码 
* @param {object} childClasses.params undefined 
* @param {string} childClasses.properties[0].accessType 访问类型：default、public、protected、private 
* @param {string} childClasses.properties[0].className 类英文名 
* @param {string} childClasses.properties[0].createBy 创建者 
* @param {string} childClasses.properties[0].createTime 创建时间 
* @param {string} childClasses.properties[0].dataSpecs 数据规格 
* @param {string} childClasses.properties[0].delFlag 删除标志 
* @param {integer} childClasses.properties[0].deptId 部门id 
* @param {string} childClasses.properties[0].descriptions 备注 
* @param {string} childClasses.properties[0].moduleName 模块分组 
* @param {integer} childClasses.properties[0].orderSort 字段排序 
* @param {object} childClasses.properties[0].params undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.className undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.itemType undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.length undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.max undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.min undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.packageName undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.size undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.step undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.unit undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.unitName undefined 
* @param {string} childClasses.properties[0].propertyDispname 属性中文名称 
* @param {integer} childClasses.properties[0].propertyId 属性id 
* @param {string} childClasses.properties[0].propertyName 属性英文 
* @param {string} childClasses.properties[0].propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} childClasses.properties[0].remark 备注 
* @param {string} childClasses.properties[0].renderSpecs H5 渲染规格 
* @param {string} childClasses.properties[0].rwFlag 读写标识：READ_WRITE：读写；READ_ONLY：只读 
* @param {string} childClasses.properties[0].searchValue 搜索值 
* @param {string} childClasses.properties[0].updateBy 更新者 
* @param {string} childClasses.properties[0].updateTime 更新时间 
* @param {string} childClasses.remark 备注 
* @param {string} childClasses.searchValue 搜索值 
* @param {string} childClasses.solidworks 视图选择器 
* @param {string} childClasses.updateBy 更新者 
* @param {string} childClasses.updateTime 更新时间 
* @param {string} childClasses.viewStyle 视图样式 
* @param {string} className 类英文名 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} dataSpecs 数据规格 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} moduleName 模块分组 
* @param {integer} orderSort 字段排序 
* @param {object} params undefined 
* @param {string} propertyDataSpecs.className undefined 
* @param {string} propertyDataSpecs.itemType undefined 
* @param {string} propertyDataSpecs.length undefined 
* @param {string} propertyDataSpecs.max undefined 
* @param {string} propertyDataSpecs.min undefined 
* @param {string} propertyDataSpecs.packageName undefined 
* @param {string} propertyDataSpecs.size undefined 
* @param {string} propertyDataSpecs.step undefined 
* @param {string} propertyDataSpecs.unit undefined 
* @param {string} propertyDataSpecs.unitName undefined 
* @param {string} propertyDispname 属性中文名称 
* @param {integer} propertyId 属性id 
* @param {string} propertyName 属性英文 
* @param {string} propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} remark 备注 
* @param {string} renderSpecs H5 渲染规格 
* @param {string} rwFlag 读写标识：READ_WRITE：读写；READ_ONLY：只读 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function postIotPropertiesExport(data) {
  return request({
    url: '/iotmodel/properties/export',
    method: 'post', 
    data: data
  })
}

/**
* 查询列表
* @param {string} accessType 访问类型：default、public、protected、private 
* @param {string} childClasses.accessType 访问类型 
* @param {string} childClasses.classDispname 类中文名称 
* @param {string} childClasses.classesExtends 继承父类code 
* @param {string} childClasses.classesType 类类型 
* @param {integer} childClasses.classId 类Id 
* @param {string} childClasses.className 类英文 
* @param {string} childClasses.createBy 创建者 
* @param {string} childClasses.createTime 创建时间 
* @param {string} childClasses.defineJson 类定义json字段 
* @param {string} childClasses.delFlag 删除标志 
* @param {integer} childClasses.deptId 部门id 
* @param {string} childClasses.descriptions 备注 
* @param {integer} childClasses.formId undefined 
* @param {string} childClasses.objTableName 实例存储表名称 
* @param {string} childClasses.packageName 包编码 
* @param {object} childClasses.params undefined 
* @param {string} childClasses.properties[0].accessType 访问类型：default、public、protected、private 
* @param {string} childClasses.properties[0].className 类英文名 
* @param {string} childClasses.properties[0].createBy 创建者 
* @param {string} childClasses.properties[0].createTime 创建时间 
* @param {string} childClasses.properties[0].dataSpecs 数据规格 
* @param {string} childClasses.properties[0].delFlag 删除标志 
* @param {integer} childClasses.properties[0].deptId 部门id 
* @param {string} childClasses.properties[0].descriptions 备注 
* @param {string} childClasses.properties[0].moduleName 模块分组 
* @param {integer} childClasses.properties[0].orderSort 字段排序 
* @param {object} childClasses.properties[0].params undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.className undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.itemType undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.length undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.max undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.min undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.packageName undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.size undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.step undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.unit undefined 
* @param {string} childClasses.properties[0].propertyDataSpecs.unitName undefined 
* @param {string} childClasses.properties[0].propertyDispname 属性中文名称 
* @param {integer} childClasses.properties[0].propertyId 属性id 
* @param {string} childClasses.properties[0].propertyName 属性英文 
* @param {string} childClasses.properties[0].propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} childClasses.properties[0].remark 备注 
* @param {string} childClasses.properties[0].renderSpecs H5 渲染规格 
* @param {string} childClasses.properties[0].rwFlag 读写标识：READ_WRITE：读写；READ_ONLY：只读 
* @param {string} childClasses.properties[0].searchValue 搜索值 
* @param {string} childClasses.properties[0].updateBy 更新者 
* @param {string} childClasses.properties[0].updateTime 更新时间 
* @param {string} childClasses.remark 备注 
* @param {string} childClasses.searchValue 搜索值 
* @param {string} childClasses.solidworks 视图选择器 
* @param {string} childClasses.updateBy 更新者 
* @param {string} childClasses.updateTime 更新时间 
* @param {string} childClasses.viewStyle 视图样式 
* @param {string} className 类英文名 
* @param {string} createBy 创建者 
* @param {string} createTime 创建时间 
* @param {string} dataSpecs 数据规格 
* @param {string} delFlag 删除标志 
* @param {integer} deptId 部门id 
* @param {string} descriptions 备注 
* @param {string} moduleName 模块分组 
* @param {integer} orderSort 字段排序 
* @param {object} params undefined 
* @param {string} propertyDataSpecs.className undefined 
* @param {string} propertyDataSpecs.itemType undefined 
* @param {string} propertyDataSpecs.length undefined 
* @param {string} propertyDataSpecs.max undefined 
* @param {string} propertyDataSpecs.min undefined 
* @param {string} propertyDataSpecs.packageName undefined 
* @param {string} propertyDataSpecs.size undefined 
* @param {string} propertyDataSpecs.step undefined 
* @param {string} propertyDataSpecs.unit undefined 
* @param {string} propertyDataSpecs.unitName undefined 
* @param {string} propertyDispname 属性中文名称 
* @param {integer} propertyId 属性id 
* @param {string} propertyName 属性英文 
* @param {string} propertyType 属性类型：ARRAY、STRUCT、INT、FLOAT、DOUBLE、TEXT、DATE、ENUM、BOOL 
* @param {string} remark 备注 
* @param {string} renderSpecs H5 渲染规格 
* @param {string} rwFlag 读写标识：READ_WRITE：读写；READ_ONLY：只读 
* @param {string} searchValue 搜索值 
* @param {string} updateBy 更新者 
* @param {string} updateTime 更新时间 
* @returns 
*/
export function getIotPropertiesList(query) {
  return request({
    url: '/iotmodel/properties/list',
    method: 'get', 
    params: query
  })
}

/**
* 获取详细信息
* @param {integer} propertyId propertyId 
* @returns 
*/
export function getIotPropertiesPropertyId(propertyId) {
  return request({
    url: '/iotmodel/properties/' + propertyId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} propertyId propertyId 
* @returns 
*/
export function deleteIotPropertiesPropertyId(propertyId) {
  return request({
    url: '/iotmodel/properties/' + propertyId,
    method: 'delete', 
  })
}

