/**
 * 实例关系API
 */
import request from '@/utils/request'

/**
* 新增
* @param {undefined} objectsRelationData objectsRelationData 
* @returns 
*/
export function postIotObjectsRelation(data) {
  return request({
    url: '/iotmodel/objects/relation',
    method: 'post', 
    data: data
  })
}

/**
* 修改
* @param {undefined} objectsRelationData objectsRelationData 
* @returns 
*/
export function putIotObjectsRelation(data) {
  return request({
    url: '/iotmodel/objects/relation',
    method: 'put', 
    data: data
  })
}

/**
* 获取类定义
* @param {string} className className 
* @returns 
*/
export function getIotObjectsRelationDefineClassName(className) {
  return request({
    url: '/iotmodel/objects/relation/define/' + className,
    method: 'get', 
  })
}

/**
* 根据父关系Id，查询数据
* @param {integer} parentRelationId parentRelationId 
* @param {integer} regionId regionId 
* @returns 
*/
export function getIotObjectsRelationListRegionId(regionId) {
  return request({
    url: '/iotmodel/objects/relation/list/' + regionId,
    method: 'get', 
  })
}

/**
* 获取详细信息
* @param {integer} relationId relationId 
* @returns 
*/
export function getIotObjectsRelationRelationId(relationId) {
  return request({
    url: '/iotmodel/objects/relation/' + relationId,
    method: 'get', 
  })
}

/**
* 删除
* @param {integer} relationId relationId 
* @returns 
*/
export function deleteIotObjectsRelationRelationId(relationId) {
  return request({
    url: '/iotmodel/objects/relation/' + relationId,
    method: 'delete', 
  })
}

