import request from '@/utils/request';

// 修改
export function putDevApiIotModule(data) {
  return request({
    url: '/iot/module',
    method: 'put',
    data: data,
  });
}

// 新增
export function postDevApiIotModule(data) {
  return request({
    url: '/iot/module',
    method: 'post',
    data: data,
  });
}

// 导出列表
export function postDevApiIotModuleExport(data) {
  return request({
    url: '/iot/module/export',
    method: 'post',
    data: data,
  });
}

// 查询列表
export function getDevApiIotModuleList(query) {
  return request({
    url: '/iot/module/list',
    method: 'get',
    params: query,
  });
}

// 获取详细信息
export function getDevApiIotModuleModuleId(moduleId) {
  return request({
    url: '/iot/module/' + moduleId,
    method: 'get',
  });
}

// 删除
export function deleteDevApiIotModuleModuleId(moduleId) {
  return request({
    url: '/iot/module/' + moduleId,
    method: 'delete',
  });
}
