import request from '@/utils/request';

// 修改
export function putDevApiIotTopology(data) {
  return request({
    url: '/iot/topology',
    method: 'put',
    data: data,
  });
}

// 新增
export function postDevApiIotTopology(data) {
  return request({
    url: '/iot/topology',
    method: 'post',
    data: data,
  });
}

// 导出列表
export function postDevApiIotTopologyExport(data) {
  return request({
    url: '/iot/topology/export',
    method: 'post',
    data: data,
  });
}

// 查询列表
export function getDevApiIotTopologyList(query) {
  return request({
    url: '/iot/topology/list',
    method: 'get',
    params: query,
  });
}

// 获取详细信息
export function getDevApiIotTopologyTopologyId(topologyId) {
  return request({
    url: '/iot/topology/' + topologyId,
    method: 'get',
  });
}

// 删除
export function deleteDevApiIotTopologyTopologyId(topologyId) {
  return request({
    url: '/iot/topology/' + topologyId,
    method: 'delete',
  });
}
