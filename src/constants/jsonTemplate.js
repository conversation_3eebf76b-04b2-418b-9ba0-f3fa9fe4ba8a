import { PropertyType } from '@/constants/atomComponents.js';
const RequiredTextArrs = [PropertyType.SELECT, PropertyType.RADIO];

export function formItemJsonTemplate({ ...arg }) {
  const {
    label,
    prop,
    echoConfig,
    isEnter = false,
    dictType,
    required,
    type = 'Input',
    requiredRule = {},
    rules = [],
    options = {},
    option,
    association,
    associationProp,
    itemOptions,
    showConfig,
    dialogJson,
  } = arg;
  let requiredText = '请输入';
  if (RequiredTextArrs.includes(type)) {
    requiredText = '请选择';
  }
  return {
    label,
    prop,
    /**
     * 回显配置， 用于非 prop 回显时场景
     * echoConfig: {
     *  // 回显取值
     *  field: 'properties',
     *  // 取值后根据此字段进行区分
     *  basis: {
     *    field: 'inoutType',
     *    value: 'OUT'
     *  }
     * }
     */
    echoConfig,
    // 关联字段，如果需要将名称也保存的话或不方便使用 prop 场景，比如嵌套 form-item
    associationProp,
    // 类型
    type,
    // 回车触发搜索
    isEnter,
    // 字典类型
    dictType,
    // 是否必填
    required,
    // 规则
    rules: [
      {
        required: true,
        message: requiredText + label,
        trigger: 'blur',
        ...requiredRule,
      },
      ...rules,
    ],
    // 表单元素如 input、select 配置项
    options: {
      clearable: true,
      placeholder: requiredText + label,
      ...options,
    },
    // 当为 select、radio 且未设置字典时，设置选项
    option,
    // 关联配置，配置显示隐藏字段
    association,
    // 关联配置内的form-item配置
    itemOptions,
    /**
     * 展示配置, 根据 field 的 value 值判断展示隐藏
     * {
     *  field: '',
     *  value: ''
     * }
     *
     */
    showConfig,
    dialogJson,
  };
}

export const DefaultFormOption = {
  inline: false,
  labelWidth: '130px',
  labelPosition: 'top',
  size: 'small',
};
