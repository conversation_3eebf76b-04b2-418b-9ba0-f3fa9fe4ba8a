export default {
  props: {},
  data() {
    return {
      tableData: [],
      // 总条数
      total: 0,
      // 查询参数
      defaultQueryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dialogVisible: false,
      loading: true,
      initFormData: {},
      queryParams: {},
      methodsConfig: {},
      // 新增时是否不重置表单
      addInitNoReset: false,
    };
  },

  created() {},

  mounted() {},

  methods: {
    // 获取查询关联字段，重置或搜索时不清空
    getAssociationFields() {
      if (!this.associationFields || this.associationFields.length === 0) return {};
      const fieldsObj = {};
      this.associationFields.forEach(item => {
        fieldsObj[item] = this[item];
      });
      return fieldsObj;
    },
    // 分页
    handlerPagination(vals) {
      const { page, limit } = vals;
      this.defaultQueryParams.pageNum = page;
      this.defaultQueryParams.pageSize = limit;
      this.getList(this.queryParams);
    },

    // 查询表集合
    async getList(queryParams = {}, api) {
      this.loading = true;
      if (api) {
        this.methodsConfig.listApi = api;
      }
      this.queryParams = {
        ...this.queryParams,
        ...queryParams,
      };

      const queryData = {
        ...(!this.hiddenPagination ? this.defaultQueryParams : {}),
        ...this.getAssociationFields(),
        ...this.queryParams,
        ...queryParams,
      };

      console.log('queryData', queryData);
      try {
        const response = await this.methodsConfig.listApi(queryData);
        const { rows = [], total = 0 } = response || {};
        if (this?.handleResponse) {
          this.tableData = this?.handleResponse(rows);
        } else {
          this.tableData = rows;
        }
        if (!this.hiddenPagination) {
          this.total = total || 0;
        }
        this.loading = false;
      } catch (error) {
        console.log('列表处理错误', error)
        this.loading = false;
      }
    },

    // 搜索事件
    // 搜索事件
    handlerSearch({ ...args }) {
      const { searchParams, searchType = 'Query' } = args || {};
      this.defaultQueryParams = {
        ...this.defaultQueryParams,
        pageNum: 1,
        pageSize: 10,
      };
      if (searchType === 'Reset') {
        this.queryParams = this.defaultQueryParams;
      }
      if (searchType === 'Export') {
        this.$confirm('是否确认导出所有数据项?', "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.download(
            this.exportApi,
            {},
            `${this.exportFileName}_${new Date().getTime()}.xlsx`,
            {
              data: {
                ...this.queryParams,
                ...searchParams,
              },
            }
          );
        }).catch(() => { });
        return
      }
      this.getList({
        ...this.queryParams,
        ...searchParams,
      });
    },

    async handlerSave({ ...args }) {
      const { formData, queryParams, apis = {}, editParams = [], onSuccess } = args || {};
      const params = {
        ...formData,
        ...queryParams,
      };
      let msgText = '新增';
      let func = apis.addApi;
      if (this.getDialogType() === 'Edit') {
        editParams.forEach(item => {
          params[item] = this.initFormData[item];
        });

        msgText = '编辑';
        func = apis.editApi;
      }
      try {
        const { code, data } = await func(params);
        if (code === 200) {
          this.dialogVisible = false;
          this.getList();
          this.$modal.msgSuccess(`${msgText}成功`);
          onSuccess && onSuccess(data);
        }
        this.$refs.formDialog.resetLoading();
      } catch (error) {
        // 接口调用失败重置 loading
        this.$refs.formDialog.resetLoading();
      }
    },

    setDialogType(type) {
      if (!this.activeTab) {
        this.dialogJson.type = type;
      } else {
        this.dialogJson[this.activeTab].type = type;
      }
    },

    getDialogType() {
      if (!this.activeTab) return this.dialogJson.type;
      return this.dialogJson[this.activeTab].type;
    },

    handlerDialogJson(type, data) {
      if (!this.addInitNoReset) {
        if (type === 'Add' ) {
          this.initFormData = {};
        } else {
          this.initFormData = data;
        }
      }

      this.setDialogType(type);
      this.dialogVisible = true;
    },

    // 新增按钮事件
    async pageButton({ ...args }) {
      const { item, onAdd, row = {}} = args;
      const { type } = item || {};
      if (type === 'Add') {
        if (onAdd) {
          onAdd();
          return;
        }
        this.handlerDialogJson(type);
      }

      // TODO: 批量删除，后续有此功能时再做处理
      if (type === 'Delete') {
        const tableIds = row.tableId || this.ids;
        this.$modal
          .confirm('是否确认删除表编号为"' + tableIds + '"的数据项？')
          .then(function() {
            // eslint-disable-next-line no-undef
            return delTable(tableIds);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess('删除成功');
          })
          .catch(() => {});
      }
    },

    async getInfoData({ ...args }) {
      const { id, api } = args;
      const { code, data } = await api(id);
      if (code === 200) {
        this.handlerDialogJson('Edit', data);
      }
    },

    deleteTableItem({ ...args }) {
      const { id, api, onSuccess } = args;
      this.$modal
        .confirm('是否确认删除该数据项？')
        .then(function() {
          return api(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
          onSuccess && onSuccess();
        })
        .catch(() => {});
    },

    // 发布
    handlerLive({ ...args }) {
      const { id, api, onSuccess } = args;
      this.$modal
        .confirm('是否确认发布该数据项？')
        .then(function() {
          return api(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('发布成功');
          onSuccess && onSuccess();
        })
        .catch(() => {});
    },

    tableButton({ ...args }) {
      const { btnItem, idKey, onAdd, onEdit, onLive, onStop, onInfo } = args;
      const { row, item } = btnItem;
      const { type } = item;
      if (type === 'Add') {
        if (onAdd) {
          onAdd(row);
          return;
        }
        this.handlerDialogJson(type);
      }

      if (type === 'Edit') {
        if (onEdit) {
          onEdit(row);
          return;
        }
        this.getInfo(row[idKey]);
      }

      if (type === 'Delete') {
        this.deleteItem(row[idKey]);
      }

      if (type === 'Live' && onLive) {
        onLive(row[idKey]);
        return;
      }

      if (type === 'Stop' && onStop) {
        onStop(row);
        return;
      }

      if (type === 'Info' && onInfo) {
        onInfo(row);
        return;
      }
    },

    handlerDialogClose() {
      // this.initFormData = {};
      this.setDialogType('Add');
    },

    goToPage(routerConfig = {}) {
      this.$router.push(routerConfig);
    },
  },
};
