import html2pdf from 'html2pdf.js';

export default {
  props: {},
  data() {
    return {};
  },

  created() {},

  mounted() {},

  methods: {
    async exportPDF(element) {
      try {
        this.loading = true;
        const opt = {
          margin: [18, 12], // 增加边距
          filename: `${this.pdfName}.pdf`,
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: {
            scale: 2,
            useCORS: true,
            logging: false,
            letterRendering: true, // 添加字母渲染选项
            windowWidth: element.scrollWidth * 2, // 确保捕获完整宽度
            windowHeight: element.scrollHeight * 2, // 确保捕获完整高度
          },
          pagebreak: { mode: ['avoid-all','css','legacy'] },
          jsPDF: {
            unit: 'mm',
            format: 'a4',
            orientation: 'portrait',
            compress: true, // 启用压缩
            precision: 4, // 提高精度
          },
        };

        // await html2pdf().from(element).set(opt).save();
        // this.$message.success('PDF导出成功');

        const pdf = await html2pdf().from(element).set(opt).outputPdf('blob')
        // 创建 Blob URL
        const pdfUrl = URL.createObjectURL(pdf)
        window.open(pdfUrl, '_blank')

      } catch (error) {
        console.error('PDF导出失败:', error);
        this.$message.error('PDF导出失败');
      } finally {
        this.loading = false;
      }
    },
  },
};
