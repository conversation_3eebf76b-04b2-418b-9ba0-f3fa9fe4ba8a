export default {
  props: {
    value: {
      type: [String, Number, Array, Object],
      default: '',
    },
    atomConfig: {
      type: Object,
      default: () => {},
    },
    initFormData: {
      type: Object,
      default: () => {},
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  computed: {},

  created() {},

  mounted() {},

  methods: {
    handleInput(value) {
      this.$emit('input', value);
    },
  },
};
