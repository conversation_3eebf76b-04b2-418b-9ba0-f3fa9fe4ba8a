<template>
  <el-dialog :title="dialogTitle" :visible="visible" v-bind="config.options" @close="handleClose">
    <CoustomTable
      :default-select-id="initData.defaultSelectId"
      :page-buttons-json="config.tableConfig.pageButtonsJson"
      :paginations="queryParams"
      :query-form-json="config.tableConfig.queryFormJson"
      :table-data="tableData"
      :table-json="config.tableConfig.tableJson"
      :total="total"
      @onButton="handlerButton"
      @onPageButton="handlerPageButton"
      @onRowClick="handlerRowClick"
      @onSearch="handlerSearch"
    />

    <div slot="footer" class="dialog-footer" flex="main:right">
      <el-button
        v-for="(item, index) in config.buttonList"
        :key="index"
        :loading="loadingBtnType === item.type"
        v-bind="item.options"
        @click="handleBtn(item)"
      >{{ item.label }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'CoustomTableDialog',
  // 嵌套组件使用异步加载
  components: {
    CoustomTable(resolve) {
      require(['@/components/CoustomTable'], resolve);
    },
  },
  inject: {
    dictData: { value: [], default: [] },
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    config: {
      type: Object,
      default: () => { },
    },
    initData: {
      type: Object,
      default: () => { },
    },
    onSubmit: {
      type: Function,
      default: function() { },
    },
  },

  data() {
    return {
      loadingBtnType: '',
      titleOption: {
        Add: '新增',
        Edit: '编辑',
        Info: '详情',
      },
      loading: false,
      tableData: [],
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      selectRow: null,
    };
  },
  computed: {
    dialogTitle() {
      return this.config.type ? `${this.titleOption[this.config.type]}${this.config.title}` : `${this.config.title}`;
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.handlerSearch({});
      }
    },
  },

  mounted() { },
  methods: {
    async handlerPageButton(item) { },
    handlerButton() { },

    handlerSearch(params) {
      this.getList(params?.searchParams);
    },

    handlerRowClick(item) {
      if (this.config.buttonList && Object.keys(this.config.buttonList.length !== 0)) {
        this.selectRow = item;
        return;
      }

      this.$emit('onSubmit', item);
      this.handleClose();
    },

    /** 查询表集合 */
    getList(queryParams = {}) {
      this.loading = true;
      const queryData = {
        ...queryParams,
        ...this.queryParams,
        packageName: this.packageName,
      };
      this.config.api(queryData).then(response => {
        this.tableData = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    resetLoading() {
      this.loadingBtnType = '';
    },

    handleClose() {
      if (this.selectRow) {
        this.selectRow = null;
      }
      this.$emit('update:visible', false);
    },
    async handleBtn(item) {
      const { type } = item;
      if (type === 'Cancel') {
        this.$emit('update:visible', false);
      }

      if (type === 'Ok') {
        this.$emit('onSubmit', this.selectRow);
        this.handleClose();
      }
    },
  },
};
</script>
<style scoped lang="scss"></style>
