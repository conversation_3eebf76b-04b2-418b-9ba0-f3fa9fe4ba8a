# CoustomForm 自定义表单组件

基于 Element UI 的 Form 组件封装的配置化表单组件。通过 JSON 配置快速生成表单，支持表单联动、搜索布局、表单验证等功能。

## 功能特性

- 支持通过 JSON 配置生成表单
- 支持表单字段联动显示
- 支持默认值设置
- 支持搜索表单布局
- 支持表单验证
- 支持表单重置
- 支持自定义组件

## 基础用法

```vue
<coustom-form :form-json="formJson" :init-data="initData" />
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| formJson | 表单配置对象 | Object | {} |
| initData | 表单初始值 | Object | {} |

### formJson 配置项

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| formOption | Element Form 的配置项 | Object | - |
| formItemJson | 表单项配置数组 | Array | - |
| buttonJson | 按钮配置对象 | Object | - |
| defaultFormData | 表单默认值 | Object | - |

### formItemJson 配置项

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| type | 组件类型 | String | - |
| label | 标签文本 | String | - |
| prop | 表单域字段 | String | - |
| options | 选项配置(用于Select等) | Array | - |
| association | 联动配置 | Object | - |
| showConfig | 显示条件配置 | Object | - |

## Events

| 事件名称 | 说明 | 回调参数 |
|---------|------|----------|
| onSearch | 搜索按钮点击时触发 | { searchParams: Object, searchType: String } |
| onFieldChange | 表单项值变化时触发 | { prop: String, value: any } |

## Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| validateForm | 表单验证方法 | - |
| resetForm | 重置表单 | - |

## 注意事项

1. formJson 的结构必须符合规范，包含必要的配置项
2. 表单联动配置需要通过 association 和 showConfig 正确设置
3. 搜索布局需要设置 buttonJson.type 为 'Search'
4. 组件内部会监听 eventBus 的 'resetForm' 事件来重置表单
5. 表单验证需要在 formOption 中配置相应的 rules

## 支持的组件类型

- Input: 输入框
- Select: 下拉选择框
- DatePicker: 日期选择器
- TimePicker: 时间选择器
- Radio: 单选框
- Checkbox: 复选框
- Switch: 开关
- 自定义组件 (通过 AtomComponents 支持)

## 高级用法

### 自定义验证规则
