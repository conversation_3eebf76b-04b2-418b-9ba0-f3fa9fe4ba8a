<template>
  <el-form
    ref="coustomForm"
    :class="{ formFlex: buttonJsonType === 'Search' }"
    :model="formData"
    v-bind="formJson.formOption"
  >
    <template v-for="(item, index) in formJson.formItemJson">
      <div :key="`${item.type}-${index}`">
        <AtomComponents
          v-model="formData[item.prop]"
          :atom-config="item"
          :init-form-data="initData"
          @onFieldChange="handleFieldChange"
        />
      </div>

      <template v-if="checkAssociation(item)">
        <template v-for="(childItem, childItemIndex) in getAssociationData(item)">
          <div
            v-if="
              !childItem.showConfig ||
                (childItem.showConfig &&
                  formData[childItem.showConfig.field] === childItem.showConfig.value)
            "
            :key="`${childItem.type}-${childItemIndex}-child`"
          >
            <AtomComponents
              v-model="formData[childItem.prop]"
              :atom-config="childItem"
              :init-form-data="initData"
              @onFieldChange="handleFieldChange"
            />
          </div>
        </template>
      </template>
    </template>

    <!-- <el-form-item label="创建时间">
      <el-date-picker
        end-placeholder="结束日期"
        range-separator="-"
        start-placeholder="开始日期"
        style="width: 240px"
        type="daterange"
        v-model="dateRange"
        value-format="yyyy-MM-dd"
      ></el-date-picker>
    </el-form-item>-->

    <template v-if="formJson.buttonJson && formJson.buttonJson.type === 'Search'">
      <el-form-item>
        <el-button
          v-for="(item, index) in formJson.buttonJson.list"
          :key="index"
          :init-form-data="initData"
          v-bind="item.options"
          @click="handleQuery(item)"
        >{{ item.label }}</el-button>
      </el-form-item>
    </template>
  </el-form>
</template>
<script>
import { associationIsShow, associationData } from '@/utils/coustom.js';
export default {
  name: 'CoustomForm',
  components: {
    AtomComponents(resolve) {
      require(['@/components/AtomComponents'], resolve);
    },
  },
  inject: {
    dictData: { value: [], default: [] },
  },
  provide() {
    return {
      initFormData: this.initData,
    };
  },
  props: {
    formJson: {
      type: Object,
      default: () => { },
    },
    initData: {
      type: Object,
      default: () => { },
    },
  },

  data() {
    return {
      formConfig: [],
      formData: {},
      // 关联表单
      associationConfig: {},
    };
  },

  computed: {
    buttonJsonType() {
      return this.formJson?.buttonJson?.type || '';
    },
  },

  watch: {
    formJson: {
      handler: function(val, olVal) {
        // 当 formJson 有更改时，需要重新赋值
        if (val && Object.keys(val).length !== 0) {
          this.init(2);
        }
      },
      deep: true,
      immediate: false,
    },
  },

  created() {
    this.init(1);
  },
  mounted() {
    this.handleEventBus();
  },
  beforeDestroy() {
    this.offEventBus();
  },
  methods: {
    init(value) {
      console.log('object :>> value', value);
      this.resetForm();
      this.initFormJSon();
      if (this.initData && Object.keys(this.initData).length !== 0) {
        this.formData = {
          ...this.formData,
          ...this.initData,
        };
      }
    },

    handleEventBus() {
      this.offEventBus();
      this.$eventBus.$on('resetForm', () => {
        console.log('object :>>重置 ');
        this.resetForm();
      });
    },

    offEventBus() {
      this.$eventBus.$off?.('resetForm');
    },

    handleFieldChange(values) {
      const { prop, value } = values;
      this.$set(this.formData, prop, value);
    },

    checkAssociation(item) {
      return associationIsShow(item, this.formData);
    },

    getAssociationData(item) {
      const associationConfig = associationData(item, this.formData);
      this.associationConfig = associationConfig;
      return associationConfig.childItems || [];
    },

    initFormJSon() {
      // 表单默认值
      if (this.formJson.defaultFormData) {
        this.formData = {
          ...this.formData,
          ...this.formJson.defaultFormData,
        };

        console.log('object :>>this.formData ', this.formData);
      }
    },

    getChildFields(childFields = [], fields) {
      childFields.forEach(item => {
        fields[item.prop] = this.formData[item.prop];
        if (item.associationProp) {
          fields[item.associationProp] = this.formData[item.associationProp];
        }
        if (item.itemOptions) {
          this.getChildFields(item.itemOptions, fields);
        }
      });
    },

    async validateForm() {
      console.log('object :>> this.formData', this.formData);
      try {
        const result = await this.$refs.coustomForm.validate();
        if (result) {
          if (this.associationConfig && Object.keys(this.associationConfig.length !== 0)) {
            const { childItems, jsonField } = this.associationConfig;
            const fields = {};
            if (childItems && childItems.length !== 0) {
              this.getChildFields(childItems, fields);
            }
            if (jsonField) {
              this.formData[jsonField] = JSON.stringify(fields);
            }
          }

          return this.formData;
        }
        return false;
      } catch (error) {
        console.log('object :>> error', error);
        return false;
      }
    },

    resetForm() {
      this.formData = {};
      this.$refs?.coustomForm?.resetFields?.();
    },

    handleQuery(item) {
      const { type } = item;
      if (type === 'Query') {
      }

      if (type === 'Reset') {
        this.formData = {};
      }

      this.$emit('onSearch', { searchParams: this.formData, searchType: type });
    },

    handleEnter(item) {
      if (!item.isEnter) return;
      this.$emit('onSearch', this.formData);
    },
  },
};
</script>
<style scoped lang="scss">
.formFlex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
</style>
