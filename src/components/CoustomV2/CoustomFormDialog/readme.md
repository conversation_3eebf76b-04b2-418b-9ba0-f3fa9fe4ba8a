``` javascript
 dialogJson = {
  options: {
    title: "预览",
    width: "600px",
    appendToBody: true,
  },
  buttonList: [
    {
      label: "取消",
      type: 'Cancel',
      permi: '',
      options: {
        type: "",
      }
    },
    {
      label: "确定",
      type: 'Ok',
      permi: '',
      options: {
        type: "primary",
      }
    },
  ],
  formJson: {
    // 表单设置
    formOption: {
      inline: false,
      labelWidth: "68px",
      size: 'small',
    },
    // 表单项
    formItemJson: [
      {
        label: "包名称",
        prop: "packageDispname",
        type: "Input",
        options: {
          clearable: true,
          placeholder: '请输入表名称',
        }
      }
    ],
  }

}
```