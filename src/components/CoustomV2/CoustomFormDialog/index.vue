<template>
  <el-dialog
    :visible="visible"
    v-bind="dialogJson.options"
    :title="title"
    @close="handleClose"
  >
    <div :key="key">
      <slot name="head">
        <template v-if="dialogJson.headOptions">
          <el-radio-group
            v-model="headValue"
            style="margin-bottom: 20px"
            @change="handleHeardRadio"
          >
            <el-radio-button
              v-for="item in dialogJson.headOptions"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </template>
      </slot>

      <CoustomForm
        ref="coustomFormDialog"
        :form-json="formJson"
        :init-data="initFormData"
        @formDataChange="handleFormDataChange"
      >
        <slot></slot>
      </CoustomForm>

      <!-- <slot></slot> -->

      <div slot="footer" class="dialog-footer">
        <el-button
          v-for="(item, index) in dialogJson.buttonList"
          :key="index"
          :loading="loadingBtnType === item.type"
          v-bind="item.options"
          @click="handleBtn(item)"
          >{{ item.label }}</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { associationData } from "@/utils/coustom.js";
import CoustomForm from "../CoustomForm";
export default {
  name: "CoustomFormDialog",
  components: {
    CoustomForm,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dialogJson: {
      type: Object,
      default: () => {},
    },
    initData: {
      type: Object,
      default: () => {},
    },
    onSubmit: {
      type: Function,
      default: function () {},
    },
    addInitNoReset: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      headValue: this.dialogJson.defaultHeadOptions,
      loadingBtnType: "",
      titleOption: {
        Add: "新增",
        Edit: "编辑",
        Info: "详情",
      },
      key: new Date().getTime(),
    };
  },
  computed: {
    title() {
      const { title, type } = this.dialogJson;
      if (this.titleOption[type]) {
        return `${this.titleOption[type]}${title}`;
      }
      return title;
    },
    formJson() {
      console.log(
        "object :>> this.dialogJson.formJson",
        this.dialogJson.formJson
      );
      return !this.headValue
        ? this.dialogJson.formJson
        : this.dialogJson.formJson[this.headValue] || {};
    },
    initFormData() {
      console.log(
        "object :>> this.initData12",
        this.dialogJson.type,
        this.initData
      );
      if (this.dialogJson.type === "Add" && !this.addInitNoReset) {
        return {};
      }
      const { formItemJson = [] } = this.formJson || {};
      let initData = {};
      formItemJson.forEach((el) => {
        el.children.forEach((item) => {
          // 基本字段回显
          if (this.isNotEmpty(this.initData[item.prop])) {
            initData[item.prop] = this.initData[item.prop];
          }
          if (this.isNotEmpty(this.initData[item.associationProp])) {
            initData[item.associationProp] = this.initData[item.associationProp];
          }

          // 根据回显配置项回显
          if (item.echoConfig && this.isNotEmpty(this.initData[item.echoConfig?.field])) {
            initData[item.echoConfig.field] =
              this.initData[item.echoConfig.field];
          }

          // 关联字段回显
          if (item.association) {
            const { childItems = [], jsonField } =
              associationData(item, this.initData) || {};

            if (childItems && childItems.length !== 0) {
              childItems.forEach((childItem) => {
                initData[childItem.prop] = this.initData[childItem.prop];
              });
            }

            if (this.initData[jsonField]) {
              let jsonFields = {};
              try {
                jsonFields = JSON.parse(this.initData[jsonField]);
              } catch (error) {}
              initData = Object.assign(initData, jsonFields);
            }
          }
        });
      });
      return initData;
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.key = new Date().getTime();
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    // 判断数据不是 undefined 和 null
    isNotEmpty(value) {
      return value !== undefined && value !== null;
    },
    handleFormDataChange(formData) {
      this.$emit('formDataChange', formData)
    },

    handleHeardRadio() {},

    resetLoading() {
      this.loadingBtnType = "";
    },

    handleClose() {
      this.$emit("onDialogClose");
      this.$emit("update:visible", false);
      // this.$refs.coustomFormDialog.resetForm();
    },
    async handleBtn(item) {
      const { type } = item;
      if (type === "Cancel") {
        this.$emit("update:visible", false);
      } else {
        const result = await this.$refs.coustomFormDialog.validateForm();
        if (result) {
          this.loadingBtnType = type;
          this.$emit("onSubmit", result, this.headValue, { btnType: type });
        }
      }
    },
  },
};
</script>
<style scoped lang="scss">
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 12px;
}

// ::v-deep .el-dialog{
//   display: flex;
//   flex-direction: column;
//   margin:0 !important;
//   position:absolute;
//   top:50%;
//   left:50%;
//   transform:translate(-50%,-50%);
//   max-height:calc(100% - 30px);
//   max-width:calc(100% - 30px);
// }
// ::v-deep  .el-dialog .el-dialog__body{
//   flex:1;
//   overflow: auto;
// }
</style>
