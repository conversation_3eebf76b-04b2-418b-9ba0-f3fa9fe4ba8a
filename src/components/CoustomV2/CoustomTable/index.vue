<template>
  <div>
    <!-- 搜索栏 -->
    <CoustomForm
      v-show="showSearch"
      ref="searchForm"
      :form-json="queryFormJson"
      @formDataChange="handleFormDataChange"
      @onSearch="handleSearch"
    />
    <!-- 页面操作按钮 -->
    <el-row class="mb8" v-bind="pageButtonsJson.rowJson" v-if="!isDialog">
      <el-col
        v-for="(item, index) in pageButtonsJson.buttonList"
        :key="index"
        :span="item.span"
      >
        <el-button
          v-hasPermi="[item.permi]"
          v-bind="item.options"
          @click="handlePageButton(item)"
          >{{ item.label }}</el-button
        >
      </el-col>

      <template
        v-if="
          !pageButtonsJson.hiddenToolbar &&
          pageButtonsJson.buttonList &&
          Object.keys(pageButtonsJson.buttonList).length !== 0
        "
      >
        <right-toolbar
          :show-search.sync="showSearch"
          @queryTable="handleSearch({})"
        />
      </template>
    </el-row>

    <slot name="content">
      <el-table
        v-loading="loading"
        :data="tableData"
        ref="coustomTable"
        :row-key="rowKey"
        :highlight-current-row="!!tableJson.columnJson.selectIdKey && isDialog"
        :row-class-name="tableRowClassName"
        v-bind="tableOptions"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <template v-if="tableJson.columnJson.showSelect">
          <el-table-column align="center" type="selection" width="55" />
        </template>

        <template v-for="(item, index) in columnJsonData">
          <el-table-column :key="index" v-bind="item" v-if="!(isDialog && item.dialogHidden)">
            <template slot-scope="scope">
              <template v-if="item.type === 'index'">
                <span>{{
                  (paginations.pageNum - 1) * paginations.pageSize +
                  scope.$index +
                  1
                }}</span>
              </template>

              <template v-else-if="item.type === 'func'">
                <template v-for="(btnItem, btnIndex) in item.buttonList">
                  <el-button
                    v-if="
                      !btnItem.hiddenOptions ||
                      (btnItem.hiddenOptions &&
                        btnItem.hiddenOptions.values.includes(
                          scope.row[btnItem.hiddenOptions.prop]
                        ))
                    "
                    :key="btnIndex"
                    v-hasPermi="[btnItem.permi]"
                    v-bind="btnItem.options"
                    @click="handleColumnBtn(scope.row, btnItem, scope.$index)"
                    >{{ btnItem.label }}</el-button
                  >
                </template>
              </template>

              <!-- 插槽定义特殊内容 -->
              <slot
                v-else-if="item.sloatName"
                :name="item.sloatName"
                :scope="item"
              />

              <el-link
                v-else-if="item.isLink"
                :underline="false"
                type="primary"
                @click="handleLink(scope.row)"
                >{{ scope.row[item.prop] }}</el-link
              >

              <span
                :style="{
                  color: item.colorOptions
                    ? item.colorOptions[scope.row[item.prop]]
                    : '#606266',
                }"
                v-else-if="item.dictType && dictData"
                >{{ dictData.label[item.dictType][scope.row[item.prop]] }}</span
              >
              <span
                :style="{
                  color: item.colorOptions
                    ? item.colorOptions[scope.row[item.prop]]
                    : '#606266',
                }"
                v-else-if="item.optionsMap"
                >{{ item.optionsMap[scope.row[item.prop]] }}</span
              >
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </slot>

    <!-- 表单 -->
    <pagination
      v-show="total > 10"
      :limit.sync="paginations.pageSize"
      :page.sync="paginations.pageNum"
      :total="total"
      @pagination="handlePagination"
    />
  </div>
</template>
<script>
import CoustomForm from "../CoustomForm";
export default {
  name: "CoustomTable",
  components: {
    CoustomForm,
  },
  inject: {
    dictData: { value: [], default: [] },
  },
  props: {
    isDialog: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    defaultSelectId: {
      type: String,
      default: "",
    },
    queryFormJson: {
      type: Object,
      default: () => {},
    },
    pageButtonsJson: {
      type: Object,
      default: () => {},
    },
    tableJson: {
      type: Object,
      default: () => {},
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    onSearch: {
      type: Function,
      default: function () {},
    },
    onButton: {
      type: Function,
      default: function () {},
    },
    onFunction: {
      type: Function,
      default: function () {},
    },
    onTableRowClick: {
      type: Function,
      default: function () {},
    },
    // 总条数
    total: {
      type: Number,
      default: 0,
    },
    // 查询参数
    paginations: {
      type: Object,
      default: () => {
        return {
          pageNum: 1,
          pageSize: 10,
        };
      },
    },
  },
  data() {
    return {
      // 选中数组
      ids: [],
      // 选中表数组
      tableNames: [],
      // 显示搜索条件
      showSearch: true,
      // 表数据
      tableList: [],
      // 日期范围
      dateRange: "",
      // 选中 tableId
      selectedId: this.defaultSelectId,
    };
  },
  computed: {
    tableOptions() {
      return this.tableJson?.options || {};
    },
    columnJsonData() {
      const columnJson = this.tableJson?.columnJson?.data || [];
      if (this.isDialog) {
        return columnJson.filter((item) => item.type !== "func");
      }
      return columnJson;
    }
  },

  beforeDestroy() {
    this.selectedId = "";
  },
  created() {},
  mounted() {},
  methods: {
    handleFormDataChange(formData) {
     this.$emit('onFormDataChange', formData)
    },
    rowKey(row) {
      if (this.tableJson.columnJson.selectIdKey) {
        return row[this.tableJson.columnJson.selectIdKey];
      }
      return row.id
    },
    handlePagination(vals) {
      this.$emit("onPagination", vals);
    },
    handleLink(item) {
      this.$emit("onTableRowClick", item);
    },

    handleSearch(searchParams) {
      this.$emit("onSearch", searchParams);
    },

    // 页面按钮操作
    handlePageButton(item) {
      this.$emit("onPageButton", item);
    },

    // 表格每行按钮操作
    handleColumnBtn(row, item, rowIndex) {
      this.$emit("onButton", { row, item, rowIndex });
    },

    // 多选框选中数据
    handleSelectionChange(selection) {},

    // 单行选中数据
    handleRowClick(row) {
      console.log("选中row11", row, this.tableJson.columnJson);
      if (!this.tableJson.columnJson.selectIdKey) return;
      this.selectedId = row[this.tableJson.columnJson.selectIdKey];
      console.log("选中row", row, this.selectedId);
      this.$emit("onRowClick", row);
    },

    // 为被选中的行添加类名， 改变选中行背景色
    tableRowClassName({ row, rowIndex }) {
      if (row[this.tableJson.columnJson.selectIdKey] === this.selectedId) {
        return "selected-row";
      } else {
        return "";
      }
    },
  },
};
</script>
<style lang="scss">
.selected-row {
  background-color: #ecf5ff !important;
  .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background-color: #ecf5ff !important;
  }
}

.el-table th.el-table__cell>.cell{
  white-space: pre-line !important;
}

.el-table__body-wrapper.is-scrolling-none {
  height: auto !important;
}
// .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
//   background-color: #a0cfff !important;
// }
</style>
