<template>
  <el-form
    ref="coustomForm"
    :class="{ formFlex: buttonJsonType === 'Search' }"
    :model="formData"
    v-bind="formOption"
  >
    <template v-for="el in formItemJson">
      <template
        v-if="
          !el.showConfig ||
          (el.showConfig && el.showConfig.values.includes(formData[el.showConfig.field]))
        "
      >
        <div class="title-divider" v-if="el.showTitle">{{ el.label }}</div>
        <template v-if="!el.isSlot">
          <el-row
            :gutter="el.formFlex ? el.formFlex.gutter : formFlex.gutter"
            type="flex"
            style="flex-wrap: wrap"
          >
            <el-col
              :span="item.formFlex ? item.formFlex.span : formFlex.span"
              v-for="(item, index) in el.children"
            >
              <div
                :key="`${item.type}-${index}`"
                v-if="
                  !item.showConfig ||
                  (item.showConfig &&
                    item.showConfig.values.includes(formData[item.showConfig.field]))
                "
              >
                <AtomComponents
                  v-model="formData[item.prop]"
                  :form-data="formData"
                  :atom-config="item"
                  :is-search="buttonJsonType === 'Search'"
                  :disabled="formOption.disabled"
                  :init-form-data="initData"
                  @onFieldChange="handleFieldChange"
                />
              </div>

              <template v-if="checkAssociation(item)">
                <template v-for="(childItem, childItemIndex) in getAssociationData(item)">
                  <div
                    v-if="
                      !childItem.showConfig ||
                      (childItem.showConfig &&
                        formData[childItem.showConfig.field] === childItem.showConfig.value)
                    "
                    :key="`${childItem.type}-${childItemIndex}-child`"
                  >
                    <AtomComponents
                      v-model="formData[childItem.prop]"
                      :form-data="formData"
                      :is-search="buttonJsonType === 'Search'"
                      :disabled="formOption.disabled"
                      :atom-config="childItem"
                      :init-form-data="initData"
                      @onFieldChange="handleFieldChange"
                    />
                  </div>
                </template>
              </template>
            </el-col>
          </el-row>
        </template>
        <slot v-else></slot>
      </template>
    </template>

    <template v-if="buttonJson && buttonJson.type === 'Search'">
      <el-form-item
        class="form-search-button"
        style="width: 100%; display: flex; justify-content: flex-end"
      >
        <el-button
          v-for="(item, index) in buttonJson.list"
          :key="index"
          :init-form-data="initData"
          v-bind="item.options"
          @click="handleQuery(item)"
          >{{ item.label }}</el-button
        >
      </el-form-item>
    </template>
  </el-form>
</template>
<script>
import { associationIsShow, associationData } from '@/utils/coustom.js';
export default {
  name: 'CoustomForm',
  components: {
    AtomComponents(resolve) {
      require(['@/components/AtomComponents'], resolve);
    },
  },
  inject: {
    dictData: { value: [], default: [] },
  },
  provide() {
    return {
      initFormData: this.initData,
    };
  },
  props: {
    formJson: {
      type: Object,
      default: () => {},
    },
    initData: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      formConfig: [],
      formData: {},
      // 关联表单
      associationConfig: {},
    };
  },

  computed: {
    buttonJsonType() {
      return this.formJson?.buttonJson?.type || '';
    },
    formOption() {
      return this.formJson?.formOption || {};
    },
    formItemJson() {
      return this.formJson?.formItemJson || [];
    },
    buttonJson() {
      return this.formJson?.buttonJson || {};
    },
    formFlex() {
      const { formFlex } = this.formJson?.formOption || {};
      if (formFlex) {
        return formFlex;
      }
      return {
        gutter: 0,
        span: 24,
      };
    },
  },

  watch: {
    formJson: {
      handler: function (val, olVal) {
        // 当 formJson 有更改时，需要重新赋值
        if (val && Object.keys(val).length !== 0) {
          this.init(2);
        }
      },
      deep: true,
      immediate: false,
    },
    formData: {
      handler: function (val) {
        this.$emit('formDataChange', val);
      },
      deep: true,
    },
  },

  created() {
    this.init(1);
  },
  mounted() {
    this.handleEventBus();
  },
  beforeDestroy() {
    this.offEventBus();
  },
  methods: {
    init(value) {
      console.log('object :>> value', value);
      // 初始化时才重置表单，TODO:需要关注其它场景
      if (value === 2) return;
      this.resetForm();
      this.initFormJSon();
      if (this.initData && Object.keys(this.initData).length !== 0) {
        this.formData = {
          ...this.formData,
          ...this.initData,
        };
      }
    },

    handleEventBus() {
      this.offEventBus();
      this.$eventBus.$on('resetForm', () => {
        console.log('object :>>重置 ');
        this.resetForm();
      });
    },

    offEventBus() {
      this.$eventBus?.$off?.('resetForm');
    },

    handleFieldChange(values) {
      const { prop, value } = values;
      this.$set(this.formData, prop, value);
      this.$refs.coustomForm.validateField(prop);
    },

    checkAssociation(item) {
      return associationIsShow(item, this.formData);
    },

    updateFormData(prop, value) {
      this.$set(this.formData, prop, value);
    },

    getAssociationData(item) {
      const associationConfig = associationData(item, this.formData);
      this.associationConfig = associationConfig;
      return associationConfig.childItems || [];
    },

    initFormJSon() {
      // 表单默认值
      if (this.formJson?.defaultFormData) {
        this.formData = {
          ...this.formData,
          ...(this.formJson?.defaultFormData || {}),
        };
      }
    },

    getChildFields(childFields = [], fields) {
      childFields.forEach(item => {
        fields[item.prop] = this.formData[item.prop];
        if (item.associationProp) {
          fields[item.associationProp] = this.formData[item.associationProp];
        }
        if (item.itemOptions) {
          this.getChildFields(item.itemOptions, fields);
        }
      });
    },

    async validateForm() {
      console.log('object :>> this.formData', this.formData);
      try {
        const result = await this.$refs.coustomForm.validate();
        if (result) {
          if (this.associationConfig && Object.keys(this.associationConfig.length !== 0)) {
            const { childItems, jsonField } = this.associationConfig;
            const fields = {};
            if (childItems && childItems.length !== 0) {
              this.getChildFields(childItems, fields);
            }
            if (jsonField) {
              this.formData[jsonField] = JSON.stringify(fields);
            }
          }

          return this.formData;
        }
        return false;
      } catch (error) {
        console.log('object :>> error', error);
        return false;
      }
    },

    resetForm() {
      this.formData = {};
      this.$refs?.coustomForm?.resetFields?.();
    },

    handleQuery(item) {
      const { type } = item;
      if (type === 'Query') {
      }

      if (type === 'Reset') {
        this.formData = {};
        this.$eventBus.$emit('resetSelectDept');
        this.$eventBus.$emit('resetSelectUser');
        this.$eventBus.$emit('resetSelectProject');
      }
      this.$emit('onSearch', { searchParams: this.formData, searchType: type });
    },

    handleEnter(item) {
      if (!item.isEnter) return;
      this.$emit('onSearch', this.formData);
    },
  },
};
</script>
<style scoped lang="scss">
.formFlex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-around;

  .el-form-item {
    width: 100%;
    display: flex;
    align-items: center;

    ::v-deep .el-form-item__label {
      flex-shrink: 0 !important;
    }

    ::v-deep .el-form-item__content {
      width: 80%;
    }
  }

  ::v-deep .el-row--flex {
    width: 100%;
  }

  .form-search-button {
    display: flex;
    justify-content: flex-end;

    ::v-deep .el-form-item__content {
      display: flex;
      justify-content: flex-end;
    }
  }
}
.title-divider {
  height: 18px;
  width: 100%;
  border-left: 4px solid #11a983;
  font-size: 16px;
  color: #333;
  font-weight: 600;
  display: flex;
  align-items: center;
  padding-left: 12px;
  margin-bottom: 24px;
}
</style>
