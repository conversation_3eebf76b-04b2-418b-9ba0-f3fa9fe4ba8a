<template>
  <el-breadcrumb
    class="app-breadcrumb"
    separator="/"
  >
    <transition-group name="breadcrumb">
      <el-breadcrumb-item
        v-for="(item, index) in levelList"
        :key="item.path"
      >
        <span
          v-if="item.redirect === 'noRedirect' || index == levelList.length - 1"
          class="no-redirect"
        >{{ item.meta.title }}</span>
        <a
          v-else
          @click.prevent="handleLink(item)"
        >{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
export default {
  data() {
    return {
      levelList: null,
      // 关联路由参数
      concernRoutersQuery: {}
    };
  },
  computed: {
    routes() {
      return this.$store.state.permission.routes;
    },
  },
  watch: {
    $route(currentRoute, previousRoute) {
      if (currentRoute.path.startsWith('/redirect/')) {
        return;
      }

      this.getBreadcrumb(currentRoute, previousRoute);
    }
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let querys = {};
      try {
        const sessionQuery = this.$cache.session.getJSON('CONCERN_ROUTERS_QUERY');
        if (sessionQuery) {
          querys = JSON.parse(querys);
        }
      } catch (error) { }
      this.concernRoutersQuery = querys;
      this.getBreadcrumb();
    },

    getBreadcrumb(route, previousRoute) {
      // only show routes with meta.title
      let matched = this.$route.matched.filter(item => item.meta && item.meta.title);
      const first = matched[0];

      if (!this.isDashboard(first)) {
        matched = [{ path: '/index', meta: { title: '首页' }}].concat(matched);
      }

      const levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false);

      const currentRoute = route || this.$route;

      const names = currentRoute?.name.split('/');

      // 存在嵌套子路由时，动态插入父路由面包屑
      if (names.length > 1) {
        const currentName = names[names.length - 1];
        let previousName = '';
        if (previousRoute) {
          const previousNames = previousRoute?.name?.split?.('/');
          previousName = previousNames[previousNames.length - 1];
        }
        // 当前父菜单
        const currentRouters = this.routes.find(item => {
          const path = item.path.replace('/', '');
          if (path === first.path.replace('/', '')) {
            return item;
          }
        });
        // 当前父菜单子菜单
        const currentRoutersChildren = currentRouters?.children || [];

        names.forEach(item => {
          if (item !== currentName) {
            const currentRouters = currentRoutersChildren.find(el => {
              const elRouterNames = el?.name.split('/');
              const elRouterName = elRouterNames[elRouterNames.length - 1];
              return elRouterName === item;
            }) || {};

            const query = item === previousName ? previousRoute.query : {};
            levelList.splice(levelList.length - 1, 0, {
              ...currentRouters,
              query,
            });

            if (Object.keys(query).length !== 0) {
              // 存储路由参数，防止页面刷新时丢失
              this.$set(this.concernRoutersQuery, currentRouters.name, query);
              this.$cache.session.setJSON('CONCERN_ROUTERS_QUERY', this.concernRoutersQuery);
            }
          }
        });
      }

      this.levelList = levelList;
    },

    isDashboard(route) {
      const name = route && route.name;
      if (!name) {
        return false;
      }
      return name.trim() === 'Index';
    },

    handleLink(item) {
      const { redirect, path, name, query } = item;
      if (redirect) {
        this.$router.push(redirect);
        return;
      }
      if (path === '/index') {
        this.$router.push(path);
        return;
      }

      this.$router.push({
        name,
        query: query || this.concernRoutersQuery[name],
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
