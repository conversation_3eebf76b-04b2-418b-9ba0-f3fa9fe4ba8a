
## 基础用法

```vue
<template>
  <base-tree
    :tree-data="treeData"
    node-key="id"
    :show-checkbox="true"
    @node-click="handleNodeClick"
    />
</template>
<script>
export default {
  data() {
    return {
      treeData: [
        {
          id: 1,
          label: '一级节点',
      children: [
        {
          id: 2,
          label: '二级节点'
        }
        ]
      }
      ]
    } 
  },
  methods: {
    handleNodeClick(data, node) {
    console.log('节点被点击:', data)
    }
  }
}
</script>

```

## 自定义节点内容

```vue
<template>
  <base-tree :tree-data="treeData" node-key="id">
    <template #custom="{ node, data }">
      <span>{{ data.label }}</span>
      <el-button
        size="small"
        @click.stop="handleClick(data)">
        操作
      </el-button>
    </template>
  </base-tree>
</template>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| treeData | 树形数据源 | Array | [] |
| treeProps | 配置选项,包含 children 和 label 的配置 | Object | { children: 'children', label: 'label' } |
| nodeKey | 每个树节点用来作为唯一标识的属性 | String | 'id' |
| defaultExpandedKeys | 默认展开的节点的 key 的数组 | Array | [] |
| showCheckbox | 是否显示勾选框 | Boolean | false |
| defaultCheckedKeys | 默认勾选的节点的 key 的数组 | Array | [] |
| draggable | 是否开启拖拽 | Boolean | false |
| allowDrag | 判断节点能否被拖拽的方法 | Function | () => true |
| allowDrop | 拖拽时判定目标节点能否被放置的方法 | Function | () => true |

## Events

| 事件名称 | 说明 | 回调参数 |
|---------|------|----------|
| node-click | 节点被点击时的回调 | (data: Object, node: Object) |
| check | 节点勾选状态发生变化时的回调 | (data: Object, { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes }) |
| node-drag-start | 节点开始拖拽时触发的事件 | (node: Object, event: Event) |
| node-drag-enter | 拖拽进入其他节点时触发的事件 | (draggingNode: Object, dropNode: Object, event: Event) |
| node-drag-leave | 拖拽离开某个节点时触发的事件 | (draggingNode: Object, dropNode: Object, event: Event) |
| node-drag-over | 在拖拽节点时触发的事件 | (draggingNode: Object, dropNode: Object, event: Event) |
| node-drag-end | 拖拽结束时触发的事件 | (draggingNode: Object, dropNode: Object, dropType: String, event: Event) |
| node-drop | 拖拽成功完成时触发的事件 | (draggingNode: Object, dropNode: Object, dropType: String, event: Event) |

## Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| getTree | 获取树实例 | - |
| setChecked | 通过 key 设置某个节点的勾选状态 | (key: string\|number, checked: boolean) |
| setCheckedKeys | 设置目前勾选的节点 | (keys: array) |
| getCheckedKeys | 获取当前被勾选节点的 key 所组成的数组 | - |
| filter | 过滤所有树节点,过滤后只会显示匹配的节点 | (value: string) |

## Slots

| 插槽名称 | 说明 | 作用域参数 |
|---------|------|------------|
| custom | 自定义树节点的内容 | { node: 节点对象, data: 节点数据 } |

## 注意事项

1. 树形数据源中的每个节点必须有唯一的标识字段(默认为 id)
2. 使用拖拽功能时,建议设置 nodeKey
3. 过滤功能默认匹配节点的 label 字段