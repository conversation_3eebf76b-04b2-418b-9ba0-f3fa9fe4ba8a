export default {
  extension: {
    widgetLabel: {
      card: 'Card',
      alert: '<PERSON><PERSON>',
    },

    setting: {
      cardFolded: 'Folded',
      cardShowFold: 'Show Fold',
      cardWidth: 'Width Of Card',
      cardShadow: 'Shadow',

      alertTitle: 'Title',
      alertType: 'Type',
      description: 'Description',
      closable: 'Closable',
      closeText: 'Text On Close Btn',
      center: 'Center',
      showIcon: 'Show Icon',
      effect: 'Effect',
    },
  },
};
