export default {
  application: {
    'zh-CN': '简体中文',
    'en-US': 'English',
    productTitle: 'Online Form Designer',
    github: 'GitHub',
    document: 'Docs',
    qqGroup: 'WeChat Group',
    deployment: 'Deployment',
    subscription: 'Subscription',
  },

  designer: {
    componentLib: 'Components',
    formLib: 'Templates',
    templateTitle: '模板',
    containerTitle: 'Container',
    dragHandlerHint: 'drag container or field to layout center',
    dragAction: 'drag',
    basicFieldTitle: 'Basic Field',
    advancedFieldTitle: 'Advanced Field',
    customFieldTitle: 'Customized Field',

    noWidgetHint: 'Please select a widget from the left list, drag and drop to this container.',

    widgetLabel: {
      standardTable: '标准表格',
      baseInfoTable: '基本信息',
      inspectionItemsTable: '检验项目',
      approvalInfoTable: '审批信息',
      gridThree: '三列栅格',
      tableFourThree: '4x3表格',
      grid: 'Grid',
      table: 'Table',
      nestTable: 'nestTable',
      tab: 'Tab',
      section: 'Section',
      'sub-form': 'SubForm',
      'grid-col': 'GridCol',
      'table-cell': 'TableCell',
      'tab-pane': 'TabPane',
      'data-table': 'DataTable',

      input: 'Input',
      textarea: 'Textarea',
      number: 'InputNumber',
      radio: 'Radio',
      checkbox: 'Checkbox',
      select: 'Select',
      'tree-select': '树选项',
      time: 'Time',
      'time-range': 'Time range',
      date: 'Date',
      'date-range': 'Date range',
      switch: 'Switch',
      rate: 'Rate',
      color: 'ColorPicker',
      slider: 'Slider',
      'static-text': 'Text',
      'html-text': 'HTML',
      button: 'Button',
      divider: 'Divider',

      'picture-upload': 'Picture',
      'file-upload': 'File',
      'rich-editor': 'Rich Editor',
      cascader: 'Cascader',
      slot: 'Slot',

      custom: 'Custom Component',
      sign: 'sign',
    },

    hint: {
      selectParentWidget: 'Select parent of this widget',
      moveUpWidget: 'Move up this widget',
      moveDownWidget: 'Move down this widget',
      cloneWidget: 'Clone this widget',
      insertRow: 'Insert new row',
      insertColumn: 'Insert new column',
      copy: 'Copy this widget',
      remove: 'Remove this widget',
      cellSetting: 'Cell setting',
      dragHandler: 'Drag handler',
      copyField: 'Copy field widget',
      onlyFieldWidgetAcceptable: 'Only field widget can be dragged into sub-form',
      moveUpFirstChildHint: 'First child can not be move up',
      moveDownLastChildHint: 'Last child can not be move down',

      closePreview: 'Close',
      copyJson: 'Copy',
      saveFormJson: 'Save As File',
      copyVueCode: 'Copy Vue Code',
      copyHtmlCode: 'Copy HTML Code',
      copyJsonSuccess: 'Copy succeed',
      importJsonSuccess: 'Import succeed',
      copyJsonFail: 'Copy failed',
      copyVueCodeSuccess: 'Copy succeed',
      copyVueCodeFail: 'Copy failed',
      copyHtmlCodeSuccess: 'Copy succeed',
      copyHtmlCodeFail: 'Copy failed',
      saveVueCode: 'Save Vue File',
      saveHtmlCode: 'Save Html File',
      getFormData: 'Get Data',
      resetForm: 'Reset',
      disableForm: 'Disable',
      enableForm: 'Enable',
      exportFormData: 'Form Data',
      copyFormData: 'Copy',
      saveFormData: 'Save As File',
      copyVue2SFC: 'Copy Vue2',
      copyVue3SFC: 'Copy Vue3',
      copySFCFail: 'Copy failed',
      copySFCSuccess: 'Copy succeed',
      saveVue2SFC: 'Save As Vue2',
      saveVue3SFC: 'Save As Vue3',
      fileNameForSave: 'File name:',
      saveFileTitle: 'Save as File',
      fileNameInputPlaceholder: 'Enter the file name',
      sampleLoadedSuccess: 'Example loaded successfully',
      sampleLoadedFail: 'Sample load failed',
      loadFormTemplate: 'Load This',
      loadFormTemplateHint: 'Are you sure to load this template?',
      loadFormTemplateSuccess: 'Load form template success!',
      loadFormTemplateFailed: 'Load form template failed.',
      currentNodeCannotBeSelected: 'The current node cannot be selected.',

      widgetSetting: 'Widget Config',
      formSetting: 'Form Config',

      prompt: 'Prompt',
      confirm: 'OK',
      cancel: 'Cancel',
      import: 'Import',
      importJsonHint: 'The code to be imported should have the following JSON format.',
      invalidOptionsData: 'Invalid data of options:',
      lastPaneCannotBeDeleted: 'The last pane cannot be deleted.',
      duplicateName: 'Duplicate name: ',
      nameRequired: 'Name required.',

      numberValidator: 'Number',
      letterValidator: 'Letter',
      letterAndNumberValidator: 'LetterAndNumber',
      mobilePhoneValidator: 'MobilePhone',
      emailValidator: 'Email',
      urlValidator: 'URL',
      noChineseValidator: 'Non-Chinese',
      chineseValidator: 'Chinese',

      rowspanNotConsistentForMergeEntireRow:
        "Cells in this row don't have the same rowspan, operation failed.",
      colspanNotConsistentForMergeEntireColumn:
        "Cells in this column don't have the same colspan, operation failed.",
      rowspanNotConsistentForDeleteEntireRow:
        "Cells in this row don't have the same rowspan, operation failed.",
      colspanNotConsistentForDeleteEntireColumn:
        "Cells in this column don't have the same colspan, operation failed.",
      lastColCannotBeDeleted: 'The last col cannot be deleted.',
      lastRowCannotBeDeleted: 'The last row cannot be deleted.',
    },

    toolbar: {
      undoHint: 'Undo',
      redoHint: 'Redo',
      pcLayout: 'PC',
      padLayout: 'Pad',
      mobileLayout: 'H5',
      nodeTreeHint: 'Tree View Of Component Hierarchy',
      nodeTreeTitle: 'Tree View Of Component Hierarchy',
      clear: 'Clear',
      preview: 'Preview',
      importJson: 'Import',
      exportJson: 'Export',
      exportCode: 'Codes',
      generateCode: 'Generate Code',
      generateSFC: 'Generate SFC',
    },

    setting: {
      basicSetting: 'Basic Setting',
      attributeSetting: 'Attribute Setting',
      commonSetting: 'Common Setting',
      advancedSetting: 'Advanced Setting',
      eventSetting: 'Event Setting',
      uniqueName: 'Unique Name',
      editNameHelp: 'Press enter to confirm the modification',
      label: 'Label',
      displayType: 'Type',
      defaultValue: 'Default Value',
      placeholder: 'Placeholder',
      startPlaceholder: 'Start Placeholder',
      endPlaceholder: 'End Placeholder',
      widgetColumnWidth: 'Width',
      widgetSize: 'Size',
      fontSize: 'Font Size',
      textAlign: 'Text Align',
      showStops: 'Show Stops',
      displayStyle: 'Display Style',
      inlineLayout: 'inline',
      blockLayout: 'block',
      buttonStyle: 'Show As Button',
      border: 'Show Border',
      labelWidth: 'Width Of Label',
      rows: 'Rows',
      labelHidden: 'Hide Label',
      required: 'Required',
      requiredHint: 'Failure Hint',
      validation: 'Validation',
      validationHelp: 'Regular expressions supported',
      validationHint: 'Validation Hint',
      readonly: 'Readonly',
      disabled: 'Disabled',
      hidden: 'Hidden',
      textContent: 'Text',
      preWrap: 'Line Wrap',
      htmlContent: 'HTML',
      clearable: 'Clearable',
      editable: 'Editable',
      format: 'Format',
      valueFormat: 'Value Format',
      showPassword: 'Show Reveal',
      filterable: 'Filterable',
      allowCreate: 'Allow Create',
      remote: 'Remote Query',
      automaticDropdown: 'Automatic Dropdown',
      multiple: 'Multiple',
      multipleLimit: 'Multiple Limit',
      checkStrictly: 'Any Level Selectable',
      showAllLevels: 'Show All Levels',
      contentPosition: 'Content Position',
      plain: 'Plain',
      round: 'Round',
      circle: 'Circle',
      icon: 'Icon',
      optionsSetting: 'Options Setting',
      addOption: 'Add Option',
      importOptions: 'Import Options',
      resetDefault: 'Reset Default',
      uploadSetting: 'Upload Setting',
      uploadURL: 'Upload URL',
      uploadTip: 'Tip Content',
      withCredentials: 'Send Cookie',
      multipleSelect: 'File Multi-select',
      showFileList: 'Show File List',
      limit: 'Max Upload Number',
      fileMaxSize: 'Max Size(MB)',
      fileTypes: 'Upload File Types',
      fileTypesHelp: 'Allows to add more file types',
      headers: 'Request Headers',
      optionType: '选项类型',
      optionSyncUrl: '请求地址',
      optionMedium: '选择字典',
      optionValue: '选择值名',
      optionLabel: '选择标签名',
      optionChildrenLabel: '子节点属性名',
      signType: '签名类型',
      signUrl: '签名链接',

      cellWidth: 'Width',
      cellHeight: 'Height',
      wordBreak: 'Line Wrap',
      gridColHeight: 'Height Of Col(px)',
      gutter: 'Gutter(px)',
      columnSetting: 'Cols Setting',
      colsOfGrid: 'Cols Of Grid:',
      colSpanTitle: 'Spans Of Col',
      colOffsetTitle: 'Offset Of Col',
      colPushTitle: 'Push Of Col',
      colPullTitle: 'Pull Of Col',
      addColumn: 'Add Column',
      responsive: 'Responsive',

      tabPaneSetting: 'Tab Panes',
      addTabPane: 'Add Tab Pane',
      paneActive: 'Active',

      customLabelIcon: 'Custom Label',
      labelIconClass: 'Label Icon Class',
      labelIconPosition: 'Label Icon Position',
      labelTooltip: 'Label Tooltip',
      minValue: 'Min Value',
      maxValue: 'Max Value',
      precision: 'Precision',
      step: 'Step',
      controlsPosition: 'Controls Position',
      minLength: 'Min Length',
      maxLength: 'Max Length',
      showWordLimit: 'Show Word Limit',
      prefixIcon: 'Prefix Icon',
      suffixIcon: 'Suffix Icon',
      inputButton: 'Input Button Setting',
      appendButton: 'Append Button',
      appendButtonDisabled: 'Button Disabled',
      appendButtonIcon: 'Append Button Icon',
      buttonIcon: 'Button Icon',
      switchWidth: 'Width of Switch(px)',
      activeText: 'Active Text',
      inactiveText: 'Inactive Text',
      activeColor: 'Active Color',
      inactiveColor: 'Inactive Color',
      maxStars: 'Stars Max Number',
      lowThreshold: 'Low Threshold',
      highThreshold: 'High Threshold',
      allowHalf: 'Allow Half',
      showText: 'Show Text',
      showScore: 'Show Score',
      range: 'Range',
      vertical: 'Vertical',
      showBlankRow: 'Show Blank Row',
      showRowNumber: 'Show Row Number',

      insertColumnToLeft: 'insert column to left',
      insertColumnToRight: 'insert column to right',
      insertRowAbove: 'insert row above',
      insertRowBelow: 'insert row below',
      copyInsertRowBelow: '复制当前行',
      mergeLeftColumn: 'merge left cell',
      mergeLeftColumnTwo: '合并左侧2个单元格',
      mergeLeftColumnThree: '合并左侧3个单元格',
      mergeLeftColumnFour: '合并左侧4个单元格',
      mergeRightColumn: 'merge right cell',
      mergeRightColumnTwo: '合并右侧2个单元格',
      mergeRightColumnThree: '合并右侧3个单元格',
      mergeRightColumnFour: '合并右侧4个单元格',
      mergeEntireRow: 'merge entire row',
      mergeRowAbove: 'merge cell above',
      mergeRowAboveTwo: '合并上方2个单元格',
      mergeRowAboveThree: '合并上方3个单元格',
      mergeRowAboveFour: '合并上方4个单元格',
      mergeRowBelow: 'merge cell below',
      mergeRowBelowTwo: '合并下方2个单元格',
      mergeRowBelowThree: '合并下方3个单元格',
      mergeRowBelowFour: '合并下方4个单元格',
      mergeEntireColumn: 'merge entire column',
      undoMergeCol: 'undo merge column',
      undoMergeRow: 'undo merge row',
      deleteEntireCol: 'delete entire column',
      deleteEntireRow: 'delete entire row',

      widgetName: 'Unique Name',
      formSize: 'Size',
      labelPosition: 'Position Of Label',
      topPosition: 'Top',
      leftPosition: 'Left',
      labelAlign: 'Label Align',
      leftAlign: 'Left',
      centerAlign: 'Center',
      rightAlign: 'Right',
      formCss: 'Form CSS',
      addCss: 'Edit',
      customClass: 'Custom Class',
      globalFunctions: 'Global Functions',
      addEventHandler: 'Edit',
      editWidgetEventHandler: 'Edit Widget Event Handler',
      editFormEventHandler: 'Edit Form Event Handler',
      formSFCSetting: 'SFC Setting',
      formModelName: 'Model Name',
      formRefName: 'Ref Name',
      formRulesName: 'Rules Name',
      syntaxCheckWarning: 'Syntax error in the javascript codes, please check again!',

      // data-table
      tableWidth: 'Width(px/%)',
      tableHeight: 'Height(px/%)',
      showCheckBox: 'Show CheckBox',
      showIndex: 'Show Row Number',
      showPagination: 'Show Pagination',
      smallPagination: 'Small Pagination',
      tableColEdit: 'Edit Cols',
      tableDataEdit: 'Edit Data',
      stripe: 'Stripe',
      showSummary: 'Show Summary',
      rowSpacing: 'Row Spacing(px)',
      editAction: 'Edit...',
      columnName: 'Name',
      columnLabel: 'Label',
      columnWidth: 'Width(px/%)',
      visibleColumn: 'Visible',
      sortableColumn: 'Sortable',
      fixedColumn: 'Fixed',
      alignTypeOfColumn: 'Align',
      formatOfColumn: 'Format',
      actionColumn: 'Action',
      addTableColumn: 'Add New Column',
      deleteTableColumn: 'Delete This Column',
      OnlyOneColumnCannotBeDeleted: 'The last column cannot be deleted.',
      tableWidth: "宽度(px/%)",
      tableHeight: "高度(px/%)",
      showCheckBox: "是否显示复选框列",
      showIndex: "是否显示行号",
      showPagination: "是否显示分页",
      paginationAlign: "分页水平对齐",
      smallPagination: "小型分页",
      autoColumnWidthDisabled: "禁用列宽自动充满",
      tableColEdit: "表格列编辑",
      tableDataEdit: "表格数据编辑",
      showSummary: "是否合计",
      stripe: "是否斑马线",
      rowSpacing: "行距（px）",
      editAction: "编辑...",
      columnName: "字段名称",
      columnType: "字段类型",
      columnLabel: "显示名称",
      columnWidth: "列宽(px)",
      visibleColumn: "是否显示",
      sortableColumn: "是否排序",
      customSortColumn: "后端排序",
      fixedColumn: "是否固定",
      alignTypeOfColumn: "对齐方式",
      formatOfColumn: "格式化",
      customRenderGroup: "动态渲染",
      renderFunction: "渲染函数",
      actionColumn: "操作",
      addTableColumn: "增加列",
      deleteTableColumn: "删除列",
      onlyOneColumnCannotBeDeleted: "表格只有一列时不可删除.",
      treeDataEnabled: "允许加载树形数据",
      rowKeyOfTreeData: "行数据Key",
      childrenKeyOfTreeData: "树形数据子节点键",
      showButtonsColumn: "显示操作按钮列",
      buttonsColumnEdit: "操作按钮编辑",
      buttonsColumnTitle: "列标题",
      buttonsColumnWidth: "列宽度（px）",
      operationButtonsSetting: "操作按钮设置",
      operationButtonName: "名称",
      operationButtonLabel: "文字",
      operationButtonType: "类型",
      operationButtonSize: "大小",
      operationButtonRound: "圆角",
      operationButtonHidden: "隐藏",
      operationButtonDisabled: "禁用",
      addOperationButton: "增加按钮",
      deleteOperationButtonHint: "确定删除该按钮？",
      operationButtonDuplicatedNameError: "按钮名称不可重复",
      tableHeader: "表头",
      insertTableHeader: "插入表头",
      insertTableSubHeader: "插入下级表头",
      tableDataColumn: "数据列",
      insertTableDataColumn: "插入数据列",
      insertTableSubDataColumn: "插入下级数据列",
      onlyDragBetweenSiblingNodes: "只能在同级节点之间进行拖拽排序！",
      getHeaderLevelTitle: function(e) {
          return e + "级表头"
      },
      lineNumber: "序号",
      dsEnabled: "使用数据源",
      dsName: "指定数据源",
      dataSetName: "指定数据集",
      labelKeyName: "选项标签名",
      valueKeyName: "选项值名",
      childrenKeyName: "子节点属性名称",
      dataSource: "数据源",
      noDataSource: "暂无数据源",
      addDataSource: "新增数据源",
      importDataSource: "导入数据源",
      clearExistingDataSource: "导入后清空原有数据源",
      remainExistingDataSource: "追加到已有数据源之后",
      exportDataSource: "导出数据源",
      selectDataSourceForExport: "选择导出数据源",
      dataSourceChecked: "选中导出",
      previewDataSourceExportResult: "导出结果预览",
      dataSourceSetting: "数据源设置",
      deleteDataSourceHint: "确认删除该数据源？",
      fieldValueRequired: "输入内容不可为空",
      formulaSize: "大小",
      large: "大",
      medium: "中",
      small: "小",
      mini: "迷你",
      editAction: "编辑...",
    },
  },
};
