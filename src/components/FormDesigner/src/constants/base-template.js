export const BaseTemplateType = {
  Standard: 'standard',
  Empty: 'empty',
};


export const BaseTemplateOption = [
  {
    value: BaseTemplateType.Standard,
    label: '标准模板',
  },
  {
    value: BaseTemplateType.Empty,
    label: '空模板',
  },
]


export const  BaseTemplateFormJson = {
  [BaseTemplateType.Empty]: [
    {
      "cols": [
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "text-675675",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "文本",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext39502"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-59335",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-59335"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "unitProjName",
                "label": "文本",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input97584"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-21044",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-21044"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext57204",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "文本",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext57204"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-18269",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-18269"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "divisionProjName",
                "label": "文本",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input64702"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-82667",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-82667"
        }
      ],
      "id": "table-row-100286",
      "merged": false
    },
  ],
  [BaseTemplateType.Standard]:  [
    {
      "cols": [
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "text-675675",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "单位（子单位）工程名称",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext39502"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-59335",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-59335"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "unitProjName",
                "label": "单位（子单位）工程名称",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input97584"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-21044",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-21044"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext57204",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "分部（子单位）工程名称",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext57204"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-18269",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-18269"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "divisionProjName",
                "label": "分部（子单位）工程名称",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input64702"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-82667",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-82667"
        }
      ],
      "id": "table-row-100286",
      "merged": false
    },
    {
      "cols": [
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext86104",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "分项工程名称",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext86104"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-95130",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-95130"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "subItemProjName",
                "label": "分项工程名称",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input74630"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-59353",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-59353"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext40606",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "验收部位",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext40606"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-86196",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-86196"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "acptProjName",
                "label": "验收部位",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input22725"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-75134",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-75134"
        }
      ],
      "id": "table-row-66838",
      "merged": false
    },
    {
      "cols": [
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext96953",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "总承包单位",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext96953"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-23100",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-23100"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "acptGeneralContractName",
                "label": "总承包单位",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input64118"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-47872",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-47872"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext45580",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "项目负责人",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext45580"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-78941",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-78941"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "acptGlctLeaderName",
                "label": "项目负责人",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input55338"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-63562",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-63562"
        }
      ],
      "id": "table-row-14968",
      "merged": false
    },
    {
      "cols": [
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext26509",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "施工单位",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext26509"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-93424",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-93424"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "acptConstructionName",
                "label": "施工单位",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input102394"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-54726",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-54726"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext48232",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "项目负责人",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext48232"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-112858",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-112858"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "acptCnLeaderName",
                "label": "项目负责人",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input22359"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-46203",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-46203"
        }
      ],
      "id": "table-row-14491",
      "merged": false
    },
    {
      "cols": [
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext60955",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "分包单位",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext60955"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-25329",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-25329"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "acptSubContractName",
                "label": "总承包单位",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input70544"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-87913",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-87913"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext48429",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "分包项目负责人",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext48429"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-73539",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-73539"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "acptSubContractLeaderName",
                "label": "分包项目负责人",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input88881"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-74834",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-74834"
        }
      ],
      "id": "table-row-84298",
      "merged": false
    },
    {
      "cols": [
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "pid-77386",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "施工执行标\n准名称及编号",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext77386"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-95055",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-95055"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "constructionStandards",
                "label": "施工执行标 准名称及编号",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input96890"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-43293",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-43293"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "static-text",
              "icon": "static-text",
              "formItemFlag": false,
              "options": {
                "name": "statictext52137",
                "columnWidth": "200px",
                "hidden": false,
                "textContent": "专业工长（施工人员）",
                "textAlign": "center",
                "fontSize": "13px",
                "preWrap": false,
                "label": "static-text"
              },
              "id": "statictext52137"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-102567",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-102567"
        },
        {
          "type": "table-cell",
          "category": "container",
          "icon": "table-cell",
          "internal": true,
          "widgetList": [
            {
              "type": "input",
              "icon": "text-field",
              "formItemFlag": true,
              "options": {
                "name": "workerName",
                "label": "专业工长（施工人员）",
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "columnWidth": "200px",
                "size": "",
                "labelWidth": null,
                "labelHidden": true,
                "readonly": false,
                "disabled": false,
                "hidden": false,
                "clearable": true,
                "showPassword": false,
                "required": false,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "permission": "",
                "labelIconClass": null,
                "labelIconPosition": "rear",
                "labelTooltip": null,
                "minLength": null,
                "maxLength": null,
                "showWordLimit": false,
                "prefixIcon": "",
                "suffixIcon": "",
                "appendButton": false,
                "appendButtonDisabled": false,
                "buttonIcon": "el-icon-search"
              },
              "id": "input68919"
            }
          ],
          "merged": false,
          "options": {
            "name": "table-cell-41367",
            "cellWidth": "",
            "cellHeight": "",
            "colspan": 1,
            "rowspan": 1,
            "wordBreak": false,
            "customClass": ""
          },
          "id": "table-cell-41367"
        }
      ],
      "id": "table-row-15158",
      "merged": false
    }
  ],
}
