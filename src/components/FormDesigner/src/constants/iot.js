export const PropertyType = {
  INT: 'INT',
  // 单精度浮点类型
  FLOAT: 'FLOAT',
  // 双精度浮点类型
  DOUBLE: 'DOUBLE',
  ENUM: 'ENUM',
  BOOL: 'BOOL',
  TEXT: 'TEXT',
  DATE: 'DATE',
  STRUCT: 'STRUCT',
  ARRAY: 'ARRAY',
};

export const CompTypePropertyType = {
  input: PropertyType.TEXT,
  select: PropertyType.ENUM,
  radio: PropertyType.ENUM,
  checkbox: PropertyType.ENUM,
  switch: PropertyType.BOOL,
  inputNumber: PropertyType.INT,
  textarea: PropertyType.TEXT,
  date: PropertyType.DATE,
  time: PropertyType.DATE,
  editor: PropertyType.TEXT,
  colorPicker: PropertyType.TEXT,
  cascader: PropertyType.TEXT,
  upload: PropertyType.TEXT,
  link: PropertyType.TEXT,
  dialogList: PropertyType.TEXT,
  text: PropertyType.TEXT,
  html: PropertyType.TEXT,
  // 'barCode': PropertyType.TEXT,
  // 'button': PropertyType.TEXT,
  // 'divider': PropertyType.TEXT,
  // 'alert': PropertyType.TEXT,
  // 'row': PropertyType.TEXT,
  // 'dynamicTable': PropertyType.TEXT,
  // 'table': PropertyType.TEXT,
  // 'tdItem': PropertyType.TEXT,
  // 'slider': PropertyType.TEXT,
  // 'rate': PropertyType.TEXT,
};
