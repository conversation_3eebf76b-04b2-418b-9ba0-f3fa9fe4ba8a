// optionType-editor 常量
export const OptionTypeValue = {
  static: 'static',
  dynamic: 'dynamic',
  medium: 'medium',
};

export const OptionType = [
  { label: '静态数据', value: OptionTypeValue.static },
  { label: '动态数据', value: OptionTypeValue.dynamic },
  { label: '字典', value: OptionTypeValue.medium },
];

// 级联
export const CascaderOptionType = [
  { label: '静态数据', value: OptionTypeValue.static },
  { label: '动态数据', value: OptionTypeValue.dynamic },
];

// signType-editor 常量
export const SignOptionTypeValue = {
  sign: 'sign',
  url: 'url',
};

export const SignOptionType = [
  { label: '签名', value: SignOptionTypeValue.sign },
  { label: '链接', value: SignOptionTypeValue.url },
];

// 单元格类型
export const TableCellType = [
  { label: '静态文字', value: 'static-text' },
  { label: '单行输入', value: 'input' },
  { label: '多行输入', value: 'textarea' },
  { label: '单选项', value: 'radio' },
  { label: '签名', value: 'sign' },
];

export const TableBorderType = {
  borderTop: 'borderTop',
  borderBottom: 'borderBottom',
};

// 隐藏 border 类型
export const hiddenTableBorderType = [
  { label: '上边框', value: TableBorderType.borderTop },
  { label: '下边框', value: TableBorderType.borderBottom },
];

export const MarginType = {
  marginTop: 'marginTop',
  marginBottom: 'marginBottom',
  marginLeft: 'marginLeft',
  marginRight: 'marginRight',
};

// 隐藏 border 类型
export const marginTypeOption = [
  { label: '上边距', value: MarginType.marginTop },
  { label: '下边距', value: MarginType.marginBottom },
  { label: '左边距', value: MarginType.marginLeft },
  { label: '右边距', value: MarginType.marginRight },
];
