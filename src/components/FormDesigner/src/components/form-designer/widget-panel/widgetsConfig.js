import { SignOptionTypeValue } from "@@/constants/setting-panel.js";
import { StandardTableType, StandardTableFormJson } from '@@/constants/standard-table.js';

// 业务组件
export const businessContainers = [
  {
    type: 'standardTable',
    category: 'container',
    icon: 'file-upload-field',
    widgetList: StandardTableFormJson[StandardTableType.tujian],
    options: {
      name: '',
      hidden: false,
      standardType: StandardTableType.tujian,
    },
  },
  {
    type: 'baseInfoTable',
    category: 'container',
    icon: 'file-upload-field',
    "rows": [
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "text-675675",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "单位（子单位）工程名称",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext39502"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-59335",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-59335"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "unitProjName",
                  "label": "单位（子单位）工程名称",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input97584"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-21044",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-21044"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext57204",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "分部（子单位）工程名称",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext57204"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-18269",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-18269"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "divisionProjName",
                  "label": "分部（子单位）工程名称",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input64702"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-82667",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-82667"
          }
        ],
        "id": "table-row-100286",
        "merged": false
      },
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext86104",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "分项工程名称",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext86104"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-95130",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-95130"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "subItemProjName",
                  "label": "分项工程名称",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input74630"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-59353",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-59353"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext40606",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "验收部位",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext40606"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-86196",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-86196"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "acptProjName",
                  "label": "验收部位",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input22725"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-75134",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-75134"
          }
        ],
        "id": "table-row-66838",
        "merged": false
      },
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext96953",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "总承包单位",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext96953"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-23100",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-23100"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "acptGeneralContractName",
                  "label": "总承包单位",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input64118"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-47872",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-47872"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext45580",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "项目负责人",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext45580"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-78941",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-78941"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "acptGlctLeaderName",
                  "label": "项目负责人",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input55338"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-63562",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-63562"
          }
        ],
        "id": "table-row-14968",
        "merged": false
      },
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext26509",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "施工单位",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext26509"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-93424",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-93424"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "acptConstructionName",
                  "label": "施工单位",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input102394"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-54726",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-54726"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext48232",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "项目负责人",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext48232"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-112858",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-112858"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "acptCnLeaderName",
                  "label": "项目负责人",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input22359"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-46203",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-46203"
          }
        ],
        "id": "table-row-14491",
        "merged": false
      },
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext60955",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "分包单位",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext60955"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-25329",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-25329"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "acptSubContractName",
                  "label": "总承包单位",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input70544"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-87913",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-87913"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext48429",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "分包项目负责人",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext48429"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-73539",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-73539"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "acptSubContractLeaderName",
                  "label": "分包项目负责人",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input88881"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-74834",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-74834"
          }
        ],
        "id": "table-row-84298",
        "merged": false
      },
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "pid-77386",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "施工执行标\n准名称及编号",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext77386"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-95055",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-95055"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "constructionStandards",
                  "label": "施工执行标 准名称及编号",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input96890"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-43293",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-43293"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext52137",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "专业工长（施工人员）",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext52137"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-102567",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-102567"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "workerName",
                  "label": "专业工长（施工人员）",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "请输入",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input68919"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-41367",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-41367"
          }
        ],
        "id": "table-row-15158",
        "merged": false
      }
    ],
    "options": {
      "name": "table22798",
      "baseTemplate": "",
      "hidden": false,
      "hiddenBoder": '',
      "customClass": []
    },
    "id": "table22798",
    "widgetList": [
      {
        "type": "textarea",
        "icon": "textarea-field",
        "formItemFlag": true,
        "options": {
          "name": "textarea60401",
          "label": "textarea",
          "labelAlign": "label-left-align",
          "rows": 1,
          "defaultValue": "表3.0.12-1   检验批质量验收记录",
          "placeholder": "",
          "columnWidth": "200px",
          "size": "",
          "labelWidth": null,
          "labelHidden": true,
          "readonly": false,
          "disabled": false,
          "hidden": false,
          "required": false,
          "requiredHint": "",
          "validation": "",
          "validationHint": "",
          "permission": "",
          "labelIconClass": null,
          "labelIconPosition": "rear",
          "labelTooltip": null,
          "minLength": null,
          "maxLength": null,
          "showWordLimit": false
        },
        "id": "textarea60401"
      },
      {
        "type": "grid",
        "category": "container",
        "icon": "grid",
        "cols": [
          {
            "type": "grid-col",
            "category": "container",
            "icon": "grid-col",
            "internal": true,
            "widgetList": [
              {
                "type": "input",
                "icon": "text-field",
                "formItemFlag": true,
                "options": {
                  "name": "input104725",
                  "label": "工程编号",
                  "labelAlign": "",
                  "type": "text",
                  "defaultValue": "",
                  "placeholder": "",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": false,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "showPassword": false,
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null,
                  "minLength": null,
                  "maxLength": null,
                  "showWordLimit": false,
                  "prefixIcon": "",
                  "suffixIcon": "",
                  "appendButton": false,
                  "appendButtonDisabled": false,
                  "buttonIcon": "el-icon-search"
                },
                "id": "input104725"
              }
            ],
            "options": {
              "name": "gridCol67161",
              "hidden": false,
              "span": 8,
              "offset": 0,
              "push": 0,
              "pull": 0,
              "responsive": false,
              "md": 12,
              "sm": 12,
              "xs": 12
            },
            "id": "grid-col-67161"
          },
          {
            "type": "grid-col",
            "category": "container",
            "icon": "grid-col",
            "internal": true,
            "widgetList": [],
            "options": {
              "name": "gridCol84430",
              "hidden": false,
              "span": 12,
              "offset": 0,
              "push": 0,
              "pull": 0,
              "responsive": false,
              "md": 12,
              "sm": 12,
              "xs": 12
            },
            "id": "grid-col-84430"
          }
        ],
        "options": {
          "name": "grid34490",
          "hidden": false,
          "gutter": 12,
          "colHeight": "38"
        },
        "id": "grid34490"
      },
      {
        "type": "table",
        "category": "container",
        "icon": "table",
        "rows": [
          {
            "cols": [
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": false,
                    "options": {
                      "name": "statictext39502",
                      "columnWidth": "200px",
                      "hidden": false,
                      "textContent": "单位（子单位）工程名称",
                      "textAlign": "center",
                      "fontSize": "13px",
                      "preWrap": false,
                      "label": "static-text"
                    },
                    "id": "statictext39502"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-59335",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-59335"
              },
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "input",
                    "icon": "text-field",
                    "formItemFlag": true,
                    "options": {
                      "name": "input97584",
                      "label": "单位（子单位）工程名称",
                      "labelAlign": "",
                      "type": "text",
                      "defaultValue": "",
                      "placeholder": "请输入",
                      "columnWidth": "200px",
                      "size": "",
                      "labelWidth": null,
                      "labelHidden": true,
                      "readonly": false,
                      "disabled": false,
                      "hidden": false,
                      "clearable": true,
                      "showPassword": false,
                      "required": false,
                      "requiredHint": "",
                      "validation": "",
                      "validationHint": "",
                      "permission": "",
                      "labelIconClass": null,
                      "labelIconPosition": "rear",
                      "labelTooltip": null,
                      "minLength": null,
                      "maxLength": null,
                      "showWordLimit": false,
                      "prefixIcon": "",
                      "suffixIcon": "",
                      "appendButton": false,
                      "appendButtonDisabled": false,
                      "buttonIcon": "el-icon-search"
                    },
                    "id": "input97584"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-21044",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-21044"
              },
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": false,
                    "options": {
                      "name": "statictext57204",
                      "columnWidth": "200px",
                      "hidden": false,
                      "textContent": "分部（子单位）工程名称",
                      "textAlign": "center",
                      "fontSize": "13px",
                      "preWrap": false,
                      "label": "static-text"
                    },
                    "id": "statictext57204"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-18269",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-18269"
              },
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "input",
                    "icon": "text-field",
                    "formItemFlag": true,
                    "options": {
                      "name": "input64702",
                      "label": "分部（子单位）工程名称",
                      "labelAlign": "",
                      "type": "text",
                      "defaultValue": "",
                      "placeholder": "请输入",
                      "columnWidth": "200px",
                      "size": "",
                      "labelWidth": null,
                      "labelHidden": true,
                      "readonly": false,
                      "disabled": false,
                      "hidden": false,
                      "clearable": true,
                      "showPassword": false,
                      "required": false,
                      "requiredHint": "",
                      "validation": "",
                      "validationHint": "",
                      "permission": "",
                      "labelIconClass": null,
                      "labelIconPosition": "rear",
                      "labelTooltip": null,
                      "minLength": null,
                      "maxLength": null,
                      "showWordLimit": false,
                      "prefixIcon": "",
                      "suffixIcon": "",
                      "appendButton": false,
                      "appendButtonDisabled": false,
                      "buttonIcon": "el-icon-search"
                    },
                    "id": "input64702"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-82667",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-82667"
              }
            ],
            "id": "table-row-100286",
            "merged": false
          },
          {
            "cols": [
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": false,
                    "options": {
                      "name": "statictext86104",
                      "columnWidth": "200px",
                      "hidden": false,
                      "textContent": "分项工程名称",
                      "textAlign": "center",
                      "fontSize": "13px",
                      "preWrap": false,
                      "label": "static-text"
                    },
                    "id": "statictext86104"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-95130",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-95130"
              },
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "input",
                    "icon": "text-field",
                    "formItemFlag": true,
                    "options": {
                      "name": "input74630",
                      "label": "分项工程名称",
                      "labelAlign": "",
                      "type": "text",
                      "defaultValue": "",
                      "placeholder": "请输入",
                      "columnWidth": "200px",
                      "size": "",
                      "labelWidth": null,
                      "labelHidden": true,
                      "readonly": false,
                      "disabled": false,
                      "hidden": false,
                      "clearable": true,
                      "showPassword": false,
                      "required": false,
                      "requiredHint": "",
                      "validation": "",
                      "validationHint": "",
                      "permission": "",
                      "labelIconClass": null,
                      "labelIconPosition": "rear",
                      "labelTooltip": null,
                      "minLength": null,
                      "maxLength": null,
                      "showWordLimit": false,
                      "prefixIcon": "",
                      "suffixIcon": "",
                      "appendButton": false,
                      "appendButtonDisabled": false,
                      "buttonIcon": "el-icon-search"
                    },
                    "id": "input74630"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-59353",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-59353"
              },
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": false,
                    "options": {
                      "name": "statictext40606",
                      "columnWidth": "200px",
                      "hidden": false,
                      "textContent": "验收部位",
                      "textAlign": "center",
                      "fontSize": "13px",
                      "preWrap": false,
                      "label": "static-text"
                    },
                    "id": "statictext40606"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-86196",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-86196"
              },
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "input",
                    "icon": "text-field",
                    "formItemFlag": true,
                    "options": {
                      "name": "input22725",
                      "label": "验收部位",
                      "labelAlign": "",
                      "type": "text",
                      "defaultValue": "",
                      "placeholder": "请输入",
                      "columnWidth": "200px",
                      "size": "",
                      "labelWidth": null,
                      "labelHidden": true,
                      "readonly": false,
                      "disabled": false,
                      "hidden": false,
                      "clearable": true,
                      "showPassword": false,
                      "required": false,
                      "requiredHint": "",
                      "validation": "",
                      "validationHint": "",
                      "permission": "",
                      "labelIconClass": null,
                      "labelIconPosition": "rear",
                      "labelTooltip": null,
                      "minLength": null,
                      "maxLength": null,
                      "showWordLimit": false,
                      "prefixIcon": "",
                      "suffixIcon": "",
                      "appendButton": false,
                      "appendButtonDisabled": false,
                      "buttonIcon": "el-icon-search"
                    },
                    "id": "input22725"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-75134",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-75134"
              }
            ],
            "id": "table-row-66838",
            "merged": false
          },
          {
            "cols": [
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": false,
                    "options": {
                      "name": "statictext96953",
                      "columnWidth": "200px",
                      "hidden": false,
                      "textContent": "总承包单位",
                      "textAlign": "center",
                      "fontSize": "13px",
                      "preWrap": false,
                      "label": "static-text"
                    },
                    "id": "statictext96953"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-23100",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-23100"
              },
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "input",
                    "icon": "text-field",
                    "formItemFlag": true,
                    "options": {
                      "name": "input64118",
                      "label": "总承包单位",
                      "labelAlign": "",
                      "type": "text",
                      "defaultValue": "",
                      "placeholder": "请输入",
                      "columnWidth": "200px",
                      "size": "",
                      "labelWidth": null,
                      "labelHidden": true,
                      "readonly": false,
                      "disabled": false,
                      "hidden": false,
                      "clearable": true,
                      "showPassword": false,
                      "required": false,
                      "requiredHint": "",
                      "validation": "",
                      "validationHint": "",
                      "permission": "",
                      "labelIconClass": null,
                      "labelIconPosition": "rear",
                      "labelTooltip": null,
                      "minLength": null,
                      "maxLength": null,
                      "showWordLimit": false,
                      "prefixIcon": "",
                      "suffixIcon": "",
                      "appendButton": false,
                      "appendButtonDisabled": false,
                      "buttonIcon": "el-icon-search"
                    },
                    "id": "input64118"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-47872",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-47872"
              },
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": false,
                    "options": {
                      "name": "statictext45580",
                      "columnWidth": "200px",
                      "hidden": false,
                      "textContent": "项目负责人",
                      "textAlign": "center",
                      "fontSize": "13px",
                      "preWrap": false,
                      "label": "static-text"
                    },
                    "id": "statictext45580"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-78941",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-78941"
              },
              {
                "type": "table-cell",
                "category": "container",
                "icon": "table-cell",
                "internal": true,
                "widgetList": [
                  {
                    "type": "input",
                    "icon": "text-field",
                    "formItemFlag": true,
                    "options": {
                      "name": "input55338",
                      "label": "项目负责人",
                      "labelAlign": "",
                      "type": "text",
                      "defaultValue": "",
                      "placeholder": "请输入",
                      "columnWidth": "200px",
                      "size": "",
                      "labelWidth": null,
                      "labelHidden": true,
                      "readonly": false,
                      "disabled": false,
                      "hidden": false,
                      "clearable": true,
                      "showPassword": false,
                      "required": false,
                      "requiredHint": "",
                      "validation": "",
                      "validationHint": "",
                      "permission": "",
                      "labelIconClass": null,
                      "labelIconPosition": "rear",
                      "labelTooltip": null,
                      "minLength": null,
                      "maxLength": null,
                      "showWordLimit": false,
                      "prefixIcon": "",
                      "suffixIcon": "",
                      "appendButton": false,
                      "appendButtonDisabled": false,
                      "buttonIcon": "el-icon-search"
                    },
                    "id": "input55338"
                  }
                ],
                "merged": false,
                "options": {
                  "name": "table-cell-63562",
                  "cellWidth": "",
                  "cellHeight": "",
                  "colspan": 1,
                  "rowspan": 1,
                  "wordBreak": false,
                  "customClass": ""
                },
                "id": "table-cell-63562"
              }
            ],
            "id": "table-row-14968",
            "merged": false
          }
        ],
        "options": {
          "name": "table22798",
          "hidden": false,
          "customClass": []
        },
        "id": "table22798"
      }
    ],
  },
  {
    type: 'inspectionItemsTable',
    category: 'container',
    icon: 'file-upload-field',
    "rows": [
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext44629",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "类别",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext44629"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-75599",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-75599"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext47158",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "序号",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext47158"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-91391",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-91391"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext50091",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "检验项目",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext50091"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-67895",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-67895"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext108342",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "质量标准",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext108342"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-110219",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-110219"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext33792",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "单位",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext33792"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-45537",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-45537"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext49061",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "施工单位自检记录",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext49061"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-83330",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 2,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-83330"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": true,
            "options": {
              "name": "table-cell-28710",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 2,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-28710"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext75413",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "检查结果",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext75413"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-77347",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-77347"
          }
        ],
        "id": "table-row-100286",
        "merged": false
      },
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-27942",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-27942"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-83681",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-83681"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-34694",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-34694"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-18365",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-18365"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-64136",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-64136"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-67230",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-67230"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-17027",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-17027"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-9072",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-9072"
          }
        ],
        "id": "table-row-66838",
        "merged": false
      },
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-106200",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-106200"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-21421",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-21421"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-71146",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-71146"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-31070",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-31070"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-113262",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false
            },
            "id": "table-cell-113262"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-34365",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-34365"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-102150",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-102150"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": false,
            "options": {
              "name": "table-cell-45114",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-45114"
          }
        ],
        "id": "table-row-14968",
        "merged": false
      }
    ],
    "options": {
      "name": "table49788",
      "hidden": false,
      "hiddenBoder": '',
      "customClass": []
    },
    "id": "table49788"
  },
  {
    type: 'approvalInfoTable',
    category: 'container',
    icon: 'file-upload-field',
    "rows": [
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext45369",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "总承包单位检查结果",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext45369"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-81892",
              "cellWidth": "100px",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-81892"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "sign",
                "icon": "el-icon-edit",
                "formItemFlag": true,
                "options": {
                  "name": "sign10914",
                  "label": "项目专业工程师",
                  "labelAlign": "",
                  "labelWidth": "120",
                  "labelHidden": false,
                  "columnWidth": "200px",
                  "disabled": false,
                  "hidden": false,
                  "required": false,
                  "requiredHint": "",
                  "customRule": "",
                  "customRuleHint": "",
                  "permission": "",
                  "signType": "sign",
                  "signUrl": ""
                },
                "id": "sign10914"
              },
              {
                "type": "date",
                "icon": "date-field",
                "formItemFlag": true,
                "options": {
                  "name": "date70515",
                  "label": "日期",
                  "labelAlign": "",
                  "type": "date",
                  "defaultValue": null,
                  "placeholder": "",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "editable": false,
                  "format": "yyyy-MM-dd",
                  "valueFormat": "yyyy-MM-dd",
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null
                },
                "id": "date70515"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-56087",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-56087"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext23886",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "施工单位检查结果",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext23886"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-7963",
              "cellWidth": "100px",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-7963"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "sign",
                "icon": "el-icon-edit",
                "formItemFlag": true,
                "options": {
                  "name": "sign52227",
                  "label": "项目专业质量检查员",
                  "labelAlign": "",
                  "labelWidth": "140",
                  "labelHidden": false,
                  "columnWidth": "200px",
                  "disabled": false,
                  "hidden": false,
                  "required": false,
                  "requiredHint": "",
                  "customRule": "",
                  "customRuleHint": "",
                  "permission": "",
                  "signType": "sign",
                  "signUrl": ""
                },
                "id": "sign52227"
              },
              {
                "type": "date",
                "icon": "date-field",
                "formItemFlag": true,
                "options": {
                  "name": "date56510",
                  "label": "日期",
                  "labelAlign": "",
                  "type": "date",
                  "defaultValue": null,
                  "placeholder": "",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "editable": false,
                  "format": "yyyy-MM-dd",
                  "valueFormat": "yyyy-MM-dd",
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null
                },
                "id": "date56510"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-101091",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-101091"
          }
        ],
        "id": "table-row-100286",
        "merged": false
      },
      {
        "cols": [
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": false,
                "options": {
                  "name": "statictext39298",
                  "columnWidth": "200px",
                  "hidden": false,
                  "textContent": "监理（建设）单位验收结论",
                  "textAlign": "center",
                  "fontSize": "13px",
                  "preWrap": false,
                  "label": "static-text"
                },
                "id": "statictext39298"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-80287",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 1,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-80287"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [
              {
                "type": "sign",
                "icon": "el-icon-edit",
                "formItemFlag": true,
                "options": {
                  "name": "sign23549",
                  "label": "专业监理工程师",
                  "labelAlign": "",
                  "labelWidth": "120",
                  "labelHidden": false,
                  "columnWidth": "200px",
                  "disabled": false,
                  "hidden": false,
                  "required": false,
                  "requiredHint": "",
                  "customRule": "",
                  "customRuleHint": "",
                  "permission": "",
                  "signType": "sign",
                  "signUrl": ""
                },
                "id": "sign23549"
              },
              {
                "type": "date",
                "icon": "date-field",
                "formItemFlag": true,
                "options": {
                  "name": "date17998",
                  "label": "日期",
                  "labelAlign": "",
                  "type": "date",
                  "defaultValue": null,
                  "placeholder": "",
                  "columnWidth": "200px",
                  "size": "",
                  "labelWidth": null,
                  "labelHidden": true,
                  "readonly": false,
                  "disabled": false,
                  "hidden": false,
                  "clearable": true,
                  "editable": false,
                  "format": "yyyy-MM-dd",
                  "valueFormat": "yyyy-MM-dd",
                  "required": false,
                  "requiredHint": "",
                  "validation": "",
                  "validationHint": "",
                  "permission": "",
                  "labelIconClass": null,
                  "labelIconPosition": "rear",
                  "labelTooltip": null
                },
                "id": "date17998"
              }
            ],
            "merged": false,
            "options": {
              "name": "table-cell-14313",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 3,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-14313"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": true,
            "options": {
              "name": "table-cell-11427",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 3,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-11427"
          },
          {
            "type": "table-cell",
            "category": "container",
            "icon": "table-cell",
            "internal": true,
            "widgetList": [],
            "merged": true,
            "options": {
              "name": "table-cell-40482",
              "cellWidth": "",
              "cellHeight": "",
              "colspan": 3,
              "rowspan": 1,
              "wordBreak": false,
              "customClass": ""
            },
            "id": "table-cell-40482"
          }
        ],
        "id": "table-row-66838",
        "merged": false
      }
    ],
    "options": {
      "name": "table121258",
      "hidden": false,
      "hiddenBoder": '',
      "customClass": []
    },
    "id": "table121258"
  },
];

// 模板
export const templates = [
  {
    type: 'gridThree',
    category: 'template',
    icon: 'grid',
    "widgetConfig": {
      "type": "grid",
      "category": "container",
      "icon": "grid",
      "cols": [
        {
          "type": "grid-col",
          "category": "container",
          "icon": "grid-col",
          "internal": true,
          "widgetList": [],
          "options": {
            "name": "gridCol34016",
            "hidden": false,
            "span": 8,
            "offset": 0,
            "push": 0,
            "pull": 0,
            "responsive": false,
            "md": 12,
            "sm": 12,
            "xs": 12,
            "customClass": ""
          },
          "id": "grid-col-34016"
        },
        {
          "type": "grid-col",
          "category": "container",
          "icon": "grid-col",
          "internal": true,
          "widgetList": [],
          "options": {
            "name": "gridCol94130",
            "hidden": false,
            "span": 8,
            "offset": 0,
            "push": 0,
            "pull": 0,
            "responsive": false,
            "md": 12,
            "sm": 12,
            "xs": 12,
            "customClass": ""
          },
          "id": "grid-col-94130"
        },
        {
          "type": "grid-col",
          "category": "container",
          "icon": "grid-col",
          "internal": true,
          "widgetList": [],
          "options": {
            "name": "gridCol70232",
            "hidden": false,
            "span": 8,
            "offset": 0,
            "push": 0,
            "pull": 0,
            "responsive": false,
            "md": 12,
            "sm": 12,
            "xs": 12,
            "customClass": ""
          },
          "id": "grid-col-70232"
        }
      ],
      "options": {
        "name": "grid79454",
        "hidden": false,
        "gutter": 12,
        "colHeight": null,
        "customClass": []
      },
      "id": "grid79454"
    },
  },
  {
    type: 'tableFourThree',
    category: 'template',
    icon: 'table',
    "widgetConfig": {
      "type": "table",
      "category": "container",
      "icon": "table",
      "rows": [
        {
          "cols": [
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-60855",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-60855"
            },
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-78835",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-78835"
            },
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-109036",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-109036"
            },
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-96490",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-96490"
            }
          ],
          "id": "table-row-100286",
          "merged": false
        },
        {
          "cols": [
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-14950",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-14950"
            },
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-33679",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-33679"
            },
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-74980",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-74980"
            },
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-29041",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-29041"
            }
          ],
          "id": "table-row-66838",
          "merged": false
        },
        {
          "cols": [
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-24855",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-24855"
            },
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-94848",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-94848"
            },
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-42628",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-42628"
            },
            {
              "type": "table-cell",
              "category": "container",
              "icon": "table-cell",
              "internal": true,
              "widgetList": [],
              "merged": false,
              "options": {
                "name": "table-cell-102164",
                "cellWidth": "",
                "cellHeight": "",
                "colspan": 1,
                "rowspan": 1,
                "wordBreak": false,
                "customClass": ""
              },
              "id": "table-cell-102164"
            }
          ],
          "id": "table-row-14968",
          "merged": false
        }
      ],
      "options": {
        "name": "table111721",
        "hidden": false,
        "customClass": []
      },
      "id": "table111721"
    },
  },
];

// 容器
export const containers = [
  {
    type: 'grid',
    category: 'container',
    icon: 'grid',
    cols: [],
    options: {
      name: '',
      hidden: false,
      gutter: 12,
      colHeight: null, // 栅格列统一高度属性，用于解决栅格列设置响应式布局浮动后被挂住的问题！！
      // customClass: '', // 自定义css类名
    },
  },
  {
    type: 'table',
    category: 'container',
    icon: 'table',
    rows: [],
    options: {
      name: '',
      hidden: false,
      // customClass: '', // 自定义css类名
    },
  },
  {
    type: 'nestTable',
    category: 'container',
    icon: 'table',
    rows: [],
    options: {
      name: '',
      label: '',
      hidden: false,
      // customClass: '', // 自定义css类名
    },
  },
  {
    type: "data-table",
    category: "container",
    icon: "data-table",
    widgetList: [],
    options: {
        name: "",
        label: "data-table",
        hidden: !1,
        // dataTableCustomClass: '',
        rowSpacing: 8,
        tableHeight: "300px",
        tableWidth: "100%",
        customClass: "",
        stripe: !0,
        showIndex: !1,
        showCheckBox: false,
        showPagination: !0,
        paginationAlign: "left",
        smallPagination: !1,
        showSummary: !1,
        border: !0,
        tableSize: "medium",
        autoColumnWidthDisabled: !1,
        tableColumns: [{
            columnId: 1,
            prop: "field1",
            label: "字段1",
            width: "160",
            show: !0,
            align: "left",
            propertyType: "TEXT",
            fixed: "",
            sortable: !0
        }, {
            columnId: 2,
            prop: "field2",
            label: "字段2",
            width: "160",
            show: !0,
            propertyType: "TEXT",
            align: "left",
            formatS: "d1"
        }, {
            columnId: 3,
            prop: "field3",
            label: "字段3",
            width: "160",
            show: !0,
            propertyType: "TEXT",
            align: "left",
            formatS: "d4"
        }, {
            columnId: 4,
            prop: "field4",
            label: "字段4",
            width: "160",
            show: !0,
            align: "left",
            propertyType: "TEXT",
            formatS: "d5"
        }, {
            columnId: 5,
            prop: "field5",
            label: "字段5",
            width: "160",
            show: !0,
            align: "right",
            propertyType: "TEXT",
            sortable: !0,
            formatS: "n1"
        }, {
            columnId: 6,
            prop: "field6",
            label: "字段6",
            width: "160",
            show: !0,
            align: "right",
            propertyType: "TEXT",
            sortable: !0,
            formatS: "n2"
        }],
        showButtonsColumn: !1,
        buttonsColumnFixed: "right",
        buttonsColumnTitle: "操作",
        buttonsColumnWidth: 120,
        operationButtons: [{
            name: "detail",
            label: "详情",
            type: "text",
            size: "small",
            round: !1,
            hidden: !0,
            disabled: !1
        }, {
            name: "edit",
            label: "编辑",
            type: "text",
            size: "small",
            round: !1,
            hidden: !1,
            disabled: !1
        }, {
            name: "delete",
            label: "删除",
            type: "text",
            size: "small",
            round: !1,
            hidden: !1,
            disabled: !1
        }],
        pagination: {
            currentPage: 1,
            pageSizes: [10, 15, 20, 30, 50, 100, 200],
            pageSize: 20,
            total: 366
        },
        dsEnabled: !1,
        dsName: "",
        dataSetName: "",
        treeDataEnabled: !1,
        rowKey: "id",
        childrenKey: "children",
        tableData: [{}],
        onCreated: "",
        onMounted: "",
        onPageSizeChange: "",
        onCurrentPageChange: "",
        onSortChange: "",
        onSelectionChange: "",
        onHideOperationButton: "",
        onDisableOperationButton: "",
        onGetOperationButtonLabel: "",
        onOperationButtonClick: "",
        onHeaderClick: "",
        onRowClick: "",
        onRowDoubleClick: "",
        onCellClick: "",
        onCellDoubleClick: "",
        onGetRowClassName: "",
        onGetSpanMethod: ""
    }
  },

  {
    type: 'tab',
    category: 'container',
    icon: 'tab',
    displayType: 'border-card',
    tabs: [],
    options: {
      name: '',
      hidden: false,
      // customClass: '', // 自定义css类名
    },
  },

  {
    type: 'grid-col',
    category: 'container',
    icon: 'grid-col',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      hidden: false,
      span: 12,
      offset: 0,
      push: 0,
      pull: 0,
      responsive: false, // 是否开启响应式布局
      md: 12,
      sm: 12,
      xs: 12,
      // customClass: '', // 自定义css类名
    },
  },

  {
    type: 'table-cell',
    category: 'container',
    icon: 'table-cell',
    internal: true,
    widgetList: [],
    merged: false,
    options: {
      name: '',
      cellWidth: '',
      cellHeight: '',
      colspan: 1,
      rowspan: 1,
      wordBreak: false, // 是否自动换行
      // customClass: '', // 自定义css类名
    },
  },
  {
    type: 'table-th-cell',
    category: 'container',
    icon: 'table-cell',
    internal: true,
    widgetList: [],
    merged: false,
    options: {
      name: '',
      label: '',
      cellWidth: '',
      cellHeight: '',
      colspan: 1,
      rowspan: 1,
      wordBreak: false, // 是否自动换行
      // // customClass: '', // 自定义css类名
      widgetConfig: {},
      tableCellType: 'static-text',
    },
  },

  {
    type: 'tab-pane',
    category: 'container',
    icon: 'tab-pane',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      hidden: false,
      active: false,
      disabled: false,
      // // customClass: '', // 自定义css类名
    },
  },
];

// 基础字段
export const basicFields = [
  {
    type: 'static-text',
    icon: 'static-text',
    formItemFlag: false,
    options: {
      name: '',
      columnWidth: '200px',
      hidden: false,
      textContent: '文本',
      textAlign: 'left',
      fontSize: '13px',
      preWrap: false, // 是否自动换行
      margin: '',
      // -------------------
      // customClass: '', // 自定义css类名
      // -------------------
      // onCreated: '',
      // onMounted: '',
    },
  },

  {
    type: 'input',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      displaySettings: {
        type: '',
        field: '',
        syncField: '',
      },
      labelAlign: '',
      contentAlign: '',
      type: 'text',
      defaultValue: '',
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      clearable: true,
      showPassword: false,
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      appendButton: false,
      appendButtonDisabled: false,
      buttonIcon: 'el-icon-search',
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onInput: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
      // onAppendButtonClick: '',
    },
  },

  {
    type: 'textarea',
    icon: 'textarea-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      rows: 3,
      defaultValue: '',
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onInput: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
    },
  },

  {
    type: 'number',
    icon: 'number-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      defaultValue: 0,
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      min: -100000000000,
      max: 100000000000,
      precision: 0,
      step: 1,
      controlsPosition: 'right',
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
    },
  },

  {
    type: 'radio',
    icon: 'radio-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      defaultValue: null,
      columnWidth: '200px',
      size: '',
      displayStyle: 'inline',
      buttonStyle: false,
      border: false,
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      optionType: 'static',
      optionItems: [
        { label: 'radio 1', value: 1 },
        { label: 'radio 2', value: 2 },
        { label: 'radio 3', value: 3 },
      ],
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onValidate: '',
    },
  },

  {
    type: 'checkbox',
    icon: 'checkbox-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      defaultValue: [],
      columnWidth: '200px',
      size: '',
      displayStyle: 'inline',
      buttonStyle: false,
      border: false,
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      optionType: 'static',
      optionItems: [
        { label: 'radio 1', value: 1 },
        { label: 'radio 2', value: 2 },
        { label: 'radio 3', value: 3 },
      ],
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onValidate: '',
    },
  },

  {
    type: 'select',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      defaultValue: '',
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      clearable: true,
      filterable: false,
      allowCreate: false,
      remote: false,
      automaticDropdown: false, // 自动下拉
      multiple: false,
      multipleLimit: 0,
      optionType: 'static',
      optionMedium: '',
      optionSyncUrl: '',
      optionLabel: 'label',
      optionValue: 'value',
      optionItems: [
        { label: 'select 1', value: 1 },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onRemoteQuery: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
    },
  },
  {
    type: 'tree-select',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      syncLabel: '',
      labelAlign: '',
      defaultValue: '',
      placeholder: '请选择',
      size: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      disabled: false,
      hidden: false,
      clearable: true,
      filterable: false,
      multiple: false,
      checkStrictly: false, // 可选择任意一级选项，默认不开启
      showAllLevels: true, // 显示完整路径
      optionType: 'static',
      optionMedium: '',
      optionSyncUrl: '/system/dept/list',
      optionLabel: 'deptName',
      optionValue: 'deptId',
      optionChildrenLabel: 'children',
      optionItems: [
        {
          deptName: 'select 1',
          deptId: 1,
          children: [{ deptName: 'child 1', deptId: 11 }],
        },
        { deptName: 'select 2', deptId: 2 },
        { deptName: 'select 3', deptId: 3 },
      ],
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
    },
  },

  {
    type: 'time',
    icon: 'time-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      defaultValue: null,
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      isCurrentTime: false,
      readonly: false,
      disabled: false,
      hidden: false,
      clearable: true,
      editable: false,
      format: 'HH:mm:ss', // 时间格式
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
    },
  },

  {
    type: 'time-range',
    icon: 'time-range-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      defaultValue: null,
      startPlaceholder: '',
      endPlaceholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      clearable: true,
      editable: false,
      format: 'HH:mm:ss', // 时间格式
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
    },
  },

  {
    type: 'date',
    icon: 'date-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      dateUnit: 'date',
      defaultValue: null,
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      isCurrentTime: false,
      readonly: false,
      disabled: false,
      hidden: false,
      clearable: true,
      editable: false,
      format: 'yyyy-MM-dd', // 日期显示格式
      valueFormat: 'yyyy-MM-dd', // 日期对象格式
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
    },
  },

  {
    type: 'date-range',
    icon: 'date-range-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      type: 'daterange',
      defaultValue: null,
      startPlaceholder: '',
      endPlaceholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      clearable: true,
      editable: false,
      format: 'yyyy-MM-dd', // 日期显示格式
      valueFormat: 'yyyy-MM-dd', // 日期对象格式
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
    },
  },

  {
    type: 'switch',
    icon: 'switch-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      defaultValue: null,
      columnWidth: '200px',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      switchWidth: 40,
      activeText: '',
      inactiveText: '',
      activeColor: null,
      inactiveColor: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onValidate: '',
    },
  },

  {
    type: 'rate',
    icon: 'rate-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      defaultValue: null,
      columnWidth: '200px',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      max: 5,
      lowThreshold: 2,
      highThreshold: 4,
      allowHalf: false,
      showText: false,
      showScore: false,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onValidate: '',
    },
  },

  {
    type: 'color',
    icon: 'color-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      defaultValue: null,
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onValidate: '',
    },
  },

  {
    type: 'slider',
    icon: 'slider-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      columnWidth: '200px',
      showStops: true,
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      validation: '',
      validationHint: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      min: 0,
      max: 100,
      step: 10,
      range: false,
      // vertical: false,
      height: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onValidate: '',
    },
  },

  {
    type: 'html-text',
    icon: 'html-text',
    formItemFlag: false,
    options: {
      name: '',
      columnWidth: '200px',
      hidden: false,
      htmlContent: '<b>html text</b>',
      // -------------------
      // customClass: '', // 自定义css类名
      // -------------------
      // onCreated: '',
      // onMounted: '',
    },
  },

  {
    type: 'button',
    icon: 'button',
    formItemFlag: false,
    options: {
      name: '',
      label: '',
      columnWidth: '200px',
      size: '',
      displayStyle: 'block',
      disabled: false,
      hidden: false,
      type: '',
      plain: false,
      round: false,
      circle: false,
      icon: null,
      // -------------------
      // customClass: '', // 自定义css类名
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onClick: '',
    },
  },

  {
    type: 'divider',
    icon: 'divider',
    formItemFlag: false,
    options: {
      name: '',
      label: '',
      columnWidth: '200px',
      direction: 'horizontal',
      contentPosition: 'center',
      hidden: false,
      // -------------------
      // customClass: '', // 自定义css类名
      // -------------------
      // onCreated: '',
      // onMounted: '',
    },
  },

  //
];

// 高级字段
export const advancedFields = [
  {
    type: 'sign',
    icon: 'el-icon-edit',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      permission: '',
      signType: SignOptionTypeValue.sign,
      signUrl: '',
      // uploadTip: '',
      // withCredentials: false,
      // multipleSelect: false,
      // showFileList: true,
      // limit: 3,
      // fileMaxSize: 5, // MB
      // fileTypes: ['jpg', 'jpeg', 'png'],
      // // headers: [],
      // // -------------------
      // // customClass: '', // 自定义css类名
      // labelIconClass: null,
      // labelIconPosition: 'rear',
      // labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onBeforeUpload: '',
      // onUploadSuccess: '',
      // onUploadError: '',
      // onFileRemove: '',
      // onValidate: '',
      // onFileChange: '',
    },
  },
  {
    type: 'picture-upload',
    icon: 'picture-upload-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      uploadURL: '/file/upload',
      uploadTip: '',
      withCredentials: false,
      multipleSelect: false,
      showFileList: true,
      limit: 1,
      fileMaxSize: 5, // MB
      fileTypes: ['jpg', 'jpeg', 'png'],
      // headers: [],
      permission: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onBeforeUpload: '',
      // onUploadSuccess: '',
      // onUploadError: '',
      // onFileRemove: '',
      // onValidate: '',
      // onFileChange: '',
    },
  },

  {
    type: 'file-upload',
    icon: 'file-upload-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      contentAlign: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      uploadURL: '/file/upload',
      uploadTip: '',
      withCredentials: false,
      multipleSelect: false,
      showFileList: true,
      limit: 1,
      fileMaxSize: 5, // MB
      fileTypes: ['doc', 'docx', 'xls', 'xlsx'],
      // headers: [],
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onBeforeUpload: '',
      // onUploadSuccess: '',
      // onUploadError: '',
      // onFileRemove: '',
      // onValidate: '',
      // onFileChange: '',
    },
  },

  {
    type: 'rich-editor',
    icon: 'rich-editor-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      placeholder: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      disabled: false,
      hidden: false,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onValidate: '',
    },
  },

  {
    type: 'cascader',
    icon: 'cascader-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      labelAlign: '',
      defaultValue: '',
      placeholder: '',
      size: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      disabled: false,
      hidden: false,
      clearable: true,
      filterable: false,
      multiple: false,
      checkStrictly: false, // 可选择任意一级选项，默认不开启
      showAllLevels: true, // 显示完整路径
      optionType: 'static',
      optionMedium: '',
      optionSyncUrl: '',
      optionLabel: 'label',
      optionValue: 'value',
      optionChildrenLabel: 'children',
      optionItems: [
        {
          label: 'select 1',
          value: 1,
          children: [{ label: 'child 1', value: 11 }],
        },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      // customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      // onCreated: '',
      // onMounted: '',
      // onChange: '',
      // onFocus: '',
      // onBlur: '',
      // onValidate: '',
    },
  },
];

// 自定义扩展字段
export const customFields = [];

export function addContainerWidgetSchema(containerSchema) {
  containers.push(containerSchema);
}

export function addBasicFieldSchema(fieldSchema) {
  basicFields.push(fieldSchema);
}

export function addAdvancedFieldSchema(fieldSchema) {
  advancedFields.push(fieldSchema);
}

export function addCustomWidgetSchema(widgetSchema) {
  customFields.push(widgetSchema);
}
