// mixins/property-editor-mixin.js

export const PropertyEditorMixin = {
  inject: ['i18nt', 'i18ntp'], // 注入国际化函数

  data() {
    return {
      // 数组操作相关的状态
      arrayIndex: -1,
      oldArrayValue: null,

      // 对象操作相关的状态
      objectKey: '',
      oldObjectValue: null
    }
  },

  methods: {
    /**
     * 添加数组元素
     * @param {Array} array 目标数组
     * @param {*} newElement 新元素
     * @param {number} index 插入位置，默认末尾
     */
    addArrayElement(array, newElement, index = -1) {
      if (!Array.isArray(array)) {
        array = []
      }

      if (index < 0) {
        array.push(newElement)
      } else {
        array.splice(index, 0, newElement)
      }

      // 触发Vue的响应式更新
      this.$set(this.optionModel, array, array)

      // 记录历史
      if (this.designer) {
        this.designer.emitHistoryChange()
      }
    },

    /**
     * 删除数组元素
     * @param {Array} array 目标数组
     * @param {number} index 删除位置
     */
    deleteArrayElement(array, index) {
      if (!Array.isArray(array) || index < 0 || index >= array.length) {
        return
      }

      // 保存旧值用于撤销
      this.arrayIndex = index
      this.oldArrayValue = array[index]

      array.splice(index, 1)

      // 触发Vue的响应式更新
      this.$set(this.optionModel, array, array)

      // 记录历史
      if (this.designer) {
        this.designer.emitHistoryChange()
      }
    },

    /**
     * 移动数组元素
     * @param {Array} array 目标数组
     * @param {number} oldIndex 原位置
     * @param {number} newIndex 新位置
     */
    moveArrayElement(array, oldIndex, newIndex) {
      if (!Array.isArray(array) ||
          oldIndex < 0 || oldIndex >= array.length ||
          newIndex < 0 || newIndex >= array.length) {
        return
      }

      const element = array.splice(oldIndex, 1)[0]
      array.splice(newIndex, 0, element)

      // 触发Vue的响应式更新
      this.$set(this.optionModel, array, array)

      // 记录历史
      if (this.designer) {
        this.designer.emitHistoryChange()
      }
    },

    /**
     * 克隆数组元素
     * @param {Array} array 目标数组
     * @param {number} index 克隆位置
     */
    cloneArrayElement(array, index) {
      if (!Array.isArray(array) || index < 0 || index >= array.length) {
        return
      }

      const newElement = JSON.parse(JSON.stringify(array[index]))
      array.splice(index + 1, 0, newElement)

      // 触发Vue的响应式更新
      this.$set(this.optionModel, array, array)

      // 记录历史
      if (this.designer) {
        this.designer.emitHistoryChange()
      }
    },

    /**
     * 设置对象属性
     * @param {Object} obj 目标对象
     * @param {string} key 属性键
     * @param {*} value 属性值
     */
    setObjectProperty(obj, key, value) {
      if (!obj || typeof obj !== 'object') {
        return
      }

      // 保存旧值用于撤销
      this.objectKey = key
      this.oldObjectValue = obj[key]

      // 触发Vue的响应式更新
      this.$set(obj, key, value)

      // 记录历史
      if (this.designer) {
        this.designer.emitHistoryChange()
      }
    },

    /**
     * 删除对象属性
     * @param {Object} obj 目标对象
     * @param {string} key 属性键
     */
    deleteObjectProperty(obj, key) {
      if (!obj || typeof obj !== 'object' || !(key in obj)) {
        return
      }

      // 保存旧值用于撤销
      this.objectKey = key
      this.oldObjectValue = obj[key]

      this.$delete(obj, key)

      // 记录历史
      if (this.designer) {
        this.designer.emitHistoryChange()
      }
    },

    /**
     * 检查数组长度限制
     * @param {number} length 当前长度
     * @param {number} increment 增量
     * @returns {boolean} 是否超出限制
     */
    checkArrayLength(length, increment) {
      // 检查数组长度是否会超出最大限制(4294967295)
      return length + increment <= 4294967295
    }
  }
}