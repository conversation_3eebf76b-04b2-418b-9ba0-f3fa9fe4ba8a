

import {
  listRole,
} from '@/api/system/role';

export default {
  methods: {
    // 初始化表单拖拽配所需要的数据
    async loadFieldOptionData() {
      // 拉取所有角色
      const roleList = await this.getListRole()
      this.designer.globalOption = {
        roleList: roleList?.rows || []
      }
    },

    async getListRole() {
      const parames= {
        pageNum: 1,
        pageSize: 9999,
      }
      return await listRole(parames)
    },
  },
};
