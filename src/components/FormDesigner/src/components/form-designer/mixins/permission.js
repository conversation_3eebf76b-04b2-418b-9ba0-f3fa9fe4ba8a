import store from '@/store';

export default {
  computed: {
    permission() {
      return this.field.options.permission
    },
    hasPermission() {
      return this.checkPermission()
    }
  },
  methods: {
    checkPermission() {
      if (!this.permission) return true
      const roles = store.getters && store.getters.roles;
      const super_admin = 'admin';
      const roleFlag = [this.permission]

      const hasRole = roles.some(role => {
        return super_admin === role || roleFlag.includes(role);
      });

      return hasRole
    }
  }
}