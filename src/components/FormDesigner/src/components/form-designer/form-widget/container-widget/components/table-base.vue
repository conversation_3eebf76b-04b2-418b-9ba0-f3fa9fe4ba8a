<template>
  <table class="table-layout">
    <tbody>
      <tr v-for="(row, rowIdx) in widget.rows" :key="row.id">
        <template v-for="(colWidget, colIdx) in row.cols">
          <table-cell-widget
            v-if="!colWidget.merged"
            :key="colWidget.id"
            :widget="colWidget"
            :designer="designer"
            :parent-list="widget.cols"
            :row-index="rowIdx"
            :row-length="widget.rows.length"
            :col-index="colIdx"
            :col-length="row.cols.length"
            :col-array="row.cols"
            :row-array="widget.rows"
            :parent-widget="widget"
          />
        </template>
      </tr>
    </tbody>
  </table>
</template>
<script>
import TableCellWidget from '@@/components/form-designer/form-widget/container-widget/table-cell-widget';
export default {
  props: {
    widget: {
      type: Object,
      default: () => {}
    },
    designer: {
      type: Object,
      default: () => {}
    },
  },
  components: {
    TableCellWidget
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style scoped lang="scss">
table.table-layout {
  width: 100%;
  text-align: center;
  // border: 1px solid #c8ebfb;
  border-collapse: collapse;
  table-layout: fixed;
  margin-bottom: 12px;

  ::v-deep td {
    height: 48px;
    border: 1px dashed #c9c9c9;
    padding: 6px;
    display: table-cell;
  }

  .form-widget-list {
    border: 1px dashed #c9c9c9;
    min-height: 36px;
  }
}
</style>
