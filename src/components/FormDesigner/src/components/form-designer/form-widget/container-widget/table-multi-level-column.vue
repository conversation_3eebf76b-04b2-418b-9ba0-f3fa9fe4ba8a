<!-- TableMultiLevelColumn.vue -->
<template>
  <!-- 如果是多级表头 -->
  <el-table-column
    v-if="columnSchema.headerFlag && columnSchema.children && columnSchema.children.length > 0"
    :label="columnSchema.label"
    :align="columnSchema.align || 'center'"
  >
    <template v-for="child in columnSchema.children">
      <table-multi-level-column
        :key="`${child.columnId}-${columnIndex}`"
        :column-schema="child"
        :table-options="tableOptions"
      />
    </template>
  </el-table-column>

  <!-- 如果是普通列且需要显示 -->
  <el-table-column
    v-else-if="!columnSchema.headerFlag && columnSchema.show !== false && !columnSchema.children"
    :key="`${columnSchema.columnId}-${columnIndex}`"
    :prop="columnSchema.prop"
    :label="columnSchema.label"
    :sortable="columnSchema.sortable"
    :fixed="!!columnSchema.fixed && columnSchema.fixed"
    :align="columnSchema.align || 'center'"
    :formatter="columnSchema.editable ? undefined : formatterValue"
    :format="columnSchema.format"
    :min-width="colMinWidth"
    :width="colWidth"
  >
    <template #default="scope">
      <el-select
        :disabled="isPreview"
        v-if="columnSchema.propertyType === PropertyType.ENUM"
        v-model="scope.row[columnSchema.prop]"
        placeholder="请选择"
        size="small"
      >
        <el-option
          v-for="item in columnSchema.dataSpecs.enmu"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-switch
        v-if="columnSchema.propertyType === PropertyType.BOOL"
        :disabled="isPreview"
        v-model="scope.row[columnSchema.prop]"
        size="small"
        active-text="是"
        inactive-text="否"
      />
      <el-input-number
        v-if="['INT', 'FLOAT', 'DOUBLE'].includes(columnSchema.propertyType)"
        :disabled="isPreview"
        v-model="scope.row[columnSchema.prop]"
        size="small"
        :min="Number(columnSchema.dataSpecs.min) || 0"
        :max="Number(columnSchema.dataSpecs.max) || 0"
      />
      <el-date-picker
        v-if="['DATE'].includes(columnSchema.propertyType)"
        :disabled="isPreview"
        v-model="scope.row[columnSchema.prop]"
        :value-format="columnSchema.valueFormat || 'yyyy-MM-dd'"
        placeholder="请选择"
        size="small"
      />
      <el-input
        v-if="columnSchema.propertyType === PropertyType.TEXT"
        :disabled="isPreview"
        v-model="scope.row[columnSchema.prop]"
        placeholder="请输入"
        size="small"
      />
    </template>
  </el-table-column>
</template>

<script>
import { dateFormatters, numberFormatters } from '@@/utils/formatters';
import { PropertyType } from '@@/constants/iot';
export default {
  name: 'TableMultiLevelColumn',

  props: {
    // 列配置对象
    columnSchema: {
      type: Object,
      required: true,
    },
    // 表格配置对象
    tableOptions: {
      type: Object,
      required: true,
    },
    columnIndex: {
      type: Number,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      PropertyType,
    };
  },

  computed: {
    // 最小列宽
    colMinWidth() {
      return this.tableOptions.autoColumnWidthDisabled === true
        ? undefined
        : this.columnSchema.width;
    },

    // 列宽
    colWidth() {
      return this.tableOptions.autoColumnWidthDisabled === true
        ? this.columnSchema.width
        : undefined;
    },
  },

  methods: {
    // 格式化列值
    formatterValue(row, column, value) {
      // 空值处理
      if (!value) return '';

      // 如果有格式化配置且需要显示
      if (column.formatS && column.show) {
        switch (column.formatS) {
          // 日期格式化
          case 'd1':
            return dateFormatters.d1(value);
          case 'd2':
            return dateFormatters.d2(value);
          case 'd3':
            return dateFormatters.d3(value);
          case 'd4':
            return dateFormatters.d4(value);
          case 'd5':
            return dateFormatters.d5(value);

          // 数字格式化
          case 'n1':
            return numberFormatters.n1(value);
          case 'n2':
            return numberFormatters.n2(value);
          case 'n3':
            return numberFormatters.n3(value);
          case 'n4':
            return numberFormatters.n4(value);
          case 'n5':
            return numberFormatters.n5(value);
          case 'n6':
            return numberFormatters.n6(value);
          case 'n7':
            return numberFormatters.n7(value);
        }
      }

      // 默认返回原值
      return value;
    },

    handleInputChange(row, prop, value) {
      // 触发input值变化事件
      this.$emit('cell-value-change', {
        row,
        prop,
        value,
      });
    },
  },
};
</script>
