<!--
/**
 * author: vformAdmin
 * email: <EMAIL>
 * website: https://www.vform666.com
 * date: 2021.08.18
 * remark: 如果要分发VForm源码，需在本文件顶部保留此文件头信息！！
 */
-->

<template>
  <container-wrapper
    :designer="designer"
    :widget="widget"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <div
      :key="widget.id"
      class="table-container"
      :class="[selected ? 'selected' : '', customClass]"
      @click.stop="selectWidget(widget)"
    >
      <TableBase :widget="widget" :designer="designer"></TableBase>
    </div>
  </container-wrapper>
</template>

<script>
import i18n from '@@/utils/i18n';
import containerMixin from '@@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@@/components/form-designer/form-widget/container-widget/container-wrapper';
import TableBase from './components/table-base';
import refMixinDesign from '@@/components/form-designer/refMixinDesign';
export default {
  name: 'TableWidget',
  componentName: 'ContainerWidget',
  components: {
    ContainerWrapper,
    TableBase,
  },
  mixins: [i18n, containerMixin, refMixinDesign],
  inject: ['refList'],
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  watch: {
    //
  },
  created() {
    this.initRefList();
  },
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
div.table-container {
  padding: 6px;
  border: 1px dashed #c9c9c9;
  box-sizing: border-box;
}

.table-container.selected {
  outline: 2px solid $--color-primary !important;
}
</style>
