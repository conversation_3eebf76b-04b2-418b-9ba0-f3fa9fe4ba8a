<template>
  <container-wrapper
    :designer="designer"
    :widget="widget"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <div
      :key="widget.id"
      class="table-container"
      :class="[selected ? 'selected' : '', customClass]"
      @click.stop="selectWidget(widget)"
    >
      <TableBase :widget="widget" :designer="designer"></TableBase>
    </div>
  </container-wrapper>
</template>

<script>
import i18n from '@@/utils/i18n';
import containerMixin from '@@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@@/components/form-designer/form-widget/container-widget/container-wrapper';
import refMixinDesign from '@@/components/form-designer/refMixinDesign';

import TableBase from './components/table-base';

import { generateId } from '@@/utils/util';

export default {
  name: 'InspectionItemsTableWidget',
  componentName: 'ContainerWidget',
  components: {
    ContainerWrapper,
    TableBase,
  },
  mixins: [i18n, containerMixin, refMixinDesign],
  inject: ['refList'],
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    inspectionItems: Array,
  },
  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  watch: {
    inspectionItems: {
      handler: function (val) {
        if (val?.length > 0) {
          // this.handlerTableRow();
        }
      },
      immediate: true,
    },
  },
  created() {
    this.initRefList();
  },
  mounted() {},
  methods: {
    getStaticTextField(textContent) {
      return {
        type: 'static-text',
        icon: 'static-text',
        formItemFlag: false,
        options: {
          name: `statictext${generateId()}`,
          columnWidth: '200px',
          hidden: false,
          textContent: textContent,
          textAlign: 'center',
          fontSize: '13px',
          preWrap: false,
          label: 'static-text',
        },
        id: `statictext${generateId()}`,
      };
    },
    getTextAreaField(colsItem, itemId) {
      const { label, name } = colsItem || {}
      const id =  `${name}-${generateId()}`
      return {
        type: 'textarea',
        icon: 'textarea-field',
        formItemFlag: true,
        options: {
          itemId: itemId, // 存储 ItemId
          name: id,
          label,
          labelAlign: '',
          rows: 3,
          defaultValue: '',
          placeholder: '请输入',
          columnWidth: '200px',
          size: '',
          labelWidth: null,
          labelHidden: true,
          readonly: false,
          disabled: false,
          hidden: false,
          required: false,
          requiredHint: '',
          validation: '',
          validationHint: '',
          permission: '',
          labelIconClass: null,
          labelIconPosition: 'rear',
          labelTooltip: null,
          minLength: null,
          maxLength: null,
          showWordLimit: false,
        },
        id,
      };
    },

    handlerTableRow() {
      const newTable = [];
      this.inspectionItems.map((rows, rowsIndex) => {
        const rowsObj = {
          id: `table-row-${generateId()}`,
          merged: false,
          cols: [],
        };
        let itemId = 0
        rows.map((cols, colsIndex) => {
          // 首列 Id 不展示
          if (colsIndex === 0) {
            itemId = cols.value
            return
          }
          const id = `table-cell-${generateId()}`;
          const closObj = {
            id,
            type: 'table-cell',
            category: 'container',
            icon: 'table-cell',
            internal: true,
            merged: cols.merged || false,
            options: {
              name: id,
              cellWidth: '',
              cellHeight: '',
              colspan: cols.colspan || 1,
              rowspan: cols.rowspan || 1,
              wordBreak: false,
              customClass: '',
            },
            widgetList: [
              ['selfTestRecord', 'testResult'].includes(cols.name) && rowsIndex !== 0
                ? this.getTextAreaField(cols, itemId)
                : this.getStaticTextField(cols.value),
            ],
          };
          rowsObj.cols.push(closObj);
        });

        newTable.push(rowsObj);
      });

      console.log(
        'object :>> this.designer.widgetList',
        this.designer.widgetList[this.indexOfParentList],
      );
      console.log('newTable :>>123', newTable);

      this.designer.widgetList[this.indexOfParentList].rows = newTable;
    },
  },
};
</script>

<style lang="scss" scoped>
div.table-container {
  padding: 6px;
  border: 1px dashed #c9c9c9;
  box-sizing: border-box;
}

.table-container.selected {
  outline: 2px solid $--color-primary !important;
}
</style>
