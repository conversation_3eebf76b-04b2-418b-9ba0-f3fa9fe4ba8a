<template>
  <container-wrapper
    :designer="designer"
    :widget="widget"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <el-row
      :key="widget.id"
      :gutter="widget.options.gutter"
      class="grid-container"
      :class="[selected ? 'selected' : '', customClass]"
      @click.native.stop="selectWidget(widget)"
    >
      <draggable
        :list="widget.widgetList"
        v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 200 }"
        handle=".drag-handler"
        :move="checkContainerMove"
        @end="evt => onGridDragEnd(evt, widget.widgetList)"
        @add="evt => onGridDragAdd(evt, widget.widgetList)"
        @update="onGridDragUpdate"
      >
        <transition-group name="fade" tag="div" class="form-widget-list">
          <template v-for="(subWidget, swIdx) in widget.widgetList">
            <template v-if="'container' === subWidget.category">
              <component
                :is="subWidget.type + '-widget'"
                :key="subWidget.id"
                :widget="subWidget"
                :designer="designer"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              />
            </template>
            <template v-else>
              <component
                :is="subWidget.type + '-widget'"
                :key="subWidget.id"
                :field="subWidget"
                :designer="designer"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
                :design-state="true"
              />
            </template>
          </template>
        </transition-group>
      </draggable>

      <div
        v-if="designer.selectedId === widget.id && widget.type === 'grid-col'"
        class="grid-col-action"
      >
        <i
          class="el-icon-back"
          :title="i18nt('designer.hint.selectParentWidget')"
          @click.stop="selectParentWidget(widget)"
        />
        <i
          v-if="!!parentList && parentList.length > 1"
          class="el-icon-top"
          :title="i18nt('designer.hint.moveUpWidget')"
          @click.stop="moveUpWidget()"
        />
        <i
          v-if="!!parentList && parentList.length > 1"
          class="el-icon-bottom"
          :title="i18nt('designer.hint.moveDownWidget')"
          @click.stop="moveDownWidget()"
        />
        <i
          class="el-icon-copy-document"
          :title="i18nt('designer.hint.cloneWidget')"
          @click.stop="cloneGridCol(widget)"
        />
        <i
          class="el-icon-delete"
          :title="i18nt('designer.hint.remove')"
          @click.stop="removeWidget"
        />
      </div>

      <div
        v-if="designer.selectedId === widget.id && widget.type === 'grid-col'"
        class="grid-col-handler"
      >
        <i>{{ i18nt('designer.widgetLabel.' + widget.type) }}</i>
      </div>
    </el-row>
  </container-wrapper>
</template>

<script>
import i18n from '@@/utils/i18n';
import GridColWidget from '@@/components/form-designer/form-widget/container-widget/grid-col-widget';
import containerMixin from '@@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@@/components/form-designer/form-widget/container-widget/container-wrapper';
import refMixinDesign from '@@/components/form-designer/refMixinDesign';
import FieldComponents from '@@/components/form-designer/form-widget/field-widget/index';
import Draggable from 'vuedraggable';;

export default {
  name: 'StandardTableWidget',
  components: {
    ContainerWrapper,
    GridColWidget,
    Draggable,
    ...FieldComponents
  },
  mixins: [i18n, containerMixin, refMixinDesign],
  inject: ['refList'],
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  watch: {
    //
  },
  created() {
    this.initRefList();
  },
  mounted() {
    //
  },
  methods: {
    checkContainerMove(evt) {
      return this.designer.checkWidgetMove(evt);
    },
    onGridDragEnd(evt, subList) {
      //
    },

    onGridDragAdd(evt, subList) {
      const newIndex = evt.newIndex;
      if (subList[newIndex]) {
        this.designer.setSelected(subList[newIndex]);
      }

      this.designer.emitHistoryChange();
      this.designer.emitEvent('field-selected', this.widget);
    },

    onGridDragUpdate() {
      this.designer.emitHistoryChange();
    },
    onTableDragUpdate() {
      this.designer.emitHistoryChange();
    },
  },
};
</script>

<style lang="scss" scoped>
.el-row.grid-container {
  min-height: 50px;
  //line-height: 48px;
  //padding: 6px;
  outline: 1px dashed #c9c9c9;

  &:hover {
    outline: 1px solid #409eff;
  }

  .form-widget-list {
    min-height: 28px;
  }
}

.grid-container.selected,
.grid-cell.selected {
  outline: 2px solid $--color-primary !important;
}

.grid-cell {
  min-height: 38px;
  //margin: 6px 0;  /* 设置了margin，栅格列的offset、push、pull会失效！！ */
  padding: 3px;
  outline: 1px dashed #c9c9c9;
  position: relative;

  &:hover {
    outline: 1px solid #409eff;
  }

  .form-widget-list {
    min-height: 28px;
  }

  .grid-col-action {
    position: absolute;
    bottom: 0;
    right: -2px;
    height: 28px;
    line-height: 28px;
    background: $--color-primary;
    z-index: 999;

    i {
      font-size: 14px;
      color: #fff;
      margin: 0 5px;
      cursor: pointer;
    }
  }

  .grid-col-handler {
    position: absolute;
    top: -2px;
    left: -2px;
    height: 22px;
    line-height: 22px;
    background: $--color-primary;
    z-index: 9;

    i {
      font-size: 14px;
      font-style: normal;
      color: #fff;
      margin: 4px;
      cursor: default;
    }
  }
}
</style>
