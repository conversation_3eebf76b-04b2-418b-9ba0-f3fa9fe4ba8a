<template>
  <td
    :class="[selected ? 'selected' : '', customClass]"
    :colspan="widget.options.colspan || 1"
    :rowspan="widget.options.rowspan || 1"
    :style="{
      width: widget.options.cellWidth + '!important' || '',
      height: widget.options.cellHeight + '!important' || '',
      'word-break': !!widget.options.wordBreak ? 'break-all' : 'normal',
    }"
    class="table-cell"
    @click.stop="selectWidget(widget)"
  >
    <draggable
      :list="widget.widgetList"
      :move="checkContainerMove"
      class="draggable-div"
      handle=".drag-handler"
      v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 200 }"
      @add="evt => onTableDragAdd(evt, widget.widgetList)"
      @end="evt => onTableDragEnd(evt, widget.widgetList)"
      @update="onTableDragUpdate"
    >
      <transition-group class="form-widget-list" name="fade" tag="div">
        <template v-for="(subWidget, swIdx) in widget.widgetList">
          <template v-if="'container' === subWidget.category">
            <component
              :is="subWidget.type + '-widget'"
              :key="subWidget.id"
              :designer="designer"
              :index-of-parent-list="swIdx"
              :parent-list="widget.widgetList"
              :parent-widget="widget"
              :widget="subWidget"
            />
          </template>
          <template v-else>
            <component
              :is="subWidget.type + '-widget'"
              :key="subWidget.id"
              :design-state="true"
              :designer="designer"
              :field="subWidget"
              :index-of-parent-list="swIdx"
              :parent-list="widget.widgetList"
              :parent-widget="widget"
            />
          </template>
        </template>
      </transition-group>
    </draggable>

    <div v-if="designer.selectedId === widget.id && widget.type === 'table-cell'" class="table-cell-action">
      <i :title="i18nt('designer.hint.selectParentWidget')" class="el-icon-back" @click.stop="selectParentWidget()" />
      <el-dropdown
        size="small"
        trigger="click"
        :hide-on-click="!flag"
        @visible-change="handleVisibleChange"
        @command="handleTableCellCommand">
        <i :title="i18nt('designer.hint.cellSetting')" class="el-icon-menu" />
        <el-dropdown-menu slot="dropdown">
          <template v-if="!isNestTable">
            <el-dropdown-item command="insertLeftCol" >
              {{
                i18nt('designer.setting.insertColumnToLeft')
              }}
            </el-dropdown-item>
            <el-dropdown-item command="insertRightCol">
              {{
                i18nt('designer.setting.insertColumnToRight')
              }}
            </el-dropdown-item>
            <el-dropdown-item command="insertAboveRow">
              {{
                i18nt('designer.setting.insertRowAbove')
              }}
            </el-dropdown-item>
            <el-dropdown-item command="insertBelowRow">
              {{
                i18nt('designer.setting.insertRowBelow')
              }}
            </el-dropdown-item>
            <el-dropdown-item command="copyInsertBelowRow">
              {{
                i18nt('designer.setting.copyInsertRowBelow')
              }}
            </el-dropdown-item>
          </template>
          <template v-else>
            <el-dropdown-item command="insertAboveCopyRow">
              {{
                i18nt('designer.setting.insertRowAbove')
              }}
            </el-dropdown-item>
            <el-dropdown-item command="insertBelowCopyRow">
              {{
                i18nt('designer.setting.insertRowBelow')
              }}
            </el-dropdown-item>
          </template>
          <el-dropdown-item :disabled="mergeRightColDisabled" command="mergeRightCol" divided v-if="!isNestTable">
            <span>合并右侧</span>
            <el-input-number
              class="dropdownInput"
              v-model="mergeRightColNum"
              :controls="false"
              size="small"
              :min="1"
              :max="10"
              @focus="handleFocus"
              @blur="handleBlur" ></el-input-number>
            <span >个单元格</span>
          </el-dropdown-item>
          <el-dropdown-item :disabled="mergeLeftColDisabled" command="mergeLeftCol" v-if="!isNestTable">
            <span>合并左侧</span>
            <el-input-number
              class="dropdownInput"
              v-model="mergeLeftColNum"
              :controls="false"
              size="small"
              :min="1"
              :max="10"
              @focus="handleFocus"
              @blur="handleBlur" ></el-input-number>
            <span >个单元格</span>
          </el-dropdown-item>
          <el-dropdown-item :disabled="mergeBelowRowDisabled" command="mergeBelowRow" :divided="isNestTable">
            <span>合并下方</span>
            <el-input-number
              class="dropdownInput"
              v-model="mergeBelowRowNum"
              :controls="false"
              size="small"
              :min="1"
              :max="10"
              @focus="handleFocus"
              @blur="handleBlur" ></el-input-number>
            <span >个单元格</span>
          </el-dropdown-item>
          <el-dropdown-item :disabled="mergeAboveRowDisabled" command="mergeAboveRow">
            <span>合并上方</span>
            <el-input-number
              class="dropdownInput"
              v-model="mergeAboveRowNum"
              :controls="false"
              size="small"
              :min="1"
              :max="10"
              @focus="handleFocus"
              @blur="handleBlur" ></el-input-number>
            <span >个单元格</span>
          </el-dropdown-item>
          <el-dropdown-item :disabled="mergeWholeRowDisabled" command="mergeWholeRow" v-if="!isNestTable" divided>
            {{
              i18nt('designer.setting.mergeEntireRow')
            }}
          </el-dropdown-item>
          <el-dropdown-item :disabled="mergeWholeColDisabled" command="mergeWholeCol" v-if="!isNestTable">
            {{
              i18nt('designer.setting.mergeEntireColumn')
            }}
          </el-dropdown-item>
          <el-dropdown-item :disabled="undoMergeRowDisabled" command="undoMergeRow" divided>
            {{
              i18nt('designer.setting.undoMergeRow')
            }}
          </el-dropdown-item>
          <el-dropdown-item :disabled="undoMergeColDisabled" command="undoMergeCol">
            {{
              i18nt('designer.setting.undoMergeCol')
            }}
          </el-dropdown-item>
          <el-dropdown-item :disabled="deleteWholeColDisabled" command="deleteWholeCol" divided>
            {{
              i18nt('designer.setting.deleteEntireCol')
            }}
          </el-dropdown-item>
          <el-dropdown-item :disabled="deleteWholeRowDisabled" command="deleteWholeRow">
            {{
              i18nt('designer.setting.deleteEntireRow')
            }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <div v-if="designer.selectedId === widget.id && widget.type === 'table-cell'" class="table-cell-handler">
      <i>{{ i18nt('designer.widgetLabel.' + widget.type) }}</i>
    </div>
  </td>
</template>

<script>
import Draggable from 'vuedraggable';
import i18n from '@@/utils/i18n';
import FieldComponents from '@@/components/form-designer/form-widget/field-widget/index';
import refMixinDesign from '@@/components/form-designer/refMixinDesign';
import tableCellDropdownMixin from '@@/components/form-designer/form-widget/container-widget/mixins/tableCellDropdownMixin';

export default {
  name: 'TableCellWidget',
  componentName: 'TableCellWidget',
  components: {
    Draggable,
    ...FieldComponents,
  },
  mixins: [i18n, refMixinDesign, tableCellDropdownMixin],
  inject: ['refList'],
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,

    rowIndex: Number,
    colIndex: Number,
    rowLength: Number,
    colLength: Number,
    colArray: Array,
    rowArray: Array,

    designer: Object,
  },
  computed: {
    //  是否为嵌套表格
    isNestTable() {
      return this.parentWidget.type === 'nestTable'
    },
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },

    mergeLeftColDisabled() {
      return (
        this.colIndex <= 0 ||
        this.colArray[this.colIndex - 1].options.rowspan !== this.widget.options.rowspan
      );
    },

    mergeRightColDisabled() {
      const rightColIndex = this.colIndex + this.widget.options.colspan;
      return (
        this.colIndex >= this.colLength - 1 ||
        rightColIndex > this.colLength - 1 ||
        this.colArray[rightColIndex].options.rowspan !== this.widget.options.rowspan
      );
    },

    mergeWholeRowDisabled() {
      return this.colLength <= 1 || this.colLength === this.widget.options.colspan;
    },

    // 合并上方单元格
    mergeAboveRowDisabled() {
      return (
        this.rowIndex <= 0 ||
        this.rowArray[this.rowIndex - 1].cols[this.colIndex].type === 'table-th-cell' ||
        this.rowArray[this.rowIndex - 1].cols[this.colIndex].options.colspan !==
        this.widget.options.colspan
      );

      // return this.rowIndex <= 0
      // return (this.rowIndex <= 0) || (this.widget.options.colspan !== this.rowArray) //TODO
    },

    mergeBelowRowDisabled() {
      const belowRowIndex = this.rowIndex + this.widget.options.rowspan;
      return (
        this.rowIndex >= this.rowLength - 1 ||
        belowRowIndex > this.rowLength - 1 ||
        this.rowArray[belowRowIndex].cols[this.colIndex].options.colspan !==
        this.widget.options.colspan
      );
    },

    mergeWholeColDisabled() {
      return this.rowLength <= 1 || this.rowLength === this.widget.options.rowspan;
    },

    undoMergeColDisabled() {
      return this.widget.merged || this.widget.options.colspan <= 1;
    },

    undoMergeRowDisabled() {
      return this.widget.merged || this.widget.options.rowspan <= 1;
    },

    deleteWholeColDisabled() {
      // return this.colLength === 1
      return this.colLength === 1 || this.widget.options.colspan === this.colLength;
    },

    deleteWholeRowDisabled() {
      return this.rowLength === 1 || this.widget.options.rowspan === this.rowLength;
    },
  },
  watch: {
    //
  },
  created() {
    this.initRefList();
  },
  methods: {
    selectWidget(widget) {
      this.designer.setSelected(widget);
    },

    checkContainerMove(evt) {
      return this.designer.checkWidgetMove(evt);
    },

    onTableDragEnd(obj, subList) {
      //
    },

    onTableDragAdd(evt, subList) {
      // 重复代码，可合并
      const newIndex = evt.newIndex;
      if (subList[newIndex]) {
        this.designer.setSelected(subList[newIndex]);
      }

      this.designer.emitHistoryChange();
      this.designer.emitEvent('field-selected', this.widget);
    },

    onTableDragUpdate() {
      this.designer.emitHistoryChange();
    },

    selectParentWidget() {
      if (this.parentWidget) {
        this.designer.setSelected(this.parentWidget);
      } else {
        this.designer.clearSelected();
      }
    },

    handleTableCellCommand(command) {
      if (this.flag) return
      switch (command) {
        case 'insertLeftCol':
          this.insertLeftCol();
          break;
        case 'insertRightCol':
          this.insertRightCol();
          break;
        case 'insertAboveRow':
          this.insertAboveRow();
          break;
        case 'insertBelowRow':
          this.insertBelowRow();
          break;
        case 'copyInsertBelowRow':
          this.copyInsertBelowRow();
          break;
        case 'insertAboveCopyRow':
          this.copyInsertBelowRow(true);
          break;
        case 'insertBelowCopyRow':
          this.copyInsertBelowRow();
          break;
        case 'mergeLeftCol':
          console.log('object :>> this.mergeLeftColNum', this.mergeLeftColNum);
          this.mergeLeftCol(this.mergeLeftColNum);
          break;
        case 'mergeRightCol':
          this.mergeRightCol(this.mergeRightColNum);
          break;
        case 'mergeWholeCol':
          this.mergeWholeCol();
          break;
        case 'mergeWholeCol':
          this.mergeWholeCol();
          break;
        case 'mergeAboveRow':
          this.mergeAboveRow(this.mergeAboveRowNum);
          break;
        case 'mergeBelowRow':
          this.mergeBelowRow(this.mergeBelowRowNum);
          break;
        case 'mergeWholeRow':
          this.mergeWholeRow();
          break;
        case 'undoMergeCol':
          this.undoMergeCol();
          break;
        case 'undoMergeRow':
          this.undoMergeRow();
          break;
        case 'deleteWholeCol':
          this.deleteWholeCol();
          break;
        case 'deleteWholeRow':
          this.deleteWholeRow();
          break;
        default:
          break;
      }
    },

    insertLeftCol() {
      this.designer.insertTableCol(this.parentWidget, this.colIndex, this.rowIndex, true);
    },

    insertRightCol() {
      this.designer.insertTableCol(this.parentWidget, this.colIndex, this.rowIndex, false);
    },

    insertAboveRow() {
      this.designer.insertTableRow(
        this.parentWidget,
        this.rowIndex,
        this.rowIndex,
        this.colIndex,
        true,
      );
    },

    insertBelowRow() {
      this.designer.insertTableRow(
        this.parentWidget,
        this.rowIndex,
        this.rowIndex,
        this.colIndex,
        false,
      );
    },
    copyInsertBelowRow(isAbove) {
      this.designer.copyInsertTableCol(
        this.parentWidget,
        this.rowIndex,
        this.rowIndex,
        this.colIndex,
        isAbove,
      );
    },

    // TODO: 左侧多次合并单元格有问题，之后需排查
    async mergeLeftCol(index = 1) {
      // this.designer.mergeTableColumn(this.colArray, this.colIndex, true)
      let i = 0;
      while (i < index) {
        this.designer.mergeTableCol(
          this.rowArray,
          this.colArray,
          this.rowIndex,
          this.colIndex,
          true,
          this.widget,
        );
        i++
      }
    },

    mergeRightCol(index = 1) {
      // this.designer.mergeTableColumn(this.colArray, this.colIndex, false)
      let i = 0;
      while (i < index) {
        this.designer.mergeTableCol(
          this.rowArray,
          this.colArray,
          this.rowIndex,
          this.colIndex,
          false,
          this.widget,
        );
        i++
      }
    },

    mergeWholeRow() {
      this.designer.mergeTableWholeRow(this.rowArray, this.colArray, this.rowIndex, this.colIndex);
    },

    mergeAboveRow(index = 1) {
      let i = 0;
      while (i < index) {
        this.designer.mergeTableRow(this.rowArray, this.rowIndex, this.colIndex, true, this.widget);
        i++
      }
     
    },

    mergeBelowRow(index = 1) {
      let i = 0;
      while (i < index) {
        this.designer.mergeTableRow(this.rowArray, this.rowIndex, this.colIndex, false, this.widget);
        i++
      }
    },

    mergeWholeCol() {
      this.designer.mergeTableWholeCol(this.rowArray, this.colArray, this.rowIndex, this.colIndex);
    },

    undoMergeCol() {
      this.designer.undoMergeTableCol(
        this.rowArray,
        this.rowIndex,
        this.colIndex,
        this.widget.options.colspan,
        this.widget.options.rowspan,
      );
    },

    undoMergeRow() {
      this.designer.undoMergeTableRow(
        this.rowArray,
        this.rowIndex,
        this.colIndex,
        this.widget.options.colspan,
        this.widget.options.rowspan,
      );
    },

    deleteWholeCol() {
      this.designer.deleteTableWholeCol(this.rowArray, this.colIndex);
    },

    deleteWholeRow() {
      this.designer.deleteTableWholeRow(this.rowArray, this.rowIndex);
    },
  },
};
</script>

<style lang="scss" scoped>
.table-cell {
  //padding: 3px;
  border: 1px dashed #c9c9c9;
  display: table-cell;
  position: relative;

  .draggable-div {
    position: relative;
    height: 100%;

    ::v-deep .el-form-item {
      margin-bottom: 0;
    }
  }

  .form-widget-list {
    border: 1px dashed #c9c9c9;
    margin: 3px;

    //min-height: 36px;
    height: 100%;

    /*position: absolute;*/
    /*top: 0;*/
    /*right: 0;*/
    /*bottom: 0;*/
    /*left: 0;*/
  }

  .table-cell-action {
    position: absolute;
    //bottom: -30px;
    bottom: 0;
    right: -2px;
    height: 28px;
    line-height: 28px;
    background: $--color-primary;
    z-index: 999;

    i {
      font-size: 14px;
      color: #fff;
      margin: 0 5px;
      cursor: pointer;
    }
  }

  .table-cell-handler {
    position: absolute;
    top: -2px;
    //bottom: -24px;  /* 拖拽手柄位于组件下方，有时无法正常拖动，原因未明？？ */
    left: -2px;
    height: 22px;
    line-height: 22px;
    background: $--color-primary;
    z-index: 9;

    i {
      font-size: 14px;
      font-style: normal;
      color: #fff;
      margin: 4px;
      cursor: default; //cursor: move;
    }
  }
}

.table-cell {
  &:hover {
    outline: 1px solid #409EFF;
  }
}

.table-cell.selected {
  outline: 2px solid $--color-primary !important;
}

.dropdownInput {
  width: 50px;
  height: 24px;
  margin: 0 8px;

  ::v-deep .el-input__inner {
      height: 24px !important;
      line-height: 24px !important;
    }
}
</style>
