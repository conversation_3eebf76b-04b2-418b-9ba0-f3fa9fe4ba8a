export default {
  data() {
    return {
      // 合并右侧单元格
      mergeRightColNum: 1,
      // 合并左侧单元格
      mergeLeftColNum: 1,
      // 合并下方单元格
      mergeBelowRowNum: 1,
      // 合并上方单元格
      mergeAboveRowNum: 1,
      flag: false,
    }
  },
  methods: {
    // 下拉菜单输入框聚焦
    handleFocus(value) {
      this.flag = true;
    },
     // 下拉菜单输入框失去焦点
    handleBlur(command) {
      this.flag = false;
    },
    // 下拉菜单关闭重置
    handleVisibleChange(visible) {
      if (!visible) {
        this.mergeRightColNum = 1
        this.mergeLeftColNum = 1
        this.mergeBelowRowNum = 1
        this.mergeAboveRowNum = 1
      }
    },
  }

};
