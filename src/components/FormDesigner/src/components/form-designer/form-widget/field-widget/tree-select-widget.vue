<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
  >
    <treeselect
      ref="fieldEditor"
      v-model="fieldModel"
      :options="field.options.optionItems"
      :normalizer="normalizer"
      class="full-width-input"
      :disabled="field.options.disabled"
      :size="field.options.size"
      :clearable="field.options.clearable"
      :filterable="field.options.filterable"
      :allow-create="field.options.allowCreate"
      :default-first-option="allowDefaultFirstOption"
      :automatic-dropdown="field.options.automaticDropdown"
      :multiple="field.options.multiple"
      :multiple-limit="field.options.multipleLimit"
      :placeholder="field.options.placeholder || '请选择'"
      :remote="field.options.remote"
      :remote-method="remoteMethod"
      @select="handleSelect"
    >
      <el-option
        v-for="item in field.options.optionItems"
        :key="item.value"
        :label="item[field.options.optionLabel || 'label']"
        :value="item[field.options.optionValue || 'value']"
        :disabled="item.disabled"
      />
    </treeselect>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from '@@/utils/emitter';
import i18n, { translate } from '@@/utils/i18n';
import fieldMixin from '@@/components/form-designer/form-widget/field-widget/fieldMixin';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
  name: 'TreeSelectWidget',
  componentName: 'FieldWidget',
  components: {
    FormItemWrapper,
    Treeselect
  }, // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, // field组件change之前的值
      fieldModel: null,
      rules: [],
    };
  },
  computed: {
    allowDefaultFirstOption() {
      return !!this.field.options.filterable && !!this.field.options.allowCreate;
    },

    remoteMethod() {
      if (!!this.field.options.remote && !!this.field.options.onRemoteQuery) {
        return this.remoteQuery;
      } else {
        return undefined;
      }
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initOptionItems();
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    handleSelect(item) {
      const { optionValue, optionLabel, syncLabel } = this.field.options;
      this.handleChangeEvent(item[optionValue])

      // TODO: 注意编辑后保存字段丢失问题
      if (syncLabel) {
        this.$set(this.formModel, syncLabel, item[optionLabel] || '')
      }
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      const { optionValue, optionLabel, optionChildrenLabel} = this.field.options || {}
      return {
        id: node[optionValue] || '',
        label: node[optionLabel] || '',
        children: node[optionChildrenLabel] || '',
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/global.scss'; //* form-item-wrapper已引入，还需要重复引入吗？ *//

.full-width-input {
  width: 100% !important;
}
</style>
