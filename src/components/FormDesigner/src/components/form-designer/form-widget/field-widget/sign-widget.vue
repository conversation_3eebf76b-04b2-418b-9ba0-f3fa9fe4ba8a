<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
  >
    <template v-if="!isPreview">
      <div class="sign-layout" :class="{ 'no-click': !hasPermission || field.options.disabled }" @click="handleSignDialog">
        <img :src="fieldModel" width="100%"  v-if="fieldModel">
        <i class="el-icon-edit" v-else></i>
      </div>
      <el-dialog
        v-dialog-drag
        title="签名"
        :visible.sync="showSignDialog"
        append-to-body
        width="50%"
        :show-close="true"
        custom-class="drag-dialog small-padding-dialog"
        :close-on-click-modal="true"
        :close-on-press-escape="true"
        :destroy-on-close="true"
      >
      <vue-esign 
        ref="esign" 
        :width="1000" 
        :height="300"
        :style="'width:100%;margin: 0 auto;border: 1px solid #bfbdbd;'"
        :isCrop="signOption.isCrop" 
        :lineWidth="signOption.lineWidth" 
        :lineColor="signOption.lineColor" 
        :bgColor.sync="signOption.bgColor" />
        <div slot="footer">
          <el-button @click="handleReset">清空画板</el-button>
          <el-button type="primary" @click="handleGenerate">生成图片</el-button>
        </div>
      </el-dialog>
    </template>

    <img :src="fieldModel" width="100%"  v-else>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from '@@/utils/emitter';
import i18n, { translate } from '@@/utils/i18n';
import fieldMixin from '@@/components/form-designer/form-widget/field-widget/fieldMixin';
import { base64ToBlob } from "@@/utils/util";
import { postFileUpload } from "@/api/file";
import VueEsign from 'vue-esign'
import permission from "../../mixins/permission.js";

export default {
  name: 'signWidget',
  componentName: 'FieldWidget',
  components: {
    FormItemWrapper,
    VueEsign
  }, // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, permission, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    isPreview: Boolean,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, // field组件change之前的值
      fieldModel: null,
      rules: [],
      showSignDialog: false,
      signOption: {
        lineWidth: 6,
        lineColor: '#000000',
        bgColor: '',
        resultImg: '',
        isCrop: false
      }
    };
  },
  computed: {},
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    handleSignDialog() {
      if(!this.hasPermission || this.field.options.disabled) return

      this.showSignDialog = true;
    },
    handleReset () {
      this.$refs.esign.reset()
    },
    handleGenerate () {
      this.oldValue = this.fieldModel;
      this.$refs.esign.generate().then(async res => {
        let formData = new FormData();
        formData.append("file", base64ToBlob(res));
        formData.append("fileName", `sign-${new Date().getTime()}`);
        const result = await postFileUpload(formData);
        this.fieldModel = result?.data?.url || ''

        this.syncUpdateFormModel(this.fieldModel);
        this.emitFieldDataChange(this.fieldModel, this.oldValue);

        this.showSignDialog = false;
      }).catch(err => {
        console.log('上传签名失败', err);
        this.$message.warning('请签名')
      })
    }
  },
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/global.scss'; //* form-item-wrapper已引入，还需要重复引入吗？ *//
.sign-layout {
  width: 100%;
  min-height: 50px;
  display: flex;
  align-items: flex-start;
  justify-content: left;
  cursor: pointer;

  .el-icon-edit {
    margin-top: 12px;
  }
}
.no-click {
  color: #C0C4CC;
  cursor: not-allowed;
}
</style>
