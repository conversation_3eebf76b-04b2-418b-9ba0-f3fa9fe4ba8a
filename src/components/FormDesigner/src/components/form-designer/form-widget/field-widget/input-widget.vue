<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
  >
    <template v-if="!isPreview">
      <el-input
        ref="fieldEditor"
        v-model="fieldModel"
        :disabled="!hasPermission || field.options.disabled"
        :readonly="field.options.readonly"
        :size="field.options.size"
        class="hide-spin-button"
        :type="inputType"
        :show-password="field.options.showPassword"
        :placeholder="field.options.placeholder"
        :clearable="field.options.clearable"
        :minlength="field.options.minLength"
        :maxlength="field.options.maxLength"
        :show-word-limit="field.options.showWordLimit"
        :prefix-icon="field.options.prefixIcon"
        :suffix-icon="field.options.suffixIcon"
        @focus="handleFocusCustomEvent"
        @blur="handleBlurCustomEvent"
        @input="handleInputCustomEvent"
        @change="handleChangeEvent"
      >
        <el-button
          v-if="field.options.appendButton"
          slot="append"
          :disabled="field.options.disabled || field.options.appendButtonDisabled"
          :class="field.options.buttonIcon"
          @click.native="emitAppendButtonClick"
        />
      </el-input>
    </template>
    <template v-else>
      <span>{{ fieldModel }}</span>
    </template>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from '@@/utils/emitter';
import i18n, { translate } from '@@/utils/i18n';
import fieldMixin from '@@/components/form-designer/form-widget/field-widget/fieldMixin';
import permission from '../../mixins/permission.js';
import { DisplaySettingsType } from '@@/constants/display-settings.js';
export default {
  name: 'InputWidget',
  componentName: 'FieldWidget',
  components: {
    FormItemWrapper,
  }, // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n, permission],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    isPreview: Boolean,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, // field组件change之前的值
      fieldModel: null,
      rules: [],
    };
  },
  computed: {
    inputType() {
      if (this.field.options.type === 'number') {
        return 'text'; // 当input的type设置为number时，如果输入非数字字符，则v-model拿到的值为空字符串，无法实现输入校验！故屏蔽之！！
      }

      return this.field.options.type;
    },
    displaySettings() {
      return this.field.options.displaySettings || {};
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  watch: {
    displaySettings: {
      handler(newVal) {
        if (newVal.type) {
          this.handleDisplaySettings();
        }
      },
      deep: true,
    },
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();

    this.$nextTick(() => {
      this.handleDisplaySettings();
    });
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    // 处理回显逻辑
    handleDisplaySettings() {
      if (!this.isEdit && !this.displaySettings.type) return;

      const { type, field, syncField } = this.displaySettings;
      let syncFieldValue = '';
      if (type === DisplaySettingsType.User) {
        // 部门单独处理
        if (field === 'deptName') {
          this.fieldModel = this.userInfo.dept[field];
          syncFieldValue = this.userInfo.dept.deptId
        } else if (field === 'nickName') {
          this.fieldModel = this.userInfo[field];
          syncFieldValue = this.userInfo.userId
        } else {
          this.fieldModel = this.userInfo[field];
        }
      }

      if (type === DisplaySettingsType.Query) {
        const query = this.$route.query[field];
        if (query) {
          this.fieldModel = query;
        }
      }

      if (this.fieldModel) {
        this.handleChangeEvent(this.fieldModel);
        if (syncField) {
          this.$set(this.formModel, syncField, syncFieldValue);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/global.scss'; //* form-item-wrapper已引入，还需要重复引入吗？ *//
</style>
