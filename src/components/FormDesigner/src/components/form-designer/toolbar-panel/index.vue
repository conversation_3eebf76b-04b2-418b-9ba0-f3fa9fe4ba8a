<!-- eslint-disable vue/no-unused-components -->
<template>
  <div class="toolbar-container">
    <div class="left-toolbar">
      <el-button
        :disabled="undoDisabled"
        :title="i18nt('designer.toolbar.undoHint')"
        type="text"
        @click="undoHistory"
      >
        <svg-icon icon-class="undo" />
      </el-button>
      <el-button
        :disabled="redoDisabled"
        :title="i18nt('designer.toolbar.redoHint')"
        type="text"
        @click="redoHistory"
      >
        <svg-icon icon-class="redo" />
      </el-button>
      <el-button-group style="margin-left: 20px">
        <el-button :type="layoutType === 'PC' ? 'info' : ''" @click="changeLayoutType('PC')">
          {{ i18nt('designer.toolbar.pcLayout') }}
        </el-button>
        <!-- <el-button :type="layoutType === 'Pad' ? 'info' : ''" @click="changeLayoutType('Pad')">
          {{
            i18nt('designer.toolbar.padLayout')
          }}
        </el-button>
        <el-button :type="layoutType === 'H5' ? 'info' : ''" @click="changeLayoutType('H5')">
          {{
            i18nt('designer.toolbar.mobileLayout')
          }}
        </el-button> -->
      </el-button-group>
      <!-- <el-button
        :title="i18nt('designer.toolbar.nodeTreeHint')"
        style="margin-left: 20px"
        type
        @click="showNodeTreeDrawer"
      >
        <svg-icon icon-class="node-tree" />
      </el-button> -->
    </div>

    <el-drawer
      :destroy-on-close="true"
      :modal="false"
      :size="280"
      :title="i18nt('designer.toolbar.nodeTreeTitle')"
      :visible.sync="showNodeTreeDrawerFlag"
      class="node-tree-drawer"
      direction="ltr"
    >
      <el-tree
        ref="nodeTree"
        :data="nodeTreeData"
        class="node-tree"
        default-expand-all
        highlight-current
        icon-class="el-icon-arrow-right"
        node-key="id"
        @node-click="onNodeTreeClick"
      />
    </el-drawer>

    <div class="right-toolbar">
      <div class="right-toolbar-con">
        <el-button
          v-if="showToolButton('clearDesignerButton')"
          type="text"
          @click="clearFormWidget"
        >
          <i class="el-icon-delete" />
          {{ i18nt('designer.toolbar.clear') }}
        </el-button>
        <el-button v-if="showToolButton('previewFormButton')" type="text" @click="previewForm">
          <i class="el-icon-view" />
          {{ i18nt('designer.toolbar.preview') }}
        </el-button>
        <el-button v-if="showToolButton('importJsonButton')" type="text" @click="importJson">
          {{ i18nt('designer.toolbar.importJson') }}
        </el-button>
        <el-button v-if="showToolButton('exportJsonButton')" type="text" @click="exportJson">
          {{ i18nt('designer.toolbar.exportJson') }}
        </el-button>
        <!-- <el-button v-if="showToolButton('exportCodeButton')" type="text" @click="exportCode">
          {{
            i18nt('designer.toolbar.exportCode')
          }}
        </el-button> -->
        <!-- <el-button v-if="showToolButton('generateSFCButton')" type="text" @click="generateSFC">
          <svg-icon icon-class="vue-sfc" />
          {{ i18nt('designer.toolbar.generateSFC') }}
        </el-button> -->
        <template v-for="(idx, slotName) in $slots">
          <slot :name="slotName" />
        </template>
      </div>
    </div>

    <el-dialog
      v-if="showPreviewDialogFlag"
      v-dialog-drag
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :fullscreen="layoutType === 'H5' || layoutType === 'Pad'"
      :show-close="true"
      :title="i18nt('designer.toolbar.preview')"
      :visible.sync="showPreviewDialogFlag"
      center
      class="small-padding-dialog"
      width="55%"
    >
      <div>
        <div
          :class="[layoutType === 'H5' ? 'h5-layout' : layoutType === 'Pad' ? 'pad-layout' : '']"
          class="form-render-wrapper"
          ref="formRenderLayout"
        >
          <v-form-render
            ref="preForm"
            :form-data="testFormData"
            :form-json="formJson"
            :global-dsv="designerDsv"
            :option-data="testOptionData"
            :preview-state="true"
            :isPreview="isPreview"
            @appendButtonClick="testOnAppendButtonClick"
            @buttonClick="testOnButtonClick"
            @formChange="handleFormChange"
            @onUpdateWidgetList="handleUpdateWidgetList"
          >
            <!--
            <div slot="testSlot">aaaa</div>
            -->
          </v-form-render>
        </div>
      </div>
      <code-editor v-model="testFunc" style="display: none" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleEchoForm">
          {{ !isPreview ? '开启回显' : '关闭回显' }}
        </el-button>
        <el-button type="primary" @click="handleExportPdfForm" v-if="isPreview">
          导出 pdf
        </el-button>
        <el-button type="primary" @click="getFormData"
          >{{ i18nt('designer.hint.getFormData') }}
        </el-button>
        <el-button type="primary" @click="resetForm"
          >{{ i18nt('designer.hint.resetForm') }}
        </el-button>
        <el-button type="primary" @click="setFormDisabled"
          >{{ i18nt('designer.hint.disableForm') }}
        </el-button>
        <el-button type="primary" @click="setFormEnabled"
          >{{ i18nt('designer.hint.enableForm') }}
        </el-button>
        <el-button type @click="showPreviewDialogFlag = false"
          >{{ i18nt('designer.hint.closePreview') }}
        </el-button>
        <el-button v-if="false" @click="printFormJson">PrintFormJson</el-button>
        <el-button v-if="false" @click="testValidate">TestValidate</el-button>
        <el-button v-if="false" @click="testSetFormData">TestSF</el-button>
        <el-button v-if="false" @click="testReloadOptionData">Test ROD</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="showImportJsonDialogFlag"
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :show-close="true"
      :title="i18nt('designer.toolbar.importJson')"
      :visible.sync="showImportJsonDialogFlag"
      append-to-body
      center
      class="small-padding-dialog"
      width="65%"
    >
      <el-alert
        :title="i18nt('designer.hint.importJsonHint')"
        class="alert-padding"
        show-icon
        type="info"
      />
      <code-editor v-model="importTemplate" :mode="'json'" :readonly="false" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="doJsonImport">
          {{ i18nt('designer.hint.import') }}
        </el-button>
        <el-button @click="showImportJsonDialogFlag = false">
          {{ i18nt('designer.hint.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="showExportJsonDialogFlag"
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :show-close="true"
      :title="i18nt('designer.toolbar.exportJson')"
      :visible.sync="showExportJsonDialogFlag"
      append-to-body
      center
      class="small-padding-dialog"
      width="65%"
    >
      <el-tabs v-model="activeJson" type="card" @tab-click="handleJsonTabClick">
        <el-tab-pane label="基础JSON" name="baseJson">
          <code-editor v-model="jsonContent" :mode="'json'" :readonly="true" />
        </el-tab-pane>
        <el-tab-pane label="类JSON" name="classJson">
          <code-editor v-model="classJsonContent" :mode="'json'" :readonly="true" />
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button
          :data-clipboard-text="activeJson === 'baseJson' ? jsonRawContent : classJsonRawContent"
          class="copy-json-btn"
          type="primary"
          @click="copyFormJson"
          >{{ i18nt('designer.hint.copyJson') }}
        </el-button>
        <el-button @click="saveFormJson">{{ i18nt('designer.hint.saveFormJson') }}</el-button>
        <el-button type @click="showExportJsonDialogFlag = false">
          {{ i18nt('designer.hint.closePreview') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="showExportCodeDialogFlag"
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :show-close="true"
      :title="i18nt('designer.toolbar.exportCode')"
      :visible.sync="showExportCodeDialogFlag"
      append-to-body
      center
      class="small-padding-dialog"
      width="65%"
    >
      <el-tabs v-model="activeCodeTab" class="no-box-shadow no-padding" type="border-card">
        <el-tab-pane label="Vue" name="vue">
          <code-editor v-model="vueCode" :mode="'html'" :readonly="true" :user-worker="false" />
        </el-tab-pane>
        <el-tab-pane label="HTML" name="html">
          <code-editor v-model="htmlCode" :mode="'html'" :readonly="true" :user-worker="false" />
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button
          :data-clipboard-text="vueCode"
          class="copy-vue-btn"
          type="primary"
          @click="copyVueCode"
          >{{ i18nt('designer.hint.copyVueCode') }}
        </el-button>
        <el-button
          :data-clipboard-text="htmlCode"
          class="copy-html-btn"
          type="primary"
          @click="copyHtmlCode"
          >{{ i18nt('designer.hint.copyHtmlCode') }}
        </el-button>
        <el-button @click="saveVueCode">{{ i18nt('designer.hint.saveVueCode') }}</el-button>
        <el-button @click="saveHtmlCode">{{ i18nt('designer.hint.saveHtmlCode') }}</el-button>
        <el-button type @click="showExportCodeDialogFlag = false">
          {{ i18nt('designer.hint.closePreview') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="showFormDataDialogFlag"
      v-dialog-drag
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :show-close="true"
      :title="i18nt('designer.hint.exportFormData')"
      :visible.sync="showFormDataDialogFlag"
      center
      class="dialog-title-light-bg"
    >
      <div style="border: 1px solid #dcdfe6">
        <code-editor v-model="formDataJson" :mode="'json'" :readonly="true" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          :data-clipboard-text="formDataRawJson"
          class="copy-form-data-json-btn"
          type="primary"
          @click="copyFormDataJson"
          >{{ i18nt('designer.hint.copyFormData') }}
        </el-button>
        <el-button @click="saveFormData">{{ i18nt('designer.hint.saveFormData') }}</el-button>
        <el-button type @click="showFormDataDialogFlag = false">
          {{ i18nt('designer.hint.closePreview') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="showExportSFCDialogFlag"
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :show-close="true"
      :title="i18nt('designer.toolbar.generateSFC')"
      :visible.sync="showExportSFCDialogFlag"
      append-to-body
      center
      class="small-padding-dialog"
      width="65%"
    >
      <el-tabs v-model="activeSFCTab" class="no-box-shadow no-padding" type="border-card">
        <el-tab-pane label="Vue2" name="vue2">
          <code-editor v-model="sfcCode" :mode="'html'" :readonly="true" :user-worker="false" />
        </el-tab-pane>
        <el-tab-pane label="Vue3" name="vue3">
          <code-editor v-model="sfcCodeV3" :mode="'html'" :readonly="true" :user-worker="false" />
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button
          :data-clipboard-text="sfcCode"
          class="copy-vue2-sfc-btn"
          type="primary"
          @click="copyV2SFC"
          >{{ i18nt('designer.hint.copyVue2SFC') }}
        </el-button>
        <el-button
          :data-clipboard-text="sfcCodeV3"
          class="copy-vue3-sfc-btn"
          type="primary"
          @click="copyV3SFC"
          >{{ i18nt('designer.hint.copyVue3SFC') }}
        </el-button>
        <el-button @click="saveV2SFC">{{ i18nt('designer.hint.saveVue2SFC') }}</el-button>
        <el-button @click="saveV3SFC">{{ i18nt('designer.hint.saveVue3SFC') }}</el-button>
        <el-button type @click="showExportSFCDialogFlag = false">
          {{ i18nt('designer.hint.closePreview') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import VFormRender from '@@/components/form-render/index';
import CodeEditor from '@@/components/code-editor/index';
import Clipboard from 'clipboard';
import {
  deepClone,
  copyToClipboard,
  generateId,
  getQueryParam,
  traverseAllWidgets,
  addWindowResizeHandler,
} from '@@/utils/util';
import i18n from '@@/utils/i18n';
import { generateCode } from '@@/utils/code-generator';
import { genSFC } from '@@/utils/sfc-generator';
import loadBeautifier from '@@/utils/beautifierLoader';
import { saveAs } from 'file-saver';
import SvgIcon from '@@/components/svg-icon';
import ExportPdf from '/src/mixin/exportPdf.js';
import { formatClassJson, formatTableClassJson, findNestTableByName } from './utils';

export default {
  name: 'ToolbarPanel',
  components: {
    VFormRender,
    CodeEditor,
    Clipboard,
    SvgIcon,
  },
  mixins: [i18n, ExportPdf],
  props: {
    designer: Object,
    globalDsv: {
      type: Object,
      default: () => ({}),
    },
  },
  inject: ['getDesignerConfig'],
  data() {
    return {
      designerConfig: this.getDesignerConfig(),

      toolbarWidth: 420,
      showPreviewDialogFlag: false,
      showImportJsonDialogFlag: false,
      showExportJsonDialogFlag: false,
      showExportCodeDialogFlag: false,
      showFormDataDialogFlag: false,
      showExportSFCDialogFlag: false,
      showNodeTreeDrawerFlag: false,

      nodeTreeData: [],

      testFunc: '',
      importTemplate: '',
      jsonContent: '',
      jsonRawContent: '',
      classJsonContent: '',
      classJsonRawContent: '',

      formDataJson: '',
      formDataRawJson: '',

      vueCode: '',
      htmlCode: '',

      sfcCode: '',
      sfcCodeV3: '',

      activeCodeTab: 'vue',
      activeSFCTab: 'vue2',

      testFormData: {
        // 'userName': '666888',
        // 'productItems': [
        //   {'pName': 'iPhone12', 'pNum': 10},
        //   {'pName': 'P50', 'pNum': 16},
        // ]

        select62173: 2,
      },
      testOptionData: {
        select62173: [
          { label: '01', value: 1 },
          { label: '22', value: 2 },
          { label: '333', value: 3 },
        ],

        select001: [
          { label: '辣椒', value: 1 },
          { label: '菠萝', value: 2 },
          { label: '丑橘子', value: 3 },
        ],
      },

      activeJson: 'baseJson',

      isPreview: false,
    };
  },
  computed: {
    formJson() {
      return {
        // widgetList: this.designer.widgetList,
        // formConfig: this.designer.formConfig

        widgetList: deepClone(this.designer.widgetList),
        formConfig: deepClone(this.designer.formConfig),
      };
    },

    undoDisabled() {
      return !this.designer.undoEnabled();
    },

    redoDisabled() {
      return !this.designer.redoEnabled();
    },

    layoutType() {
      return this.designer.getLayoutType();
    },

    designerDsv() {
      return this.globalDsv;
    },
  },
  watch: {
    'designer.widgetList': {
      deep: true,
      handler(val) {
        // console.log('test-----', val)
        // this.refreshNodeTree()
      },
    },
  },
  mounted() {
    const maxTBWidth = this.designerConfig.toolbarMaxWidth || 420;
    const minTBWidth = this.designerConfig.toolbarMinWidth || 300;
    const newTBWidth = window.innerWidth - 260 - 300 - 320 - 80;
    this.toolbarWidth =
      newTBWidth >= maxTBWidth ? maxTBWidth : newTBWidth <= minTBWidth ? minTBWidth : newTBWidth;
    addWindowResizeHandler(() => {
      this.$nextTick(() => {
        const newTBWidth2 = window.innerWidth - 260 - 300 - 320 - 80;
        this.toolbarWidth =
          newTBWidth2 >= maxTBWidth
            ? maxTBWidth
            : newTBWidth2 <= minTBWidth
            ? minTBWidth
            : newTBWidth2;
      });
    });
  },
  methods: {
    showToolButton(configName) {
      if (this.designerConfig[configName] === undefined) {
        return true;
      }

      return !!this.designerConfig[configName];
    },

    buildTreeNodeOfWidget(widget, treeNode) {
      const curNode = {
        id: widget.id,
        label: widget.options.label || widget.type,
        // selectable: true,
      };
      treeNode.push(curNode);

      if (widget.category === undefined) {
        return;
      }

      curNode.children = [];
      if (widget.type === 'grid') {
        widget.cols.map(col => {
          const colNode = {
            id: col.id,
            label: col.options.name || widget.type,
            children: [],
          };
          curNode.children.push(colNode);
          col.widgetList.map(wChild => {
            this.buildTreeNodeOfWidget(wChild, colNode.children);
          });
        });
      } else if (widget.type === 'table') {
        // TODO: 需要考虑合并单元格！！
        widget.rows.map(row => {
          const rowNode = {
            id: row.id,
            label: 'table-row',
            selectable: false,
            children: [],
          };
          curNode.children.push(rowNode);

          row.cols.map(cell => {
            if (cell.merged) {
              // 跳过合并单元格！！
              return;
            }

            const rowChildren = rowNode.children;
            const cellNode = {
              id: cell.id,
              label: 'table-cell',
              children: [],
            };
            rowChildren.push(cellNode);

            cell.widgetList.map(wChild => {
              this.buildTreeNodeOfWidget(wChild, cellNode.children);
            });
          });
        });
      } else if (widget.type === 'tab') {
        widget.tabs.map(tab => {
          const tabNode = {
            id: tab.id,
            label: tab.options.name || widget.type,
            selectable: false,
            children: [],
          };
          curNode.children.push(tabNode);
          tab.widgetList.map(wChild => {
            this.buildTreeNodeOfWidget(wChild, tabNode.children);
          });
        });
      } else if (widget.type === 'sub-form') {
        widget.widgetList.map(wChild => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.category === 'container') {
        // 自定义容器
        widget.widgetList.map(wChild => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }
    },

    refreshNodeTree() {
      this.nodeTreeData.length = 0;
      this.designer.widgetList.forEach(wItem => {
        this.buildTreeNodeOfWidget(wItem, this.nodeTreeData);
      });
    },

    showNodeTreeDrawer() {
      this.refreshNodeTree();
      this.showNodeTreeDrawerFlag = true;
      this.$nextTick(() => {
        if (this.designer.selectedId) {
          // 同步当前选中组件到节点树！！！
          this.$refs.nodeTree.setCurrentKey(this.designer.selectedId);
        }
      });
    },

    undoHistory() {
      this.designer.undoHistoryStep();
    },

    redoHistory() {
      this.designer.redoHistoryStep();
    },

    changeLayoutType(newType) {
      this.designer.changeLayoutType(newType);
    },

    clearFormWidget() {
      this.designer.clearDesigner();
      this.designer.formWidget.clearWidgetRefList();
    },

    previewForm() {
      this.showPreviewDialogFlag = true;
    },

    saveAsFile(fileContent, defaultFileName) {
      this.$prompt(
        this.i18nt('designer.hint.fileNameForSave'),
        this.i18nt('designer.hint.saveFileTitle'),
        {
          inputValue: defaultFileName,
          closeOnClickModal: false,
          inputPlaceholder: this.i18nt('designer.hint.fileNameInputPlaceholder'),
        },
      )
        .then(({ value }) => {
          if (!value) {
            value = defaultFileName;
          }

          if (getQueryParam('vscode') == 1) {
            this.vsSaveFile(value, fileContent);
            return;
          }

          const fileBlob = new Blob([fileContent], {
            type: 'text/plain;charset=utf-8',
          });
          saveAs(fileBlob, value);
        })
        .catch(() => {
          //
        });
    },

    vsSaveFile(fileName, fileContent) {
      const msgObj = {
        cmd: 'writeFile',
        data: {
          fileName,
          code: fileContent,
        },
      };
      window.parent.postMessage(msgObj, '*');
    },

    importJson() {
      this.importTemplate = JSON.stringify(this.designer.getImportTemplate(), null, '  ');
      this.showImportJsonDialogFlag = true;
    },

    doJsonImport() {
      try {
        const importObj = JSON.parse(this.importTemplate);
        this.designer.loadFormJson(importObj);

        this.showImportJsonDialogFlag = false;
        this.$message.success(this.i18nt('designer.hint.importJsonSuccess'));

        this.designer.emitHistoryChange();

        this.designer.emitEvent('form-json-imported', []);
      } catch (ex) {
        this.$message.error(ex + '');
      }
    },

    handleJsonTabClick(tab, event) {
      this.activeJson = tab.name;
    },

    exportJson() {
      const widgetList = deepClone(this.designer.widgetList);
      const formConfig = deepClone(this.designer.formConfig);
      this.jsonContent = JSON.stringify({ widgetList, formConfig }, null, 2);
      this.jsonRawContent = JSON.stringify({ widgetList, formConfig });
      try {
        const classJson = formatClassJson(widgetList, formConfig, this.designer.formName);
        this.classJsonContent = JSON.stringify(classJson, null, 2);
        this.classJsonRawContent = JSON.stringify(classJson);
      } catch (error) {
        console.log('解析classJson失败', error);
      }

      this.showExportJsonDialogFlag = true;
    },

    copyFormJson(e) {
      const jsonText =
        this.activeJson === 'baseJson' ? this.jsonRawContent : this.classJsonRawContent;
      copyToClipboard(
        jsonText,
        e,
        this.$message,
        this.i18nt('designer.hint.copyJsonSuccess'),
        this.i18nt('designer.hint.copyJsonFail'),
      );
    },

    saveFormJson() {
      this.saveAsFile(this.jsonContent, `vform${generateId()}.json`);
    },

    exportCode() {
      this.vueCode = generateCode(this.formJson);
      this.htmlCode = generateCode(this.formJson, 'html');
      this.showExportCodeDialogFlag = true;
    },

    copyVueCode(e) {
      copyToClipboard(
        this.vueCode,
        e,
        this.$message,
        this.i18nt('designer.hint.copyVueCodeSuccess'),
        this.i18nt('designer.hint.copyVueCodeFail'),
      );
    },

    copyHtmlCode(e) {
      copyToClipboard(
        this.htmlCode,
        e,
        this.$message,
        this.i18nt('designer.hint.copyHtmlCodeSuccess'),
        this.i18nt('designer.hint.copyHtmlCodeFail'),
      );
    },

    saveVueCode() {
      this.saveAsFile(this.vueCode, `vform${generateId()}.vue`);
    },

    saveHtmlCode() {
      this.saveAsFile(this.htmlCode, `vform${generateId()}.html`);
    },

    generateSFC() {
      loadBeautifier(beautifier => {
        this.sfcCode = genSFC(this.designer.formConfig, this.designer.widgetList, beautifier);
        this.sfcCodeV3 = genSFC(
          this.designer.formConfig,
          this.designer.widgetList,
          beautifier,
          true,
        );
        this.showExportSFCDialogFlag = true;
      });
    },

    copyV2SFC(e) {
      copyToClipboard(
        this.sfcCode,
        e,
        this.$message,
        this.i18nt('designer.hint.copySFCSuccess'),
        this.i18nt('designer.hint.copySFCFail'),
      );
    },

    copyV3SFC(e) {
      copyToClipboard(
        this.sfcCodeV3,
        e,
        this.$message,
        this.i18nt('designer.hint.copySFCSuccess'),
        this.i18nt('designer.hint.copySFCFail'),
      );
    },

    saveV2SFC() {
      this.saveAsFile(this.sfcCode, `vformV2-${generateId()}.vue`);
    },

    saveV3SFC() {
      this.saveAsFile(this.sfcCodeV3, `vformV3-${generateId()}.vue`);
    },
    // 找到嵌套表
    findNestTable(widgetList, name) {
      return widgetList.find(item => item.options.name === name) || {};
    },

    // 处理嵌套表
    handlerArray(className, widgetList, formData) {
      const curtWidget = findNestTableByName(widgetList, className)
      const headRow = curtWidget.rows[0];
      // 删除第一行数据，即删除表头
      const rows = curtWidget.rows.slice(1);

      const newData = [];
      rows.map(item => {
        const rowItem = {};
        item.cols.map((colsItem, i) => {
          const headClosItem = headRow.cols[i].options || {};
          const { merged, widgetList } = colsItem;
          const widgetConfig = widgetList[0] || {};
          if (!merged) {
            if (widgetConfig.type === 'static-text') {
              rowItem[headClosItem.name] = widgetConfig.options.textContent || '';
            } else {
              rowItem[headClosItem.name] = formData[widgetConfig.id] || '';
            }
          }
        });

        console.log('rowItem :>> ', rowItem);

        newData.push(rowItem);
      });

      return newData;
    },

    // 更新组件列表
    handleUpdateWidgetList(widgetList) {
      this.designer.widgetList = widgetList;
    },

    getFormData() {
      this.$refs['preForm']
        .getFormData()
        .then(formData => {
          console.log('formData :>> ', formData);
          // 表单样式 JSON
          const widgetList = deepClone(this.designer.widgetList);
          const formConfig = deepClone(this.designer.formConfig);
          console.log('widgetList :>> ', widgetList);
          console.log('formConfig :>> ', formConfig);
          // 表单类 JSON
          const classJson = formatClassJson(widgetList, formConfig, this.designer.formName);

          const makeFormData = {};
          const properties = classJson[0].properties || [];

          properties.map(item => {
            if (item.propertyType === 'ARRAY') {
              console.log('item :>> ', item);
              makeFormData[item.propertyName] = this.handlerArray(
                item.propertyName,
                widgetList,
                formData,
              );
            } else {
              makeFormData[item.propertyName] = formData[item.propertyName];
            }
          });

          // 业务组件-检验项目 传值特殊处理
          const inspectionItemsIndex = widgetList.findIndex(
            item => item.type === 'inspectionItemsTable',
          );
          let items = [];
          if (inspectionItemsIndex !== -1) {
            const inspectionItems = widgetList[inspectionItemsIndex] || [];
            inspectionItems.rows.map((rowItem, rowIndex) => {
              const rows = {};
              // 忽略表头
              if (rowIndex === 0) return;
              rowItem.cols.map(item => {
                const widgetItem = item.widgetList[0];
                if (widgetItem.type === 'textarea') {
                  const key = widgetItem.id.split('-')[0];
                  rows[key] = formData[widgetItem.id];
                  rows.itemId = widgetItem.options.itemId;
                }
              });
              items.push(rows);
            });
          }

          // if (widgetList.find(item => item.type === 'data-table')) {
          //   const dataTableIndex = widgetList.findIndex(item => item.type === 'data-table');
          //   const dataTable = widgetList[dataTableIndex];
          //   dataTable.options.tableData = items;
          // }

          if (!!items.length) {
            makeFormData.items = items;
            makeFormData['processService'] = 'Dlt5210RecordTestBatchProcessService';
          }

          console.log('object :>> formData', formData);
          this.formDataJson = JSON.stringify(makeFormData, null, '  ');
          this.formDataRawJson = JSON.stringify(makeFormData);

          this.showFormDataDialogFlag = true;
        })
        .catch(error => {
          console.log('object :>>error ', error);
          this.$message.error(error);
        });
    },

    copyFormDataJson(e) {
      copyToClipboard(
        this.formDataRawJson,
        e,
        this.$message,
        this.i18nt('designer.hint.copyJsonSuccess'),
        this.i18nt('designer.hint.copyJsonFail'),
      );
    },

    saveFormData() {
      this.saveAsFile(this.htmlCode, `formData${generateId()}.json`);
    },

    resetForm() {
      this.$refs['preForm'].resetForm();
    },

    setFormDisabled() {
      this.$refs['preForm'].disableForm();
    },
    handleEchoForm() {
      this.isPreview = !this.isPreview;
    },
    handleExportPdfForm() {
      const element = this.$refs.formRenderLayout;
      this.exportPDF(element);
    },

    setFormEnabled() {
      this.$refs['preForm'].enableForm();
    },

    printFormJson() {
      const tmpFS = {
        widgetList: deepClone(this.designer.widgetList),
        formConfig: deepClone(this.designer.formConfig),
      };

      console.log(tmpFS);
    },

    testValidate() {
      console.log('test===', this.$refs['preForm'].validateForm());
    },

    testSetFormData() {
      // let fData = {
      //   'fuTest': [
      //     {
      //       name: '上传文件测试.xlsx',
      //       url: 'https://www.vform666.com/123.xlsx'
      //     }
      //   ]
      // }
      // this.$refs['preForm'].setFormData(fData)

      const nfData = {
        checkbox45524: [1, 2],
      };
      this.$refs['preForm'].setFormData(nfData);
    },

    testReloadOptionData() {
      this.testOptionData['select001'].push({
        label: 'aaa',
        value: 888,
      });

      // this.$refs.preForm.reloadOptionData()
      this.$refs.preForm.reloadOptionData('select001');
    },

    handleFormChange(fieldName, newValue, oldValue, formModel) {
      /*
        console.log('---formChange start---')
        console.log('fieldName', fieldName)
        console.log('newValue', newValue)
        console.log('oldValue', oldValue)
        console.log('formModel', formModel)
        console.log('---formChange end---')
        */
    },

    testOnAppendButtonClick(clickedWidget) {
      console.log('test', clickedWidget);
    },

    testOnButtonClick(button) {
      console.log('test', button);
    },

    findWidgetById(wId) {
      let foundW = null;
      traverseAllWidgets(this.designer.widgetList, w => {
        if (w.id === wId) {
          foundW = w;
        }
      });

      return foundW;
    },

    onNodeTreeClick(nodeData, node, nodeEl) {
      // console.log('test', JSON.stringify(nodeData))

      if (nodeData.selectable !== undefined && !nodeData.selectable) {
        this.$message.info(this.i18nt('designer.hint.currentNodeCannotBeSelected'));
      } else {
        const selectedId = nodeData.id;
        const foundW = this.findWidgetById(selectedId);
        if (foundW) {
          this.designer.setSelected(foundW);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
div.toolbar-container {
  display: flex;
  justify-content: space-between;
}

.left-toolbar {
  float: left;
  font-size: 16px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.right-toolbar {
  float: right;
  text-align: right;
  overflow: auto;

  .right-toolbar-con {
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: right;
  }

  ::v-deep .el-button--text {
    font-size: 14px !important;
  }
}

.el-button i {
  margin-right: 3px;
}

.small-padding-dialog {
  ::v-deep .el-dialog__header {
    //padding-top: 3px;
    //padding-bottom: 3px;
    background: #f1f2f3;
  }

  ::v-deep .el-dialog__body {
    padding: 12px 15px 12px 15px;

    .el-alert.alert-padding {
      padding: 0 10px;
    }
  }

  ::v-deep .ace-container {
    border: 1px solid #dcdfe6;
  }
}

.dialog-title-light-bg {
  ::v-deep .el-dialog__header {
    background: #f1f2f3;
  }
}

.no-box-shadow {
  box-shadow: none;
}

.no-padding.el-tabs--border-card {
  ::v-deep .el-tabs__content {
    padding: 0;
  }
}

.form-render-wrapper {
  //height: calc(100vh - 142px);
  //all: revert !important; /* 防止表单继承el-dialog等外部样式，未生效，原因不明？？ */

  ::v-deep .el-form-item__label {
    font-size: 13px !important;
    font-weight: 400;
  }
}

.form-render-wrapper.h5-layout {
  margin: 0 auto;
  width: 420px;
  border-radius: 15px;
  //border-width: 10px;
  box-shadow: 0 0 1px 10px #495060;
  height: calc(100vh - 142px);
  overflow-y: auto;
  overflow-x: hidden;
}

.form-render-wrapper.pad-layout {
  margin: 0 auto;
  width: 960px;
  border-radius: 15px;
  //border-width: 10px;
  box-shadow: 0 0 1px 10px #495060;
  height: calc(100vh - 142px);
  overflow-y: auto;
  overflow-x: hidden;
}

.node-tree-drawer ::v-deep {
  .el-drawer {
    padding: 10px;
    overflow: auto;
  }

  .el-drawer__header {
    margin-bottom: 12px;
    padding: 5px 5px 0;
  }

  .el-drawer__body {
    padding-left: 5px;
  }
}

/*.node-tree-scroll-bar {*/
/*  height: 100%;*/
/*  overflow: auto;*/
/*}*/

.node-tree ::v-deep {
  .el-tree > .el-tree-node:after {
    border-top: none;
  }

  .el-tree-node {
    position: relative;
    padding-left: 12px;
  }

  .el-tree-node__content {
    padding-left: 0 !important;
  }

  .el-tree-node__expand-icon.is-leaf {
    display: none;
  }

  .el-tree-node__children {
    padding-left: 12px;
    overflow: visible !important; /* 加入此行让el-tree宽度自动撑开，超出宽度el-draw自动出现水平滚动条！ */
  }

  .el-tree-node :last-child:before {
    height: 38px;
  }

  .el-tree > .el-tree-node:before {
    border-left: none;
  }

  .el-tree > .el-tree-node:after {
    border-top: none;
  }

  .el-tree-node:before {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:after {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:before {
    border-left: 1px dashed #4386c6;
    bottom: 0px;
    height: 100%;
    top: -10px;
    width: 1px;
  }

  .el-tree-node:after {
    border-top: 1px dashed #4386c6;
    height: 20px;
    top: 12px;
    width: 16px;
  }

  .el-tree-node.is-current > .el-tree-node__content {
    background: #c2d6ea !important;
  }

  .el-tree-node__expand-icon {
    margin-left: -3px;
    padding: 6px 6px 6px 0px;
    font-size: 16px;
  }
}
</style>
