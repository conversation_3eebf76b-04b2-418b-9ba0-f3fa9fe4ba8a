import { PropertyType, CompTypePropertyType } from '@@/constants/iot'

/**
 * 将表单项与类类型映射关系
 * @param {*} formItemOptions 表单项 JSON
 * @param {*} propertyType 表单项与类类型映射
 * @description
 * "specs": {
 * "min": "参数最小值（int、float、double类型特有）。",
 * "max": "参数最大值（int、float、double类型特有）。",
 * "unit": "属性单位（int、float、double类型特有，非必填）。",
 * "unitName": "单位名称（int、float、double类型特有，非必填）。",
 * "size": "数组元素的个数，最大512（array类型特有）。",
 * "step": "步长（text、enum类型无此参数）。",
 * "length": "数据长度，最大10240（text类型特有）。",
 * "0": "0的值（bool类型特有）。",
 * "1": "1的值（bool类型特有）。",
 * "item": {
 * "type": "数组元素的类型（array类型特有）。"
 * }
 */
// TODO：动态获取数据的 option 如何传值？
export const formatDataSpecs = (formItemOptions, propertyType) => {
  const { min, max, step, maxLength, optionItems, } = formItemOptions || {};
  let dataSpecs = {};
  if (
    [PropertyType.INT, PropertyType.FLOAT, PropertyType.DOUBLE].includes(
      propertyType
    )
  ) {
    dataSpecs = {
      min: min || 0,
      max: max || 0,
      step: step || 1,
      unit: '',
      unitName: '',
    };
  }

  if ([PropertyType.TEXT].includes(propertyType)) {
    dataSpecs = {
      length: maxLength,
    };
  }

  if ([PropertyType.BOOL].includes(propertyType)) {
    dataSpecs = {
      [formItemOptions['activeText']]: '开',
      [formItemOptions['inactiveText']]: '关',
    };
  }

  if ([PropertyType.ENUM].includes(propertyType)) {
    dataSpecs = {
      enmu:
      optionItems && optionItems.length !== 0
        ? optionItems.map((item) => {
          return {
            name: item.label,
            value: item.value,
          };
        })
        : [],
    };
  }

  return JSON.stringify(dataSpecs);
};

/**
 * 将表单项处理为类格式
 * @param {*} FormConfigItem 表单项 JSON
 * @returns Object
 */
export const formatPropertiesJson = (FormConfigItem) => {
  const { options, type } = FormConfigItem;
  const { name, label, textContent } = options || {};
  const propertyType = CompTypePropertyType[type] || PropertyType.TEXT;
  return {
    propertyName: name,
    propertyDispname: textContent ? textContent : label,
    propertyType,
    dataSpecs: formatDataSpecs(options, propertyType),
    rwFlag: 'READ_WRITE',
    accessType: 'private',
    remark: '',
  };
};

/** ==========================  嵌套表单   ============================================== */

/**
 * 处理嵌套表数据
 * @param {*} nestTable
 * @returns
 */
export const filterNestTableData = (nestTable) => {
  const { id, options }  = nestTable ||  {}
  const { label, name }  = options ||  {}
  return {
    "propertyName": name,
    "propertyDispname": label || '嵌套表',
    "propertyType": "ARRAY",
    "rwFlag": "READ_WRITE",
    "accessType": "public",
    "dataSpecs": {
      "itemType": "STRUCT",
      "size": "12",
      "className": id,
      "packageName": ""
    }
  }
}

/**
 * 处理嵌套表 properties
 * @param {*} widgetListItem
 * @returns
 */
export const formatNestTableTableClassJson = (widgetListItem = {}) => {
  const { options, id } = widgetListItem || {};
  const { label }  = options ||  {}
  const tableHeadRow = widgetListItem.rows[0]
  return {
    className: id,
    classDispname: label || '嵌套表',
    classesType: 'NOT',
    accessType: 'public',
    remark: '',
    viewStyle: '',
    properties: handleNestTableProperties(tableHeadRow),
  }
};

/**
 * 处理嵌套表表头数据
 * @param {*} tableHeadRow
 */
export const handleNestTableProperties = (tableHeadRow = {}) => {
  const { cols = [] } = tableHeadRow;
  if (cols && !!cols.length) {
    return cols.filter(item => !item.merged).map(item => handleNestTablePropertiesJson(item))
  }
  return []
}

/**
 * 处理嵌套表表头数据
 * @param {*} nestTableHead
 */
export const handleNestTablePropertiesJson = (rowItem = {}) => {
  const { textContent, label } = rowItem.widgetList[0]?.options || {};
  const { widgetConfig, name } = rowItem.options || {};
  const { type, options } = widgetConfig || {};
  const propertyType = CompTypePropertyType[type] || PropertyType.TEXT;
  return {
    propertyName: name,
    propertyDispname: textContent ? textContent : label,
    propertyType,
    dataSpecs: formatDataSpecs(options, propertyType),
    rwFlag: 'READ_WRITE',
    accessType: 'private',
    remark: '',
  };

}


/** ==========================   表单转 class   ============================================== */

/**
 * 递归出表单内表单项
 * @param {*} widgetListJson 表单 JSON
 * @returns Array Json
 */
export const handleForm = (widgetListJson = []) => {
  const filterWidgetData = [];
  // 嵌套表
  const nestTableData =  [];
  // 递归子项
  function filterWidgetItem(widgetItem) {
    const { cols = [], rows = [], tabs = [], widgetList = [], type } = widgetItem || {};
    if (type === 'nestTable') {
      filterWidgetData.push(filterNestTableData(widgetItem))
      nestTableData.push(widgetItem)
      return
    }
    if (cols.length === 0 && rows.length === 0 && tabs.length === 0 && widgetList.length === 0) {
      // 容器组件不计入
      // TODO: 后续维护在统一的地方
      if ([
        'table-cell',
        'table',
        'grid-col',
        'grid',
        'tab-pane',
        'tab',
        'sub-form',
        'container',
        'static-text'
      ].includes(type)) return;
      filterWidgetData.push(formatPropertiesJson(widgetItem));
      return;
    }
    if (cols.length > 0) {
      filterWidget(cols)
    }
    if (rows.length > 0) {
      filterWidget(rows)
    }
    if (tabs.length > 0) {
      filterWidget(tabs)
    }
    if (widgetList.length > 0) {
      filterWidget(widgetList)
    }
  }

  // 递归父项
  function filterWidget(widgetData) {
    for (let index = 0; index < widgetData.length; index++) {
      const item = widgetData[index];
      filterWidgetItem(item)
    }
  }

  filterWidget(widgetListJson)

  return {
    properties: filterWidgetData,
    nestTableData: nestTableData
  }
}

/**
 * 获取表单对应类 JSON
 * @param {*} widgetList 表单 JSON
 * @param {*} formData  表单配置 JSON
 * @returns Object
 */
export const formatClassJson = (widgetList = [], formData = {}, formName = '') => {
  const { name } = formData || {};
  const { nestTableData, properties } = handleForm(widgetList)

  // 嵌套表处理
  const nestTableClass = []
  if (nestTableData && !!nestTableData.length) {
    nestTableData.forEach(item => {
      nestTableClass.push(formatNestTableTableClassJson(item))
    })
  }

  return [
    {
      className: name,
      classDispname: formName,
      classesType: 'NOT',
      accessType: 'public',
      remark: '',
      viewStyle: '',
      properties
    },
    ...nestTableClass
  ]
};










// 遍历表单json ,找到嵌套表
// TODO: 暂时只支持表格，后续优化其它容器
export const findNestTableByName = (function() {
  // 内部递归查找函数
  function findNestTableInner(obj, targetName) {
    // 处理数组
    if (Array.isArray(obj)) {
      for (let item of obj) {
        const result = findNestTableInner(item, targetName);
        if (result) return result;
      }
    }
    // 处理对象
    else if (typeof obj === 'object' && obj !== null) {
      // 找到匹配的nestTable则返回
      if (obj.type === 'nestTable' &&
          obj.options &&
          obj.options.name === targetName) {
        return obj;
      }
      // 递归遍历对象的所有属性
      for (let key in obj) {
        const result = findNestTableInner(obj[key], targetName);
        if (result) return result;
      }
    }
    return null;
  }

  // 返回查找函数
  return function(data, name) {
    if (!data || !name) return null;
    return findNestTableInner(data, name);
  }
})();
