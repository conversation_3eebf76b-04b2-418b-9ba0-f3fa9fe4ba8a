<template>
  <div>
    <el-divider class="custom-divider-margin-top">{{
      i18nt('designer.setting.optionsSetting')
    }}</el-divider>
    <el-form-item :label="i18nt('designer.setting.optionType')">
      <el-select v-model="optionModel.optionType">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@@/utils/i18n';
import { OptionType, CascaderOptionType} from "@@/constants/setting-panel.js";
export default {
  name: 'OptionTypeEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
    };
  },
  computed: {
    options() {
      if (this.optionModel.label !== 'cascader') {
        return OptionType
      }
      return CascaderOptionType
    }
  },
  methods: {
  },
};
</script>

<style scoped></style>
