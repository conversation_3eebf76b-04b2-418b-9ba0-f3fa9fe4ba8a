<template>
  <el-form-item :label="i18nt('designer.setting.optionMedium')" v-if="show">
    <el-select v-model="optionModel.optionMedium" filterable>
      <el-option
        v-for="item in options"
        :key="item.dictType"
        :label="item.dictName"
        :value="item.dictType"
      />
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@@/utils/i18n';
import { OptionTypeValue } from '@@/constants/setting-panel';
import { listType } from '@/api/system/dict/type';
export default {
  name: 'OptionMediumEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      show: false,
      options: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100
      },
    };
  },
  watch: {
    'optionModel.optionType': {
      handler(val) {
        this.show = val === OptionTypeValue.medium;
      },
      immediate: true,
    },
  },
  mounted() {
    console.log('object :>> getList', this.getList());
  },
  methods: {
    /** 查询字典类型列表 */
    getList() {
      listType(this.queryParams).then(response => {
        this.options = response.rows;
      });
    },
  },
};
</script>

<style scoped></style>
