<template>
  <el-form-item label="边距">
    <el-select v-model="marginConfig.type">
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-slider v-model="marginConfig.num" @change="handleChange"></el-slider>
  </el-form-item>
</template>

<script>
import { marginTypeOption } from '@@/constants/setting-panel.js';

export default {
  name: 'MarginEditor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      marginConfig: {
        type: '',
        num: 0,
      }
    }
  },
  computed: {
    options() {
      return marginTypeOption;
    },
  },
  mounted() {
    const margin = this.optionModel.margin
    if (margin) {
        this.marginConfig = {
          type: margin.type,
          num: margin.num,
        }
    }
  },
  methods: {
    handleChange() {
      this.$set(this.optionModel, 'margin', this.marginConfig)
    }
  }
};
</script>

<style lang="scss" scoped></style>
