<template>
  <div v-if="optionModel.displaySettings">
    <el-form-item label="回显设置">
      <el-select v-model="optionModel.displaySettings.type" @change="handleChange">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="回显字段">
      <el-input v-model="optionModel.displaySettings.field" type="text" v-if="optionModel.displaySettings.type === displaySettingsType.Query" />
      <el-select v-model="optionModel.displaySettings.field" @change="handleChange" v-else>
        <el-option
          v-for="item in userSettingsOption"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="回显同步字段">
      <el-input v-model="optionModel.displaySettings.syncField" type="text" />
    </el-form-item>
  </div>
</template>

<script>
import { DisplaySettingsType, DisplaySettingsOption, UserSettingsOption } from '@@/constants/display-settings.js';
export default {
  name: 'DisplaySettingsEditor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  computed: {
    options() {
      return DisplaySettingsOption;
    },
    displaySettingsType() {
      return DisplaySettingsType;
    },
    userSettingsOption() {
      return UserSettingsOption;
    },
  },
  methods: {
    handleChange(value) {
      // const selectedId = this.designer.selectedId
      // const standardTableIndx = this.designer.widgetList.findIndex(item => item.type === 'standardTable');
      // const curtIndex = this.designer.widgetList[standardTableIndx].widgetList.findIndex(item => item.id === selectedId)
      // if (standardTableIndx !== -1 && curtIndex !== -1) {
      //   this.designer.widgetList[standardTableIndx].widgetList[curtIndex].rows = BaseTemplateFormJson[value]
      // }
    },
  },
};
</script>

<style scoped></style>
