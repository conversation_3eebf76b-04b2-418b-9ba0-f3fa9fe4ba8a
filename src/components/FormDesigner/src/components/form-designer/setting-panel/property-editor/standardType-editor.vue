<template>
  <div>
    <el-form-item label="标准编号">
      <el-select v-model="optionModel.standardType" @change="handleStandardTypeChange">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@@/utils/i18n';
import { StandardTableTypeOption, StandardTableFormJson } from '@@/constants/standard-table.js';
export default {
  name: 'StandardTypeEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  computed: {
    options() {
      return StandardTableTypeOption;
    },
  },
  methods: {
    handleStandardTypeChange(value) {
      const selectedId = this.designer.selectedId
      const curtIndex = this.designer.widgetList.findIndex(item => item.id === selectedId)
      this.designer.widgetList[curtIndex].widgetList = StandardTableFormJson[value]
    },
  },
};
</script>

<style scoped></style>
