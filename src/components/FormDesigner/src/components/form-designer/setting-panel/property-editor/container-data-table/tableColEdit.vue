<template>
  <div>
    <!-- 表格列编辑对话框 -->
    <el-dialog
      v-if="dialogVisible"
      v-dialog-drag
      class="small-padding-dialog"
      :title="i18nt('designer.setting.tableColEdit')"
      :visible.sync="dialogVisible"
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      :destroy-on-close="true"
      width="1500px"
    >
      <el-table
        ref="singleTable"
        :data="optionModel.tableColumns"
        :cell-style="{ padding: '12px 4px' }"
        height="500"
        border
        size="medium"
        row-key="columnId"
        stripe
        style="width: 100%"
      >
        <!-- 序号列 -->
        <el-table-column type="index" width="55" fixed="left" />

        <!-- 显示名称 -->
        <el-table-column :label="i18nt('designer.setting.columnLabel')">
          <template #default="scope" >
            <el-select v-model="scope.row.label" size="mini" placeholder="请选择"  @change="handleLabelChange($event, scope.row)">
              <el-option
                v-for="item in propertyList"
                :key="item.propertyDispname"
                :label="item.propertyDispname"
                :value="item.propertyDispname"
              />
            </el-select>
          </template>
        </el-table-column>

        <!-- 列名称 -->
        <el-table-column :label="i18nt('designer.setting.columnName')">
          <template #default="scope">
            <el-input readonly v-model="scope.row.prop" size="mini" />
          </template>
        </el-table-column>

        <!-- 列类型 -->
        <el-table-column :label="i18nt('designer.setting.columnType')">
          <template #default="scope">
            <el-input readonly v-model="scope.row.propertyType" size="mini" />
          </template>
        </el-table-column>

        <!-- 列宽 -->
        <el-table-column :label="i18nt('designer.setting.columnWidth')">
          <template #default="scope">
            <el-input-number
              style="width: 90%"
              v-model="scope.row.width"
              size="mini"
              :min="0"
              :max="1000"
              controls-position="right"
            />
          </template>
        </el-table-column>

        <!-- 是否显示 -->
        <el-table-column :label="i18nt('designer.setting.visibleColumn')">
          <template #default="scope">
            <el-switch v-model="scope.row.show" size="mini" />
          </template>
        </el-table-column>

        <!-- 是否排序 -->
        <el-table-column :label="i18nt('designer.setting.sortableColumn')">
          <template #default="scope">
            <el-switch v-model="scope.row.sortable" size="mini" />
          </template>
        </el-table-column>

        <!-- 后端排序 -->
        <!-- <el-table-column :label="i18nt('designer.setting.customSortColumn')">
          <template #default="scope">
            <el-switch v-model="scope.row.customSort" size="mini" :disabled="!scope.row.sortable" />
          </template>
        </el-table-column> -->

        <!-- 是否固定 -->
        <el-table-column :label="i18nt('designer.setting.fixedColumn')">
          <template #default="scope">
            <el-select v-model="scope.row.fixed" size="mini">
              <el-option label="不固定" value="" />
              <el-option label="左侧" value="left" />
              <el-option label="右侧" value="right" />
            </el-select>
          </template>
        </el-table-column>

        <!-- 对齐方式 -->
        <el-table-column :label="i18nt('designer.setting.alignTypeOfColumn')">
          <template #default="scope">
            <el-select v-model="scope.row.align" size="mini">
              <el-option label="左侧" value="left" />
              <el-option label="居中" value="center" />
              <el-option label="右侧" value="right" />
            </el-select>
          </template>
        </el-table-column>

        <!-- 格式化 -->
        <!-- <el-table-column :label="i18nt('designer.setting.formatOfColumn')">
          <template #default="scope">
            <el-select v-model="scope.row.format" size="mini">
              <el-option label="-" value="" />
              <el-option-group label="Date">
                <el-option
                  v-for="fmt in dateFormatOptions"
                  :key="fmt.value"
                  :label="fmt.label"
                  :value="fmt.value"
                />
              </el-option-group>
              <el-option-group label="Number">
                <el-option
                  v-for="fmt in numberFormatOptions"
                  :key="fmt.value"
                  :label="fmt.label"
                  :value="fmt.value"
                />
              </el-option-group>
            </el-select>
          </template>
        </el-table-column> -->

        <!-- 动态渲染 -->
        <!-- <el-table-column :label="i18nt('designer.setting.customRenderGroup')" width="120">
          <template #default="scope">
            <el-button type="text" size="mini" @click="showRenderDialog(scope.row)">
              {{ i18nt('designer.setting.editAction') }}
            </el-button>
          </template>
        </el-table-column> -->

        <!-- 操作列 -->
        <el-table-column :label="i18nt('designer.setting.actionColumn')" width="100" fixed="right">
          <template #default="scope">
            <el-row type="flex" class="row-bg">
              <el-col :span="12">
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-plus"
                  @click="addCol(scope.$index + 1)"
                />
              </el-col>
              <el-col :span="12">
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-delete"
                  @click="deleteCol(scope.$index)"
                />
              </el-col>
            </el-row>
          </template>
        </el-table-column>
      </el-table>

      <!-- 对话框底部 -->
      <template #footer>
        <div class="dialog-footer">
          <!-- <el-button type="primary" size="medium" @click="saveTableColumns">
            {{ i18nt('designer.hint.confirm') }}
          </el-button> -->
          <el-button size="medium" @click="cancelEdit">
            {{ i18nt('designer.hint.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 自定义渲染对话框 -->
    <el-dialog
      v-if="showRenderDialogFlag"
      :title="i18nt('designer.setting.customRender')"
      :visible.sync="showRenderDialogFlag"
      width="800px"
    >
      <el-alert
        type="info"
        :closable="false"
        :title="i18nt('designer.setting.renderFunctionHelp')"
      />
      <code-editor v-model="renderJson" mode="javascript" :readonly="false" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" size="medium" @click="saveColumnRender">
            {{ i18nt('designer.hint.confirm') }}
          </el-button>
          <el-button size="medium" @click="showRenderDialogFlag = false">
            {{ i18nt('designer.hint.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import i18n from '@@/utils/i18n';
import CodeEditor from '@@/components/code-editor/index';
import Sortable from 'sortablejs';

export default {
  name: 'TableColEdit',

  components: {
    CodeEditor,
  },

  mixins: [i18n],

  props: {
    designer: {
      type: Object,
      required: true,
    },
    optionModel: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      dialogVisible: false,
      showRenderDialogFlag: false,
      currentTableColumn: null,
      renderJson: '',

      // 日期格式化选项
      dateFormatOptions: [
        { value: 'd1', label: 'YYYY-MM-DD' },
        { value: 'd2', label: 'YYYY/MM/DD' },
        { value: 'd3', label: 'YYYY年MM月DD日' },
        { value: 'd4', label: 'YYYY-MM-DD HH:mm:ss' },
        { value: 'd5', label: 'YYYY-MM-DD hh:mm:ss' },
      ],

      // 数字格式化选项
      numberFormatOptions: [
        { value: 'n1', label: '自动保留小数位' },
        { value: 'n2', label: '至少保留2位小数' },
        { value: 'n3', label: '保留6位小数' },
        { value: 'n4', label: '保留3位小数' },
        { value: 'n5', label: '保留2位小数' },
        { value: 'n6', label: '整数' },
        { value: 'n7', label: '百分比' },
      ],

      // 添加临时存储原始数据
      originalColumns: null,
      propertyList: [],
    };
  },
  computed: {
  },
  watch: {
    dialogVisible: {
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            const { selectedId, nestTablePropertyList } = this.designer;
            this.propertyList = nestTablePropertyList[selectedId] || [];
            this.initSortable();
          });
        }
      },
      immediate: true,
    },
  },

  mounted() {},

  methods: {
    handleLabelChange(row, rowData) {
      const property = this.propertyList.find(item => item.propertyDispname === row);
      const { propertyName, propertyType, dataSpecs } = property;
      rowData.prop = propertyName;
      rowData.propertyType = propertyType;
      if (dataSpecs) {
        try {
          rowData.dataSpecs = JSON.parse(dataSpecs);
        } catch (error) {
          console.error('dataSpecs parse error:', error);
        }
      }
    },
    // 显示渲染函数编辑对话框
    showRenderDialog(column) {
      this.currentTableColumn = column;
      this.renderJson = column.render || '';
      this.showRenderDialogFlag = true;
    },

    // 保存列渲染函数
    saveColumnRender() {
      this.$set(this.currentTableColumn, 'render', this.renderJson);
      this.showRenderDialogFlag = false;
    },

    // 删除列
    deleteCol(index) {
      this.optionModel.tableColumns.splice(index, 1);
      this.designer.emitHistoryChange();
    },

    // 添加列
    addCol(index) {
      const newColumn = {
        columnId: new Date().getTime(),
        name: '',
        label: '',
        width: 100,
        show: true,
        sortable: false,
        customSort: false,
        fixed: '',
        align: 'left',
        format: '',
        propertyType: 'TEXT',
      };
      this.optionModel.tableColumns.splice(index, 0, newColumn);
      this.designer.emitHistoryChange();
    },

    // 初始化拖拽排序
    initSortable() {
      const tbody = this.$refs.singleTable.$el.querySelector('.el-table__body-wrapper tbody');
      if (!tbody) return;

      Sortable.create(tbody, {
        handle: '.el-table__row', // 整行可拖拽
        animation: 300,
        onEnd: ({ oldIndex, newIndex }) => {
          const currRow = this.optionModel.tableColumns.splice(oldIndex, 1)[0];
          this.optionModel.tableColumns.splice(newIndex, 0, currRow);
          this.designer.emitHistoryChange();
        },
      });
    },

    // 打开对话框时保存原始数据
    showDialog() {
      this.dialogVisible = true;
      // 深拷贝当前列配置
      this.originalColumns = JSON.parse(JSON.stringify(this.optionModel.tableColumns));
    },

    // 保存表格列配置
    saveTableColumns() {
      // 基础验证
      // if (!this.validateColumns()) {
      //   return
      // }

      try {
        // 更新历史记录
        this.designer.emitHistoryChange();

        // 关闭对话框
        this.dialogVisible = false;

        // 刷新表格
        this.$nextTick(() => {
          this.refreshTableKey();
        });

        // 提示保存成功
        this.$message({
          type: 'success',
          message: this.i18nt('designer.hint.saveSuccess'),
        });
      } catch (error) {
        console.error('Save table columns error:', error);
        this.$message({
          type: 'error',
          message: this.i18nt('designer.hint.saveError'),
        });
      }
    },

    // 取消编辑
    cancelEdit() {
      // 恢复原始数据
      if (this.originalColumns) {
        this.optionModel.tableColumns = JSON.parse(JSON.stringify(this.originalColumns));
      }
      this.dialogVisible = false;
    },

    // 验证列配置
    validateColumns() {
      let valid = true;
      const nameSet = new Set();

      // 验证每一列
      this.optionModel.tableColumns.forEach((column, index) => {
        // 检查必填字段
        if (!column.name) {
          this.$message({
            type: 'error',
            message: `${this.i18nt('designer.hint.columnName')}${index + 1}${this.i18nt('designer.hint.required')}`,
          });
          valid = false;
          return;
        }

        // 检查名称唯一性
        if (nameSet.has(column.name)) {
          this.$message({
            type: 'error',
            message: `${this.i18nt('designer.hint.columnName')}${column.name}${this.i18nt('designer.hint.duplicate')}`,
          });
          valid = false;
          return;
        }
        nameSet.add(column.name);

        // 验证宽度值
        if (column.width && (isNaN(column.width) || column.width < 0)) {
          this.$message({
            type: 'error',
            message: `${this.i18nt('designer.hint.columnWidth')}${index + 1}${this.i18nt('designer.hint.invalid')}`,
          });
          valid = false;
          return;
        }
      });

      return valid;
    },

    // 修改原有的 colSubmit 方法
    colSubmit() {
      this.saveTableColumns();
    },
  },
};
</script>

<style lang="scss" scoped>
.small-padding-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
}

.dialog-footer {
  text-align: right;
  padding: 10px 20px;

  .el-button + .el-button {
    margin-left: 10px;
  }
}

.el-table {
  .cell {
    padding: 0 5px;
  }
}

/* 添加拖拽样式 */
.el-table__row {
  cursor: move;
}

.sortable-ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.sortable-drag {
  background: #fff;
  border: 1px dashed #ccc;
}
</style>
