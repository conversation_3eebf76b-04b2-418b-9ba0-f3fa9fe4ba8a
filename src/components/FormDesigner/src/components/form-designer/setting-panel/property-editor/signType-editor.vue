<template>
  <div>
    <el-divider class="custom-divider-margin-top">{{
      i18nt('designer.setting.optionsSetting')
    }}</el-divider>
    <el-form-item :label="i18nt('designer.setting.signType')">
      <el-select v-model="optionModel.signType">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@@/utils/i18n';
import { SignOptionType } from "@@/constants/setting-panel.js";
export default {
  name: 'SignTypeEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      options: SignOptionType
    };
  },
  methods: {
  },
};
</script>

<style scoped></style>
