<template>
  <div>
    <el-divider class="custom-divider-margin-top">权限设置</el-divider>
    <el-form-item label="权限">
      <el-select v-model="optionModel.permission">
        <el-option
          v-for="item in options"
          :key="item.roleKey"
          :label="item.roleName"
          :value="item.roleKey"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'PermissionEditor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  computed: {
    options() {
      return this.designer.globalOption.roleList || []
    }
  },
  created() {

  },
  methods: {
  },
};
</script>

<style scoped></style>
