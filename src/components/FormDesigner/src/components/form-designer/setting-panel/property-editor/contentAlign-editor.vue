<template>
  <el-form-item label="内容对齐">
    <el-radio-group v-model="optionModel.contentAlign" size="mini" class="radio-group-custom">
      <el-radio-button :label="item.value" v-for="item in radioOption" :key="item.value">
        {{ item.label }}
      </el-radio-button>
    </el-radio-group>
  </el-form-item>
</template>

<script>
import { ContentAlignRadioOption } from '@@/constants/content-align.js';

export default {
  name: 'ContentAlignEditor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  computed: {
    radioOption() {
      return ContentAlignRadioOption;
    },
  },
};
</script>

<style lang="scss" scoped>
.radio-group-custom {
  ::v-deep .el-radio-button__inner {
    padding-left: 12px;
    padding-right: 12px;
  }
}
</style>
