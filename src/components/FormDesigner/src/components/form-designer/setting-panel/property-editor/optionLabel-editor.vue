<template>
  <el-form-item :label="i18nt('designer.setting.optionLabel')" v-if="show">
    <el-input v-model="optionModel.optionLabel" placeholder="请输入" type="text" />
  </el-form-item>
</template>

<script>
import i18n from '@@/utils/i18n';
import { OptionTypeValue } from '@@/constants/setting-panel.js';
export default {
  name: 'OptionLabelEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      show: false,
    }
  },
  watch: {
    'optionModel.optionType': {
      handler(val) {
       this.show = val === OptionTypeValue.dynamic
      },
      immediate: true,
    },
  }
};
</script>

<style scoped></style>
