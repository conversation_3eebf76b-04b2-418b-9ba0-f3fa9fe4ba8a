<template>
  <div>
    <el-form-item label="模板">
      <el-select v-model="optionModel.baseTemplate" @change="handleChange">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import { BaseTemplateOption, BaseTemplateFormJson } from '@@/constants/base-template.js';
export default {
  name: 'BaseTemplateEditor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  computed: {
    options() {
      return BaseTemplateOption;
    },
  },
  methods: {
    handleChange(value) {
      const selectedId = this.designer.selectedId
      const standardTableIndx = this.designer.widgetList.findIndex(item => item.type === 'standardTable');
      const curtIndex = this.designer.widgetList[standardTableIndx].widgetList.findIndex(item => item.id === selectedId)
      if (standardTableIndx !== -1 && curtIndex !== -1) {
        this.designer.widgetList[standardTableIndx].widgetList[curtIndex].rows = BaseTemplateFormJson[value]
      }
    },
  },
};
</script>

<style scoped></style>
