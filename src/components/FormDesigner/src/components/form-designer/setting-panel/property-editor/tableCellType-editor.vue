<template>
  <div>
    <el-divider class="custom-divider-margin-top">单元格设置</el-divider>
    <el-form-item label="单元格类型">
      <el-select v-model="optionModel.tableCellType" @change="handleCallType">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@@/utils/i18n';
import { TableCellType } from '@@/constants/setting-panel.js';
import { deepClone } from '@@/utils/util';
export default {
  name: 'TableCellTypeEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  computed: {
    options() {
      return TableCellType;
    },
  },
  methods: {
    // 类型选中时，更新单元格组件设置
    handleCallType(val) {
      this.designer.updateSelectedWidgetOption(
        'widgetConfig',
        deepClone(this.designer.getFieldWidgetByType(val)),
      );
    },
  },
};
</script>

<style scoped></style>
