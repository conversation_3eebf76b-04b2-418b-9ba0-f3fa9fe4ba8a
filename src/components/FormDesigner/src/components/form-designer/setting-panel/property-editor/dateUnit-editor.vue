<template>
  <div>
    <el-form-item label="日期单位">
      <el-select v-model="optionModel.dateUnit" @change="handleChange">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import i18n from '@@/utils/i18n';
import { DateUnitOption } from '@@/constants/date';
export default {
  name: 'DateUnitEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {};
  },
  computed: {
    options() {
      return DateUnitOption;
    },
  },
  methods: {
    handleChange(value) {
      switch (value) {
        case 'year':
          this.$set(this.optionModel, 'placeholder', '选择年');
          this.$set(this.optionModel, 'format', 'yyyy');
          this.$set(this.optionModel, 'valueFormat', 'yyyy');
          break;
        case 'month':
          this.$set(this.optionModel, 'placeholder', '选择月');
          this.$set(this.optionModel, 'format', 'yyyy-MM');
          this.$set(this.optionModel, 'valueFormat', 'yyyy-MM');
          break;
        case 'week':
          this.$set(this.optionModel, 'placeholder', '选择周');
          this.$set(this.optionModel, 'format', 'yyyy 第 WW 周');
          this.$set(this.optionModel, 'valueFormat', '');
          break;
        case 'date':
          this.$set(this.optionModel, 'placeholder', '选择日期');
          this.$set(this.optionModel, 'format', 'yyyy-MM-dd');
          this.$set(this.optionModel, 'valueFormat', 'yyyy-MM-dd');
          break;
      }
    },
  },
};
</script>

<style scoped></style>
