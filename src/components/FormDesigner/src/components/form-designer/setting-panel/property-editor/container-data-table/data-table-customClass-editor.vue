<!-- DataTableCustomClassEditor.vue -->
<template>
  <div>
    <!-- 基础配置 -->
    <el-form-item :label="i18nt('designer.setting.tableWidth')">
      <el-input style="width: 80%" v-model="optionModel.tableWidth" />
    </el-form-item>

    <el-form-item :label="i18nt('designer.setting.tableHeight')">
      <el-input style="width: 80%" v-model="optionModel.tableHeight" />
    </el-form-item>

    <!-- <el-form-item :label="i18nt('designer.setting.customClass')">
      <el-select
        v-model="optionModel.customClass"
        multiple
        filterable
        allow-create
        default-first-option
      >
        <el-option v-for="(cls, index) in cssClassList" :key="index" :label="cls" :value="cls" />
      </el-select>
    </el-form-item> -->
    <!-- 表格列编辑按钮 -->
    <el-form-item :label="i18nt('designer.setting.tableColEdit')">
      <el-button type="primary" plain @click="openTableColEdit">
        {{ i18nt('designer.setting.editAction') }}
      </el-button>
    </el-form-item>

    <!-- 功能开关配置 -->
    <el-form-item :label="i18nt('designer.setting.showIndex')">
      <el-switch v-model="optionModel.showIndex" />
    </el-form-item>

    <el-form-item :label="i18nt('designer.setting.showCheckBox')">
      <el-switch v-model="optionModel.showCheckBox" />
    </el-form-item>

    <el-form-item :label="i18nt('designer.setting.showPagination')">
      <el-switch v-model="optionModel.showPagination" />
    </el-form-item>

    <!-- 分页对齐方式 -->
    <el-form-item
      v-if="optionModel.showPagination"
      :label="i18nt('designer.setting.paginationAlign')"
    >
      <el-radio-group style="width: 102%" size="mini" v-model="optionModel.paginationAlign" class="radio-group-custom">
        <el-radio-button label="left">
          {{ i18nt('designer.setting.leftAlign') }}
        </el-radio-button>
        <el-radio-button label="center">
          {{ i18nt('designer.setting.centerAlign') }}
        </el-radio-button>
        <el-radio-button label="right">
          {{ i18nt('designer.setting.rightAlign') }}
        </el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item :label="i18nt('designer.setting.smallPagination')">
      <el-switch v-model="optionModel.smallPagination" />
    </el-form-item>

    <!-- <el-form-item :label="i18nt('designer.setting.showSummary')">
      <el-switch v-model="optionModel.showSummary" />
    </el-form-item> -->

    <el-form-item :label="i18nt('designer.setting.stripe')">
      <el-switch v-model="optionModel.stripe" />
    </el-form-item>

    <!-- 行间距配置 -->
    <el-form-item :label="i18nt('designer.setting.rowSpacing')">
      <el-input-number
        v-model="optionModel.rowSpacing"
        :min="0"
        :max="20"
        controls-position="right"
        style="width: 80%"
      />
    </el-form-item>

    <!-- 表格尺寸配置 -->
    <el-form-item :label="i18nt('designer.setting.widgetSize')">
      <el-select v-model="optionModel.tableSize" @change="refreshTableLayout">
        <el-option
          v-for="size in widgetSizes"
          :key="size.value"
          :label="size.label"
          :value="size.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      :label="i18nt('designer.setting.autoColumnWidthDisabled')"
      :title="i18nt('designer.setting.autoColumnWidthDisabled')"
    >
      <el-switch v-model="optionModel.autoColumnWidthDisabled" />
    </el-form-item>

    <!-- 添加TableColEdit组件 -->
    <table-col-edit ref="tableColEditor" :designer="designer" :option-model="optionModel" />
  </div>
</template>

<script>
import i18n from '@@/utils/i18n';
import TableColEdit from './tableColEdit.vue';
export default {
  name: 'data-table-customClass-editor',
  mixins: [i18n],
  components: {
    TableColEdit,
  },
  props: {
    designer: {
      type: Object,
      required: true,
    },
    selectedWidget: {
      type: Object,
      required: true,
    },
    optionModel: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      // CSS类列表
      cssClassList: [],

      // 组件尺寸选项
      widgetSizes: [
        { value: 'medium', label: this.i18nt('designer.setting.medium') },
        { value: 'small', label: this.i18nt('designer.setting.small') },
        { value: 'mini', label: this.i18nt('designer.setting.mini') },
      ],

      // 对话框显示控制
      dialogVisible: false,
      dataDialogVisible: false,
      showRenderDialogFlag: false,

      // 编辑数据
      tableDataOptions: '',
      renderJson: '',
      currentTableColumn: null,
      oldButtonName: '',

      // 数据集列表
      dataSetList: [],
    };
  },

  methods: {
    // 打开设置对话框
    openSetting() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.dragSort();
        this.expandAllTableColumns(this.optionModel.tableColumns);
      });
    },

    // 刷新表格布局
    refreshTableLayout() {
      const tableRef = this.designer.formWidget.getSelectedWidgetRef();
      if (tableRef && tableRef.refreshLayout) {
        this.$nextTick(() => {
          tableRef.refreshLayout();
        });
      }
    },

    // 刷新表格Key
    refreshTableKey() {
      const tableRef = this.designer.formWidget.getSelectedWidgetRef();
      if (tableRef && tableRef.refreshTableKey) {
        this.$nextTick(() => {
          tableRef.refreshTableKey();
        });
      }
    },

    // 加载数据集
    loadDataSet(dsName) {
      this.dataSetList.length = 0;
      if (!dsName) return;

      let container = this.$utils.findContainer(this.designer.formConfig, dsName);
      if (container && container.dataSets) {
        container.dataSets.forEach(ds => {
          this.dataSetList.push({
            name: ds.name,
            remark: ds.remark,
          });
        });
      }
    },

    // 获取数据集列表
    getDataSetList() {
      this.optionModel.dataSetName = '';
      this.loadDataSet(this.optionModel.dsName);
    },

    // 处理树形数据编辑变更
    handleTDEChange(enabled) {
      if (enabled) {
        this.optionModel.rowKey = 'id';
        this.optionModel.childrenKey = 'children';
      } else {
        this.optionModel.rowKey = '';
        this.optionModel.childrenKey = '';
      }
    },

    // 添加打开表格列编辑的方法
    openTableColEdit() {
      this.$refs.tableColEditor.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.radio-group-custom {
  width: 100%;

  .el-radio-button {
    // margin-right: 10px;
  }
}

.small-padding-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
}

.dialog-footer {
  text-align: right;
  padding: 10px 20px;
}
</style>
