<template>
  <el-form-item label="隐藏边框">
    <el-select clearable v-model="optionModel.hiddenBoder">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
  </el-form-item>
</template>

<script>
import { hiddenTableBorderType } from '@@/constants/setting-panel.js';
export default {
  name: 'HiddenBoderEditor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  computed: {
    options() {
      return hiddenTableBorderType
    }
  },
 
  data() {
    return {};
  },
};
</script>

<style scoped></style>
