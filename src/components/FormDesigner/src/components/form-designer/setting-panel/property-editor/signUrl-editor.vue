<template>
  <el-form-item :label="i18nt('designer.setting.signUrl')" v-if="show">
    <el-input v-model="optionModel.signUrl" placeholder="请输入" type="text" />
  </el-form-item>
</template>

<script>
import i18n from '@@/utils/i18n';
import { SignOptionTypeValue } from "@@/constants/setting-panel.js";
export default {
  name: 'signUrlEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      show: false,
    }
  },
  watch: {
    'optionModel.signType': {
      handler(val) {
       this.show = val === SignOptionTypeValue.url
      },
      immediate: true,
    },
  }
};
</script>

<style scoped></style>
