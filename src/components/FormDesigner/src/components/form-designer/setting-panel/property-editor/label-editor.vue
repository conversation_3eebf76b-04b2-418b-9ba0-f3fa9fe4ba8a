<template>
  <el-form-item v-if="!noLabelSetting" :label="i18nt('designer.setting.label')">
    <el-input v-model="optionModel.label" type="text" v-if="!isPropertyList" />
    <el-select :value="optionModel.label" type="text" @change="onSelectProperty" v-else>
      <el-option
        v-for="item in propertyOption"
        :key="item.propertyName"
        :label="item.propertyDispname"
        :value="item.propertyName"
      />
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@@/utils/i18n';
import { getIotPropertiesList } from '@/api/iot/v2/properties/classes-property-controller';
export default {
  name: 'LabelEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    propertyList: Array,
  },
  // inject: ['propertyList'],
  computed: {
    noLabelSetting() {
      return this.selectedWidget.type === 'static-text' || this.selectedWidget.type === 'html-text';
      // || (this.selectedWidget.type === 'divider')
    },
    propertyOption() {
      const { type } = this.selectedWidget;
      // 嵌套表单, 只显示数组类型
      if (['data-table', 'nestTable'].includes(type)) {
        return this.propertyList?.filter?.(item => item.propertyType === 'ARRAY') || [];
      }
      if (type === 'table-th-cell' ) {
        const { selectedParames, nestTablePropertyList } = this.designer;
        const { parentWidgetId } = selectedParames;
        return nestTablePropertyList[parentWidgetId] || this.propertyList || [];
      }
      return this.propertyList;
    },
    isPropertyList() {
      return this.propertyOption?.length > 0;
    },
  },
  methods: {
    // 设置枚举数据
    setEnumData(item, isTableThCell = false) {
      const { propertyName, propertyType, dataSpecs } = item;
      console.log('item---', item);
      // TODO: 枚举类型生成
      if (['ENUM'].includes(propertyType)) {
        const dataSpecsJson = JSON.parse(dataSpecs);
        const handleEnmuData = dataSpecsJson?.enmu?.map?.(el => {
          return {
            value: el.value,
            label: el.name,
          };
        });
        this.$set(this.optionModel, 'optionItems', handleEnmuData);
        if (isTableThCell) {
          this.selectedWidget['options']['widgetConfig']['options']['optionItems'] = handleEnmuData;
        }
      } else {
        const defaultOptionItems = [{ label: 'select 1', value: 1 }];
        this.$set(this.optionModel, 'optionItems', defaultOptionItems);
        if (isTableThCell) {
          this.selectedWidget['options']['widgetConfig']['options']['optionItems'] = defaultOptionItems;
        }
      }
    },
    // 获取数组类型绑定类属性
    async getArrayPropertyList(item) {
      const { dataSpecs } = item;
      try {
        const dataSpecsJson = JSON.parse(dataSpecs);
        const { className, enmu } = dataSpecsJson;
        const res = await getIotPropertiesList({
          className: className,
          pageNum: 1,
          pageSize: 9999,
        })
        this.designer.nestTablePropertyList[this.selectedWidget.id] = res.rows || [];
      } catch (error) {
        console.error('获取数组类型绑定类属性失败', error);
      }
    },
    // 选择属性
    onSelectProperty(value) {
      const { type } = this.selectedWidget;
      const item = this.propertyOption.find(item => item.propertyName === value) || {};
      this.$set(this.optionModel, 'name', item.propertyName);
      this.$set(this.optionModel, 'label', item.propertyDispname);
      console.log('item', item);
      if (['data-table', 'nestTable'].includes(type)) {
        this.getArrayPropertyList(item);
      } else if (type === 'table-th-cell') {
        this.setEnumData(item, true);
      } else {
        this.setEnumData(item);
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
