<template>
  <el-form-item label-width="0" v-if="show">
    <option-items-setting
      :designer="designer"
      :selected-widget="selectedWidget"
    />
  </el-form-item>
</template>

<script>
import i18n from '@@/utils/i18n';
import OptionItemsSetting from '@@/components/form-designer/setting-panel/option-items-setting.vue';
import { OptionTypeValue } from "@@/constants/setting-panel.js";

export default {
  name: 'OptionItemsEditor',
  components: {
    OptionItemsSetting,
  },
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      show: false,
    }
  },
  watch: {
    'optionModel.optionType': {
      handler(val) {
       this.show = val === OptionTypeValue.static
      },
      immediate: true,
    },
  }
};
</script>

<style scoped></style>
