<!-- DataTableShowCheckBoxEditor.vue -->
<template>
  <!-- 这是一个隐藏的组件，用于处理表格复选框的配置 -->
  <div style="display: none">
    <!--
      这个组件被设置为不可见是因为它可能只是作为一个配置处理器，
      实际的UI展示和交互可能在其他组件中进行
    -->
  </div>
</template>

<script>
export default {
  name: 'data-table-showCheckBox-editor',

  props: {
    // 设计器实例
    designer: {
      type: Object,
      required: true
    },

    // 当前选中的组件
    selectedWidget: {
      type: Object,
      required: true
    },

    // 选项数据模型
    optionModel: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      // 这里可以添加内部状态，如果需要的话
    }
  },

  computed: {
    // 获取复选框配置
    checkboxConfig() {
      return {
        show: this.optionModel.showCheckBox || false,
        width: this.optionModel.checkBoxWidth || 55,
        fixed: this.optionModel.checkBoxFixed || 'left',
        highlight: this.optionModel.highlightSelected || false
      }
    }
  },

  watch: {
    // 监听复选框显示状态变化
    'optionModel.showCheckBox'(newVal) {
      this.handleCheckBoxChange(newVal)
    }
  },

  methods: {
    // 处理复选框状态变化
    handleCheckBoxChange(show) {
      if (show) {
        // 启用复选框时的处理
        this.enableCheckBox()
      } else {
        // 禁用复选框时的处理
        this.disableCheckBox()
      }

      // 通知设计器状态变化
      this.notifyDesigner()
    },

    // 启用复选框
    enableCheckBox() {
      // 设置默认配置
      if (!this.optionModel.checkBoxWidth) {
        this.$set(this.optionModel, 'checkBoxWidth', 55)
      }
      if (!this.optionModel.checkBoxFixed) {
        this.$set(this.optionModel, 'checkBoxFixed', 'left')
      }

      // 清空选中状态
      this.$set(this.optionModel, 'selectedRowKeys', [])
      this.$set(this.optionModel, 'selectedRows', [])
    },

    // 禁用复选框
    disableCheckBox() {
      // 清除相关配置
      this.$delete(this.optionModel, 'selectedRowKeys')
      this.$delete(this.optionModel, 'selectedRows')
      this.$delete(this.optionModel, 'checkBoxWidth')
      this.$delete(this.optionModel, 'checkBoxFixed')
      this.$delete(this.optionModel, 'highlightSelected')
    },

    // 通知设计器状态变化
    notifyDesigner() {
      if (this.designer && this.designer.formWidget) {
        // 刷新表格布局
        const tableRef = this.designer.formWidget.getSelectedWidgetRef()
        if (tableRef && tableRef.refreshLayout) {
          this.$nextTick(() => {
            tableRef.refreshLayout()
          })
        }

        // 触发历史记录
        this.designer.emitHistoryChange()
      }
    }
  }
}
</script>