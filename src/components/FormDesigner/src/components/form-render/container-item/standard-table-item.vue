<template>
  <container-item-wrapper :widget="widget">
    <el-row
      v-show="!widget.options.hidden"
      :key="widget.id"
      :ref="widget.id"
      :gutter="widget.options.gutter"
      class="grid-container"
      :class="[customClass]"
    >
      <template v-if="!!widget.widgetList && widget.widgetList.length > 0">
        <template v-for="(subWidget, swIdx) in widget.widgetList">
          <template v-if="'container' === subWidget.category">
            <component
              :is="getComponentByContainer(subWidget)"
              :key="swIdx"
              :widget="subWidget"
              :parent-list="widget.widgetList"
              :index-of-parent-list="swIdx"
              :parent-widget="widget"
              :isPreview="isPreview"
            >
              <!-- 递归传递插槽！！！ -->
              <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
                <slot :name="slot" v-bind="scope" />
              </template>
            </component>
          </template>
          <template v-else>
            <component
              :is="subWidget.type + '-widget'"
              :key="swIdx"
              :field="subWidget"
              :designer="null"
              :parent-list="widget.widgetList"
              :index-of-parent-list="swIdx"
              :parent-widget="widget"
              :isPreview="isPreview"
            >
              <!-- 递归传递插槽！！！ -->
              <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
                <slot :name="slot" v-bind="scope" />
              </template>
            </component>
          </template>
        </template>
      </template>
      <template v-else>
        <el-col>
          <div class="blank-cell">
            <span class="invisible-content">{{ i18nt('render.hint.blankCellContent') }}</span>
          </div>
        </el-col>
      </template>
    </el-row>
  </container-item-wrapper>
</template>

<script>
import emitter from '@@/utils/emitter';
import i18n from '../../../utils/i18n';
import refMixin from '../../../components/form-render/refMixin';
import ContainerItemWrapper from './container-item-wrapper';
import FieldComponents from '@@/components/form-designer/form-widget/field-widget/index';
import containerItemMixin from './containerItemMixin';

export default {
  name: 'StandardTableItem',
  componentName: 'ContainerItem',
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  props: {
    widget: Object,
    isPreview: Boolean,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  created() {
    this.initRefList();
  },
  mounted() {},
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
