<template>
  <container-item-wrapper :widget="widget">
    <div v-show="!widget.options.hidden" :key="widget.id" class="table-container">
      <table :ref="widget.id" class="table-layout" :class="[customClass]">
        <tbody>
          <tr v-for="(row, rowIdx) in widget.rows" :key="`${row.id}-${rowIdx}`">
            <template v-for="(colWidget, colIdx) in row.cols">
              <table-cell-item
                v-if="!colWidget.merged"
                :key="`${row.id}-${rowIdx}-${colIdx}`"
                :widget="colWidget"
                :parent-list="widget.cols"
                :row-index="rowIdx"
                :col-index="colIdx"
                :parent-widget="widget"
                :isPreview="isPreview"
              >
                <!-- 递归传递插槽！！！ -->
                <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
                  <slot :name="slot" v-bind="scope" />
                </template>
              </table-cell-item>
            </template>
            <!-- <td colspan="1" rowspan="1" class="table-action">
              <el-button
                type="text"
                icon="el-icon-plus"
                @click="handleAddRow(rowIdx)"
                v-if="rowIdx === 0"
              >
              </el-button>
              <el-button
                type="text"
                icon="el-icon-minus"
                @click="handleDeleteRow(rowIdx)"
                v-if="rowIdx !== 0"
              >
              </el-button>
            </td> -->
          </tr>
        </tbody>
      </table>
    </div>
  </container-item-wrapper>
</template>

<script>
import emitter from '@@/utils/emitter';
import i18n from '../../../utils/i18n';
import refMixin from '../../../components/form-render/refMixin';
import ContainerItemWrapper from './container-item-wrapper';
import TableCellItem from './table-cell-item';
import containerItemMixin from './containerItemMixin';

export default {
  name: 'NestTableItem',
  componentName: 'ContainerItem',
  components: {
    ContainerItemWrapper,
    TableCellItem,
  },
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  props: {
    widget: Object,
    isPreview: Boolean,
    designer: Object,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  created() {
    this.initRefList();
  },
  mounted() {},
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    handleAddRow() {
      this.designer.copyInsertTableCol(this.widget, 0, 0, 0, true);
      this.handleUpdateWidget()
    },
    handleDeleteRow(rowIdx) {
      this.designer.deleteTableWholeRow(this.widget.rows, rowIdx);
      this.handleUpdateWidget()
    },
    handleUpdateWidget() {
      this.$emit('onUpdateWidget', this.widget);
    },
  },
};
</script>

<style lang="scss" scoped>
div.table-container {
  table.table-layout {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    margin-bottom: 12px;
  }
}

.table-action {
  width: 50px;
  height: 36px;
  display: table-cell;
  padding-left: 12px;
}
</style>
