<template>
  <container-item-wrapper :widget="widget">
    <div v-show="!widget.options.hidden" :key="widget.id" class="table-container">
      <TableItemBase
        :ref="widget.id"
        hiddenMarginBottom
        :isPreview="isPreview"
        :widget="widget"
        :customClass="customClass"
      ></TableItemBase>
    </div>
  </container-item-wrapper>
</template>

<script>
import emitter from '@@/utils/emitter';
import i18n from '../../../utils/i18n';
import refMixin from '../../../components/form-render/refMixin';
import ContainerItemWrapper from './container-item-wrapper';
import TableItemBase from './components/table-item-base';
import containerItemMixin from './containerItemMixin';

export default {
  name: 'BaseInfoTableItem',
  componentName: 'ContainerItem',
  components: {
    ContainerItemWrapper,
    TableItemBase,
  },
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  props: {
    widget: Object,
    isPreview: Boolean,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  created() {
    this.initRefList();
  },
  mounted() {},
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
div.table-container {
}
</style>
