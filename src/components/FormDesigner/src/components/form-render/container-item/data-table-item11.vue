// DataTableWidget.vue
<template>
  <div
    class="collapse-container"
    :class="[selected ? 'selected' : '', customClass]"
    @click.stop="selectWidget(widget)"
  >
    <el-table
      v-loading="loadingFlag"
      :key="tableKey"
      ref="dataTable"
      :class="[customClass]"
      :style="{ width: widget.options.tableWidth }"
      :data="widget.options.tableData"
      :height="tableHeight"
      :border="widget.options.border"
      :show-summary="widget.options.showSummary"
      :row-key="tableRowKey"
      :tree-props="{ children: widget.options.childrenKey }"
      :size="widget.options.tableSize"
      :stripe="widget.options.stripe"
      :cell-style="{ padding: widget.options.rowSpacing + 'px 0' }"
      @select="handleRowSelect"
      @select-all="handleAllSelect"
      @click.native.stop="selectWidget(widget)"
    >
      <!-- 序号列 -->
      <el-table-column
        v-if="widget.options.showIndex"
        type="index"
        width="60"
        fixed="left"
        :label="i18nt('designer.setting.lineNumber')"
      />

      <!-- 选择框列 -->
      <el-table-column
        v-if="widget.options.showCheckBox && !isPreview"
        type="selection"
        :width="selectionWidth"
        fixed="left"
      />

      <!-- 数据列 -->
      <template v-for="(column, index) in tableColumns">
        <table-multi-level-column
          :key="`${column.name}-${index}`"
          :column-schema="column"
          :isPreview="isPreview"
          :table-options="widget.options"
          :column-index="index"
        />
      </template>

      <!-- 操作按钮列 -->
      <template v-if="widget.options.showButtonsColumn">
        <el-table-column
          :fixed="buttonsColumnFixed"
          class-name="data-table-buttons-column"
          align="center"
          :label="widget.options.buttonsColumnTitle"
          :width="widget.options.buttonsColumnWidth"
        >
          <template #default="scope">
            <template v-for="(btn, idx) in widget.options.operationButtons">
              <el-button
                v-if="!btn.hidden"
                :class="['data-table-' + btn.name + '-button']"
                :key="`${btn.name}-${idx}`"
                :type="btn.type"
                :size="btn.size"
                :round="btn.round"
                :disabled="btn.disabled"
              >
                {{ btn.label }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </template>

      <!-- 操作列 -->
      <el-table-column
        v-if="!isPreview"
        :label="i18nt('designer.setting.actionColumn')"
        width="100"
        fixed="right"
      >
        <template #default="scope">
          <el-row type="flex" class="row-bg">
            <el-col :span="12">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-plus"
                @click="addRow(scope.$index + 1)"
              />
            </el-col>
            <el-col :span="12">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-delete"
                @click="deleteRow(scope.$index)"
              />
            </el-col>
          </el-row>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-pagination
      v-if="widget.options.showPagination"
      :align="paginationAlign"
      :small="widget.options.smallPagination"
      :current-page="widget.options.pagination.currentPage"
      :page-sizes="widget.options.pagination.pageSizes"
      :page-size="widget.options.pagination.pageSize"
      :layout="paginationLayout"
      :total="widget.options.tableData.length"
    />
  </div>
</template>

<script>
import TableMultiLevelColumn from '@@/components/form-designer/form-widget/container-widget/table-multi-level-column.vue';
import { formatters } from '@@/utils/formatters';
import i18n from '@@/utils/i18n';

export default {
  name: 'DataTableItem',
  componentName: 'DataTableItem',

  components: {
    TableMultiLevelColumn,
  },

  mixins: [
    i18n,
  ],

  inject: ['refList'],

  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    subFormRowIndex: {
      type: Number,
      default: -1,
    },
    subFormColIndex: {
      type: Number,
      default: -1,
    },
    subFormRowId: {
      type: String,
      default: '',
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    isRender: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      selectAllFlag: false,
      elTableKey: '',
      loadingFlag: false,
    };
  },

  computed: {
    tableColumns() {
      return this.widget.options.tableColumns;
    },
    paginationLayout() {
      return this.widget.options.smallPagination
        ? 'prev, pager, next'
        : 'total, sizes, prev, pager, next, jumper';
    },

    selected() {
      return !this.isRender && !this.isPreview && this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },

    buttonsColumnFixed() {
      return this.widget.options.buttonsColumnFixed === undefined
        ? 'right'
        : !!this.widget.options.buttonsColumnFixed && this.widget.options.buttonsColumnFixed;
    },

    tableHeight() {
      return this.widget.options.tableHeight || undefined;
    },

    selectionWidth() {
      return this.widget.options.showSummary ? 55 : this.widget.options.treeDataEnabled ? 70 : 50;
    },

    tableRowKey() {
      return this.widget.options.treeDataEnabled ? this.widget.options.rowKey : null;
    },

    tableKey() {
      return this.elTableKey || this.widget.id;
    },

    paginationAlign() {
      return this.widget.options.paginationAlign || 'left';
    },
  },

  watch: {
    'widget.options.tableData': {
      handler(newVal) {
        console.log('newVal :>>表格数据更新 ', newVal);
        if (this.isRender) {
          // TODO: 更新表格数据, 暂时方案， 比较影响性能
          // this.$emit('onUpdateWidget', this.widget);
          this.$emit('onUpdateFormData', this.widget.options.name, this.widget.options.tableData);
        }

        // this.$emit('onUpdateSelectedWidget', this.widget);
      },
      deep: true,
      immediate: true,
    },
    // 'widget.options.name': {
    //   handler(newVal) {
    //     console.log('newVal :>>表格数据更新12 ', newVal);
    //     if (!this.isRender && !this.isPreview) {
    //       this.$emit('onUpdateFormData', this.widget.options.name, this.widget.options.tableData);
    //     }

    //     // this.$emit('onUpdateSelectedWidget', this.widget);
    //   },
    //   immediate: true,
    // },
  },

  created() {
    // this.initRefList()
  },

  methods: {
    // 删除行
    deleteRow(index) {
      this.widget.options.tableData.splice(index, 1);
      if (!this.isPreview && !this.isRender) {
        this.designer.emitHistoryChange();
      }
    },

    // 添加行
    addRow(index) {
      let newRow = JSON.parse(JSON.stringify(this.widget.options.tableData[0])) || {};
      Object.keys(newRow).map(key => (newRow[key] = ''));
      this.widget.options.tableData.splice(index, 0, newRow);

      if (!this.isPreview && !this.isRender) {
        this.designer.emitHistoryChange();
      }
    },
    refreshTableKey() {
      this.elTableKey = 'el-table-key-' + generateUUID();
    },

    selectWidget(widget) {
      this.designer.setSelected(widget);
    },

    formatter(row, column, value) {
      return value;
    },

    formatterValue(row, column, value) {
      if (!value) return '';

      if (column.formatS && column.show) {
        return formatters[column.formatS](value);
      }

      return value;
    },

    refreshLayout() {
      this.$refs.dataTable.doLayout();
    },

    getTableColumns() {
      return this.widget.options.tableColumns;
    },

    setChildrenSelected(rows, selected) {
      const childrenKey = this.widget.options.childrenKey;
      rows.map(row => {
        this.toggleSelection(row, selected);
        if (row[childrenKey]) {
          this.setChildrenSelected(row[childrenKey], selected);
        }
      });
    },

    toggleSelection(row, selected) {
      if (!row) return;
      this.$nextTick(() => {
        this.$refs.dataTable.toggleRowSelection(row, selected);
      });
    },

    handleRowSelect(selection, row) {
      const childrenKey = this.widget.options.childrenKey;
      if (selection.some(item => item.id === row.id)) {
        row[childrenKey] && this.setChildrenSelected(row[childrenKey], true);
      } else {
        row[childrenKey] && this.setChildrenSelected(row[childrenKey], false);
      }
    },

    setSelectedFlag(rows, selected) {
      const childrenKey = this.widget.options.childrenKey;
      rows.forEach(row => {
        this.$refs.dataTable.toggleRowSelection(row, selected);
        if (row[childrenKey]) {
          this.setSelectedFlag(row[childrenKey], selected);
        }
      });
    },

    handleAllSelect() {
      this.selectAllFlag = !this.selectAllFlag;
      this.setSelectedFlag(this.widget.options.tableData, this.selectAllFlag);
    },

    getNativeTable() {
      return this.$refs.dataTable;
    },

    getTableData() {
      return this.widget.options.tableData;
    },

    setTableData(data) {
      this.loadingFlag = true;
      this.widget.options.tableData = data;
      this.loadingFlag = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.selected {
  outline: 2px solid $--color-primary !important;
}

.data-table-buttons-column {
  // 操作按钮列样式
}
</style>
