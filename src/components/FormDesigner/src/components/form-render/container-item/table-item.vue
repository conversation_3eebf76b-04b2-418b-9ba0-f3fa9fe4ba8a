<template>
  <container-item-wrapper :widget="widget">
    <div v-show="!widget.options.hidden" :key="widget.id" class="table-container">
      <TableItemBase
        :ref="widget.id"
        :widget="widget"
        :isPreview="isPreview"
        :customClass="customClass"
      ></TableItemBase>
    </div>
  </container-item-wrapper>
</template>

<script>
import emitter from '@@/utils/emitter';
import i18n from '../../../utils/i18n';
import refMixin from '../../../components/form-render/refMixin';
import ContainerItemWrapper from './container-item-wrapper';
import TableItemBase from './components/table-item-base';
import containerItemMixin from './containerItemMixin';

export default {
  name: 'TableItem',
  componentName: 'ContainerItem',
  components: {
    ContainerItemWrapper,
    TableItemBase,
  },
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  props: {
    widget: Object,
    isPreview: Boolean,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  created() {
    this.initRefList();
  },
  mounted() {},
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
div.table-container {
  table.table-layout {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    margin-bottom: 12px;
    border: 1px solid #c8ebfb;
  }
}
</style>
