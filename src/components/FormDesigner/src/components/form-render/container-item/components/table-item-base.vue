<template>
  <table
    :ref="widget.id"
    class="table-layout"
    :style="{
      'margin-bottom': !hiddenMarginBottom ? '12px' : 0 ,
      'border-collapse': 'collapse'
    }"
    :class="[ tableBorderClass, customClass]"
  >
    <tbody>
      <tr v-for="(row, rowIdx) in widget.rows" :key="row.id">
        <template v-for="(colWidget, colIdx) in row.cols">
          <table-cell-item
            v-if="!colWidget.merged"
            :key="colIdx"
            :widget="colWidget"
            :parent-list="widget.cols"
            :row-index="rowIdx"
            :col-index="colIdx"
            :parent-widget="widget"
            :isPreview="isPreview"
          >
            <!-- 递归传递插槽！！！ -->
            <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
              <slot :name="slot" v-bind="scope" />
            </template>
          </table-cell-item>
        </template>
      </tr>
    </tbody>
  </table>
</template>
<script>
import TableCellItem from '../table-cell-item';
import { TableBorderType } from '@@/constants/setting-panel.js';
export default {
  props: {
    isPreview: Boolean,
    widget: {
      type: Object,
      default: () => {},
    },
    customClass: {
      type: Array,
      default: () => [],
    },
    hiddenMarginBottom: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    TableCellItem,
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  computed: {
    tableBorderClass() {
      if (this.hiddenBoder === TableBorderType.borderTop) {
        return 'hiddenTopBorder'
      }
      if (this.hiddenBoder === TableBorderType.borderBottom) {
        return 'hiddenTopBorder'
      }
      return  ''
    },
    hiddenBoder() {
      return this.widget.options.hiddenBoder;
    },
  },
  methods: {},
};
</script>
<style scoped lang="scss">
table.table-layout {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border: 1px solid #c8ebfb;
}

.hiddenTopBorder {
  td.table-cell {
    border-top-color: rgba(0,0,0,0) !important;
  }
}
.hiddenBottomBorder {
  td.table-cell {
    border-bottom-color: rgba(0,0,0,0) !important;
  }
}
</style>
