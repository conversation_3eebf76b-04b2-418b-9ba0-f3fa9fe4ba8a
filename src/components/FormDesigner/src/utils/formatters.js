// formatters.js
import dayjs from 'dayjs' // 使用 dayjs 处理日期

/**
 * 检查是否为有效日期
 * @param {string|Date} date 日期值
 * @returns {boolean} 是否为有效日期
 */
function isValidDate(date) {
  let dateObj = new Date(Date.parse(date))
  return dateObj instanceof Date && !isNaN(dateObj.getTime())
}

/**
 * 日期格式化函数集
 */
export const dateFormatters = {
  // YYYY-MM-DD
  d1(date) {
    return isValidDate(date) ? dayjs(date).format('YYYY-MM-DD') : date
  },

  // YYYY/MM/DD
  d2(date) {
    return isValidDate(date) ? dayjs(date).format('YYYY/MM/DD') : date
  },

  // YYYY年MM月DD日
  d3(date) {
    return isValidDate(date) ? dayjs(date).format('YYYY年MM月DD日') : date
  },

  // YYYY-MM-DD HH:mm:ss
  d4(date) {
    return isValidDate(date) ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : date
  },

  // YYYY-MM-DD hh:mm:ss (12小时制)
  d5(date) {
    return isValidDate(date) ? dayjs(date).format('YYYY-MM-DD hh:mm:ss') : date
  }
}

/**
 * 数字格式化函数集
 */
export const numberFormatters = {
  // 根据小数位数自动格式化(最多6位),带千分位
  n1(num) {
    if (typeof num !== 'number') return num

    let decimalLength = num.toString().split('.')[1]?.length || 0
    let fixed = Math.min(decimalLength, 6)
    let formatted = num.toFixed(fixed)

    return addThousandSeparator(formatted)
  },

  // 自动格式化(最少2位,最多6位),带千分位
  n2(num) {
    if (typeof num !== 'number') return num

    let decimalLength = num.toString().split('.')[1]?.length || 0
    let fixed = Math.max(2, Math.min(decimalLength, 6))
    let formatted = num.toFixed(fixed)

    return addThousandSeparator(formatted)
  },

  // 固定6位小数,带千分位
  n3(num) {
    if (typeof num !== 'number') return num
    return addThousandSeparator(num.toFixed(6))
  },

  // 固定3位小数,带千分位
  n4(num) {
    if (typeof num !== 'number') return num
    return addThousandSeparator(num.toFixed(3))
  },

  // 固定2位小数,带千分位
  n5(num) {
    if (typeof num !== 'number') return num
    return addThousandSeparator(num.toFixed(2))
  },

  // 固定0位小数(整数),带千分位
  n6(num) {
    if (typeof num !== 'number') return num
    return addThousandSeparator(num.toFixed(0))
  },

  // 百分比格式化(2-4位小数),带千分位
  n7(num) {
    if (typeof num !== 'number') return num

    let decimalLength = num.toString().split('.')[1]?.length || 0
    let percentage = num * 100

    let fixed
    switch(decimalLength) {
      case 0:
      case 1:
      case 2:
        fixed = 2
        break
      case 3:
        fixed = 3
        break
      default:
        fixed = 4
    }

    return addThousandSeparator(percentage.toFixed(fixed)) + '%'
  }
}

/**
 * 添加千分位分隔符
 * @param {string} numStr 数字字符串
 * @returns {string} 添加千分位后的字符串
 */
function addThousandSeparator(numStr) {
  return numStr.replace(/\d+/, function(n) {
    return n.replace(/(\d)(?=(\d{3})+$)/g, '$1,')
  })
}

// // 导出所有格式化函数
// export const formatters = {
//   ...dateFormatters,
//   ...numberFormatters
// }