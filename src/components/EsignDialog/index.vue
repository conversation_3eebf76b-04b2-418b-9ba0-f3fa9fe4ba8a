<template>
  <div class="">
    <div
      class="sign-layout"
      @click="handleSignDialog"
    >
      <img  class="sign-img" :src="value" width="100%" v-if="value" />
      <i class="el-icon-edit" v-else></i>
    </div>
    <el-dialog
      v-dialog-drag
      title="签名"
      :visible.sync="showSignDialog"
      append-to-body
      width="50%"
      :show-close="true"
      custom-class="drag-dialog small-padding-dialog"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :destroy-on-close="true"
    >
      <vue-esign
        ref="esign"
        :width="1000"
        :height="300"
        :style="'width:100%;margin: 0 auto;border: 1px solid #bfbdbd;'"
        :isCrop="signOption.isCrop"
        :lineWidth="signOption.lineWidth"
        :lineColor="signOption.lineColor"
        :bgColor.sync="signOption.bgColor"
      />
      <div slot="footer">
        <el-button @click="handleReset">清空画板</el-button>
        <el-button type="primary" @click="handleGenerate">生成图片</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { base64ToBlob } from "@@/utils/util";
import { postFileUpload } from "@/api/file";
import VueEsign from 'vue-esign'
export default {
  props: {
    signValue: String,
    value: String,
  },
  components: {
    VueEsign
  },
  data() {
    return {
      showSignDialog: false,
      signOption: {
        lineWidth: 6,
        lineColor: '#000000',
        bgColor: '',
        resultImg: '',
        isCrop: false
      }
    };
  },
  created() {},
  mounted() {},
  computed: {},
  methods: {
    handleSignDialog() {
      this.showSignDialog = true;
    },
    handleReset () {
      this.$refs.esign.reset()
    },
    handleGenerate () {
      this.$refs.esign.generate().then(async res => {
        let formData = new FormData();
        formData.append("file", base64ToBlob(res));
        formData.append("fileName", `sign-${new Date().getTime()}`);
        const result = await postFileUpload(formData);
        const signValue = result?.data?.url || ''
        this.$emit('change', signValue)
        this.$emit('input', signValue)
        this.showSignDialog = false;
      }).catch(err => {
        console.log('上传签名失败', err);
        this.$message.warning('请签名')
      })
    }
  },
};
</script>
<style scoped lang="scss">
.sign-layout {
  width: 100%;
  min-height: 50px;
  display: flex;
  align-items: flex-start;
  justify-content: left;
  cursor: pointer;

  .el-icon-edit {
    margin-top: 12px;
  }

  .sign-img {
    width: 200px;
  }
}
</style>
