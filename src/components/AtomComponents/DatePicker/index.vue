<template>
  <el-form-item v-bind="atomConfig">
    <el-date-picker
      :value="value"
      v-bind="atomConfig.options"
      :type="pickerType"
      :value-format="valueFormat"
      :format="displayFormat"
      :picker-options="pickerOptions"
      :placeholder="placeholder"
      :start-placeholder="startPlaceholder"
      :end-placeholder="endPlaceholder"
      :range-separator="rangeSeparator"
      :clearable="clearable"
      @input="handleInput"
      @change="handleChange"
    />
  </el-form-item>
</template>

<script>
import atomComponentsMixin from '@/mixin/atomComponents.js'

export default {
  name: 'DatePicker',
  mixins: [atomComponentsMixin],

  computed: {
    // 日期选择器类型
    pickerType() {
      return this.atomConfig.options?.type || 'date'
    },

    // 值格式化
    valueFormat() {
      return this.atomConfig.options?.valueFormat || 'yyyy-MM-dd'
    },

    // 显示格式化
    displayFormat() {
      return this.atomConfig.options?.format || 'yyyy-MM-dd'
    },

    // 选择器配置项
    pickerOptions() {
      return {
        // 快捷选项
        shortcuts: this.atomConfig.options?.shortcuts || [
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', new Date())
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', date)
            }
          },
          {
            text: '一周前',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            }
          }
        ],
        // 禁用日期
        disabledDate: this.atomConfig.options?.disabledDate
      }
    },

    // 占位符
    placeholder() {
      return this.atomConfig.options?.placeholder || '请选择日期'
    },

    // 范围选择的开始占位符
    startPlaceholder() {
      return this.atomConfig.options?.startPlaceholder || '开始日期'
    },

    // 范围选择的结束占位符
    endPlaceholder() {
      return this.atomConfig.options?.endPlaceholder || '结束日期'
    },

    // 范围分隔符
    rangeSeparator() {
      return this.atomConfig.options?.rangeSeparator || '-'
    },

    // 是否可清空
    clearable() {
      return this.atomConfig.options?.clearable !== false
    }
  },

  methods: {
    // 处理日期变化
    handleChange(val) {
      // 触发自定义的字段变化事件
      this.$emit('onFieldChange', {
        prop: this.atomConfig.prop,
        value: val
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-date-editor {
  width: 100%;
}
</style>
