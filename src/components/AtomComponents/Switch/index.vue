<template>
  <el-form-item v-bind="atomConfig">
    <el-switch
      :value="value"
      v-bind="atomConfig.options"
      @change="handleChange"
    >
    </el-switch>
  </el-form-item>
</template>
<script>
import atomComponentsMixin from '@/mixin/atomComponents.js';
export default {
  name: 'Switch',
  mixins: [atomComponentsMixin],
  data() {
    return {};
  },
  computed: {
  },
  created() {},
  mounted() {},
  methods: {
    handleChange(value) {
     this.handleInput(value);
      const { associationProp, dictType } = this.atomConfig;
      if (associationProp && dictType) {
        this.$emit('onFieldChange', {
          prop: associationProp,
          value,
        });
      }
    }
  },
};
</script>
<style scoped lang="scss"></style>
