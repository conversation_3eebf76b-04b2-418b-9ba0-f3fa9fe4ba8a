<template>
  <el-form-item v-bind="atomConfig">
    <treeselect
      v-model="formData[atomConfig.prop]"
      :options="deptOptions"
      :normalizer="normalizer"
      v-bind="atomConfig.options"
      :clearable="false"
      @select="handleSelect"
      @deselect="handleClear"
    />
  </el-form-item>
</template>
<script>
import atomComponentsMixin from "@/mixin/atomComponents.js";
import { listDept } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "SelectDept",
  mixins: [atomComponentsMixin],
  components: {
    Treeselect,
  },
  data() {
    return {
      deptOptions: [],
      parentId: null,
      selectDept: {},
    };
  },
  computed: {},
  created() {
    this.getDeptOptions();
  },
  mounted() {
     this.isSearch && this.handleEventBus();
  },
  beforeDestroy() {
     this.isSearch && this.offEventBus();
  },
  methods: {
     handleEventBus() {
      this.offEventBus();
      this.$eventBus.$on("resetSelectDept", () => {
        this.resetValue();
      });
    },

    offEventBus() {
      this.$eventBus?.$off?.("resetSelectDept");
    },
    resetValue() {
      this.parentId = null;
    },
    handleEnter(item) {},
    handleSelect(item) {
      console.log("选中", item);
      this.selectDept = item;
      const { prop, associationProp } = this.atomConfig;

      this.$emit("onFieldChange", {
        prop,
        value: this.selectDept.deptId || "",
      });
      if (associationProp) {
        this.$emit("onFieldChange", {
          prop: associationProp,
          value: this.selectDept.deptName || "",
        });
      }
    },
    handleClear() {
      const { prop, associationProp } = this.atomConfig;
      this.$emit("onFieldChange", {
        prop,
        value: "",
      });
      if (associationProp) {
        this.$emit("onFieldChange", {
          prop: associationProp,
          value: "",
        });
      }
    },
    getDeptOptions() {
      listDept().then((res) => {
        this.deptOptions = this.handleTree(res.data, "deptId") || [];
        this.initDeptData();
      });
    },
    initDeptData() {
      const { prop, associationProp } = this.atomConfig;
      const iniData = this.initFormData ? this.initFormData[prop] : null;
      if (!iniData) return;
      this.parentId = iniData;
      this.selectDept = {
        deptId: iniData,
        deptName: this.initFormData[associationProp],
      };
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
  },
};
</script>
<style scoped lang="scss"></style>
