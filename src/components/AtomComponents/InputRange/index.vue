<template>
  <el-form-item v-bind="atomConfig">
    <div class="input-range">
      <el-form-item class="child-inline" v-bind="atomConfig.itemOptions[0]">
        <el-input
          v-model="minValue"
          :style="atomConfig.itemOptions[0].style"
          v-bind="atomConfig.itemOptions[0].options"
          @input="handleMinInput"
        />
      </el-form-item>
      <span class="input-range-sign">~</span>
      <el-form-item v-bind="atomConfig.itemOptions[1]">
        <el-input
          v-model="maxValue"
          :style="atomConfig.itemOptions[0].style"
          v-bind="atomConfig.itemOptions[1].options"
          @input="handleMaxInput"
        />
      </el-form-item>
    </div>
  </el-form-item>
</template>
<script>
import atomComponentsMixin from '@/mixin/atomComponents.js';
export default {
  name: 'InputRange',
  mixins: [atomComponentsMixin],
  props: {},
  data() {
    return {
      minValue: '',
      maxValue: '',
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.minValue = this.initFormData[this.atomConfig.itemOptions[0].prop];
    this.maxValue = this.initFormData[this.atomConfig.itemOptions[1].prop];

    console.log('object :>> mounted', this.minValue, this.maxValue);
  },
  methods: {
    handleMinInput(value) {
      this.$emit('onFieldChange', {
        prop: this.atomConfig.itemOptions[0].prop,
        value: value,
      });
    },

    handleMaxInput(value) {
      this.$emit('onFieldChange', {
        prop: this.atomConfig.itemOptions[1].prop,
        value: value,
      });
    },
  },
};
</script>
<style scoped lang="scss">
.input-range {
  display: flex;
  align-items: center;

  &-sign {
    margin: 0 12px;
  }

  ::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
}
</style>
