<template>
  <el-form-item v-bind="atomConfig">
    <el-select
      :value="value"
      style="width: 100%"
      v-bind="atomConfig.options"
      @change="handleSelectChange"
      :loading="loading"
    >
      <el-option
        v-for="optionItem in options"
        :key="optionItem.value"
        v-bind="optionItem"
      />
    </el-select>
  </el-form-item>
</template>

<script>
import atomComponentsMixin from '@/mixin/atomComponents.js';

export default {
  name: 'Select',
  mixins: [atomComponentsMixin],
  inject: {
    dictData: { value: [], default: [] },
  },
  data() {
    return {
      loading: false,
      apiOptions: [], // 存储API获取的选项
    };
  },
  computed: {
    options() {
      const { dictType, apiConfig } = this.atomConfig;

      // 优先使用API获取的选项
      if (apiConfig) {
        return this.apiOptions;
      }

      // 其次使用字典数据
      if (dictType) {
        return this.dictData.type[dictType] || [];
      }

      // 最后使用静态配置
      return this.atomConfig?.option || [];
    },
  },
  watch: {
    'atomConfig.apiConfig': {
      immediate: true,
      handler: 'fetchOptions'
    }
  },
  methods: {
    async fetchOptions() {
      const { apiConfig } = this.atomConfig;
      if (!apiConfig) return;

      const { api, params = {} } = apiConfig;
      const { value = 'value', label = 'label' } = params;

      try {
        this.loading = true;
        const res = await api({
          page: 1,
          pageSize: 10000,
          ...params.queryParams,
        }|| {});

        // 转换API返回的数据格式
        this.apiOptions = (res.rows || res.data || []).map(item => ({
          value: item[value],
          label: item[label],
          ...item // 保留原始数据，以备他用
        }));
      } catch (error) {
        console.error('获取选项数据失败:', error);
        this.apiOptions = [];
      } finally {
        this.loading = false;
      }
    },

    handleSelectChange(value) {
      console.log('value12', value)
      this.handleInput(value);
      const { associationProp, dictType, apiConfig, associationFilds } = this.atomConfig;

      // 处理关联字段
      if (associationProp) {
        let associatedValue;
        if (dictType) {
          associatedValue = this.dictData.label[dictType][value];
        } else if (apiConfig) {
          const selectedOption = this.apiOptions.find(opt => opt.value === value);
          associatedValue = selectedOption?.label;
        }

        if (associatedValue !== undefined) {
          this.$emit('onFieldChange', {
            prop: associationProp,
            value: associatedValue,
          });
        }
      }

      // 处理联动字段
      if (value && associationFilds && associationFilds.length !== 0) {
        const itemOption = this.options.find(item => item.value === value)
        console.log('first', associationFilds)
        associationFilds.forEach( key => {
           this.$emit('onFieldChange', {
            prop: key,
            value: itemOption[key],
          });
        });
      }
    },
  },
};
</script>
