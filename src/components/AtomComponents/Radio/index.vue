<template>
  <el-form-item v-bind="atomConfig">
    <el-radio-group :value="value" v-bind="atomConfig.options" @input="handleSelectChange">
      <el-radio
        v-for="optionItem in options"
        :key="optionItem.value"
        :label="optionItem.value"
        v-bind="optionItem"
      >{{ optionItem.label }}</el-radio>
    </el-radio-group>
  </el-form-item>
</template>
<script>
import atomComponentsMixin from '@/mixin/atomComponents.js';
export default {
  name: 'Radio',
  mixins: [atomComponentsMixin],
  inject: {
    dictData: { value: [], default: [] },
  },
  props: {},
  data() {
    return {};
  },
  computed: {
    options() {
      const { dictType } = this.atomConfig;
      if (!dictType) return this.atomConfig?.option || [];
      if (!this.dictData.type[dictType]) return [];
      return this.dictData.type[dictType].map(item => {
        return {
          ...item,
          //  如果字典 value 值是数字字符，则转换为数字类型
          value: !isNaN(item.value) ? Number(item.value) : item.value,
        };
      });
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleSelectChange(value) {
      this.handleInput(value);
      const { associationProp, dictType } = this.atomConfig;
      if (associationProp && dictType) {
        this.$emit('onFieldChange', {
          prop: associationProp,
          value,
        });
      }
    },
  },
};
</script>
<style scoped lang="scss"></style>
