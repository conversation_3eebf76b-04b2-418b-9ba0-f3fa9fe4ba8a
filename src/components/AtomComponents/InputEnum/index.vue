<template>
  <el-form-item v-bind="atomConfig">
    <div
      v-for="(item, index) in enumList"
      :key="`${index}-input-enum`"
      class="input-enum"
      :class="{ 'mb-16': index !== enumList.length - 1 }"
    >
      <el-form-item class="child-inline" :prop="`item-value-${index}`" :rules="valueRules">
        <el-input
          v-model="item.value"
          type="number"
          placeholder="编号如 0"
          @change="handleItemInput"
        />
      </el-form-item>
      <span class="input-enum-sign">~</span>
      <el-form-item :rules="nameRules" class="child-inline" :prop="`item-name-${index}`">
        <el-input
          v-model="item.name"
          max-length="20"
          placeholder="对该枚举的描述"
          @change="handleItemInput"
        />
      </el-form-item>
      <el-button
        v-if="index !== 0"
        style="margin-left: 12px"
        type="text"
        @click="removeItem(index)"
      >删除</el-button>
    </div>
    <el-tooltip class="item" effect="dark" content="最多添加 100 个" placement="top">
      <el-button
        v-if="enumList.length <= 100"
        icon="el-icon-plus"
        style="margin-top: 6px"
        type="text"
        @click="addItem"
      >添加枚举项</el-button>
    </el-tooltip>
  </el-form-item>
</template>
<script>
import atomComponentsMixin from '@/mixin/atomComponents.js';

export default {
  name: 'InputEnum',
  mixins: [atomComponentsMixin],
  props: {},
  data() {
    const valueRules = [
      {
        required: true,
        validator: (rule, value, callback) => {
          const index = this.getFieldIndex(rule);
          const curtValue = Number(this.enumList[index].value);
          if (!curtValue) {
            callback(new Error('请输入枚举值'));
          }
          if (!Number.isInteger(curtValue) || curtValue < -2147483648 || curtValue > 2147483647) {
            callback(new Error('支持整型，取值范围：-2147483648 ~ 2147483647'));
          }
          callback();
        },
        trigger: ['blur'],
      },
    ];

    const nameRules = [
      {
        required: true,
        validator: (rule, value, callback) => {
          const index = this.getFieldIndex(rule);
          const curtName = this.enumList[index].name;
          if (!curtName) {
            callback(new Error('请输入该枚举描述'));
          }
          const reg = /^(?!_)(?!.*?_$)[a-zA-Z0-9_\u4e00-\u9fa5]+$/;
          if (!reg.test(curtName)) {
            callback(
              new Error(
                '请输入以中文、字母(不区分大小写)、数字、下划线(_)随意组合的字符串,不超过20个字符',
              ),
            );
          }
          callback();
        },
        trigger: ['change'],
      },
    ];
    return {
      valueRules,
      nameRules,
      enumList: [
        {
          name: '',
          value: '',
        },
      ],
    };
  },
  computed: {},
  created() {},
  mounted() {
    if (this.initFormData[this.atomConfig.associationProp]) {
      this.enumList = this.initFormData[this.atomConfig.associationProp];
    }
  },
  methods: {
    removeItem(index) {
      this.enumList.splice(index, 1);
    },

    addItem() {
      this.enumList.push({
        name: '',
        value: '',
      });
    },

    getFieldIndex(rule) {
      const { field } = rule;
      const fields = field.split('-');
      return fields[fields.length - 1];
    },

    handleItemInput() {
      this.$emit('onFieldChange', {
        prop: this.atomConfig.associationProp,
        value: this.enumList,
      });
    },
  },
};
</script>
<style scoped lang="scss">
.input-enum {
  display: flex;
  align-items: flex-start;

  .child-inline {
    width: 180px;
  }

  &-sign {
    margin: 0 12px;
  }

  ::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }

  ::v-deep .el-form-item__error {
    position: relative;
  }
}

.mb-16 {
  margin-bottom: 16px;
}
</style>
