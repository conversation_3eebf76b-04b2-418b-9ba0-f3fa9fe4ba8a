import { formItemJsonTemplate } from '@/constants/jsonTemplate.js';
import { InputDialogDialogJson } from '@/views/iot/classes/constants';

const fields = {
  propertyDispname: {
    label: '参数中文名',
    prop: 'propertyDispname',
  },
  propertyName: {
    label: '标识符',
    prop: 'propertyName',
  },
  propertyType: {
    label: '属性类型',
    prop: 'propertyType',
  },
  range: {
    label: '取值范围',
    prop: 'range',
  },
  min: {
    label: '最小值',
    prop: 'min',
  },
  bool: {
    label: '布尔值',
    prop: 'bool',
  },
  enmu: {
    label: '枚举项',
    prop: 'enmu',
  },
  remark: {
    label: '描述',
    prop: 'remark',
  },
  step: {
    label: '步长',
    prop: 'step',
  },
  unit: {
    label: '单位',
    prop: 'unit',
  },
  length: {
    label: '数据长度',
    prop: 'length',
  },
  itemType: {
    label: '元素类型',
    prop: 'itemType',
  },
  size: {
    label: '元素个数',
    prop: 'size',
  },
  className: {
    label: '选择类',
    prop: 'className',
  },
};

export const DialogJson = {
  type: 'Add',
  title: '参数',
  options: {
    width: '600px',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary',
      },
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: '',
      },
    },
  ],
  formJson: {
    formOption: {
      inline: false,
      labelWidth: '130px',
      labelPosition: 'top',
      size: 'small',
    },
    // 默认表单值
    defaultFormData: {
      propertyType: 'INT',
      itemType: 'INT',
    },
    formItemJson: [
      formItemJsonTemplate({
        label: fields.propertyDispname.label,
        prop: fields.propertyDispname.prop,
      }),
      formItemJsonTemplate({
        label: fields.propertyName.label,
        prop: fields.propertyName.prop,
        rules: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const reg = /^[a-zA-Z]{1}\w*$/;
              if (!reg.test(value)) {
                callback(new Error('请输入以字母(不区分大小写)、数字、下划线(_)随意组合的字符串'));
              }
              callback();
            },
            trigger: ['blur'],
          },
        ],
      }),
      formItemJsonTemplate({
        label: fields.propertyType.label,
        prop: fields.propertyType.prop,
        type: 'Select',
        dictType: 'iot_property_type',
        association: [
          {
            whiteValue: ['INT', 'FLOAT', 'DOUBLE'], // 当前字段满足此值时，显示关联字段配置
            jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
            childItems: [
              formItemJsonTemplate({
                label: fields.range.label,
                type: 'InputRange',
                required: true,
                itemOptions: [
                  {
                    prop: 'min',
                    style: 'width: 198px',
                    rules: [
                      {
                        required: true,
                        validator: (rule, value, callback) => {
                          if (!value) {
                            callback(new Error('请输入最小值'));
                          }
                          callback();
                        },
                        trigger: ['blur'],
                      },
                    ],
                    options: {
                      type: 'number',
                      clearable: false,
                      placeholder: '请输入最小值',
                    },
                  },
                  {
                    prop: 'max',
                    style: 'width: 198px',
                    rules: [
                      {
                        required: true,
                        validator: (rule, value, callback) => {
                          if (!value) {
                            callback(new Error('请输入最大值'));
                          }
                          callback();
                        },
                        trigger: ['blur'],
                      },
                    ],
                    options: {
                      type: 'number',
                      clearable: false,
                      placeholder: '请输入最大值',
                    },
                  },
                ],
              }),

              formItemJsonTemplate({
                label: fields.step.label,
                prop: fields.step.prop,
                options: {
                  type: 'number',
                },
              }),
              formItemJsonTemplate({
                label: fields.unit.label,
                prop: fields.unit.prop,
                type: 'Select',
                associationProp: 'unitName', // 关联字段，如果需要将名称也保存的话
                options: {
                  filterable: true,
                },
                dictType: 'iot_property_unit',
              }),
            ],
          },
          {
            whiteValue: ['ENUM'], // 当前字段满足此值时，显示关联字段配置
            jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
            childItems: [
              formItemJsonTemplate({
                label: fields.enmu.label,
                associationProp: fields.enmu.prop,
                type: 'InputEnum',
                required: true,
              }),
            ],
          },
          {
            whiteValue: ['BOOL'], // 当前字段满足此值时，显示关联字段配置
            jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
            childItems: [
              formItemJsonTemplate({
                label: fields.bool.label,
                associationProp: fields.bool.prop,
                type: 'InputBoole',
                required: true,
                itemOptions: [
                  {
                    prop: '0',
                  },
                  {
                    prop: '1',
                  },
                ],
              }),
            ],
          },
          {
            whiteValue: ['TEXT'], // 当前字段满足此值时，显示关联字段配置
            jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
            childItems: [
              formItemJsonTemplate({
                label: fields.length.label,
                prop: fields.length.prop,
                options: {
                  type: 'number',
                  max: 10240,
                  min: 1,
                  slotAppendName: '字节',
                },
                rules: [
                  {
                    required: true,
                    validator: (rule, value, callback) => {
                      if (!Number.isInteger(Number(value))) {
                        callback(new Error('请输入整型'));
                      }
                      if (Number(value) > 10240) {
                        callback(new Error('请输入1-10240内的数字'));
                      }
                      callback();
                    },
                    trigger: ['blur'],
                  },
                ],
              }),
            ],
          },
          {
            whiteValue: ['STRUCT'], // 当前字段满足此值时，显示关联字段配置
            jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
            childItems: [
              formItemJsonTemplate({
                label: fields.className.label,
                prop: fields.className.prop,
                type: 'InputDialog',
                dialogJson: InputDialogDialogJson,
                associationProp: 'packageName',
              }),
            ],
          },
          {
            whiteValue: ['ARRAY'], // 当前字段满足此值时，显示关联字段配置
            jsonField: 'dataSpecs', // 需要将关联数据放入 jsonField 内
            childItems: [
              formItemJsonTemplate({
                label: fields.itemType.label,
                prop: fields.itemType.prop,
                type: 'Radio',
                options: {
                  type: 'number',
                },
                option: [
                  {
                    value: 'INT',
                    label: 'int32',
                  },
                  {
                    value: 'FLOAT',
                    label: 'float',
                  },
                  {
                    value: 'DOUBLE',
                    label: 'double',
                  },
                  {
                    value: 'TEXT',
                    label: 'text',
                  },
                  {
                    value: 'STRUCT',
                    label: 'struct',
                  },
                ],
              }),
              formItemJsonTemplate({
                label: fields.size.label,
                prop: fields.size.prop,
                options: {
                  type: 'number',
                },
                rules: [
                  {
                    required: true,
                    validator: (rule, value, callback) => {
                      if (!Number.isInteger(Number(value))) {
                        callback(new Error('请输入整数'));
                      }
                      if (Number(value) > 512) {
                        callback(new Error('请输入1-512内的数字'));
                      }
                      callback();
                    },
                    trigger: ['blur'],
                  },
                ],
              }),
              formItemJsonTemplate({
                label: fields.className.label,
                prop: fields.className.prop,
                type: 'InputDialog',
                associationProp: 'packageName',
                showConfig: {
                  field: 'itemType',
                  value: 'STRUCT',
                },
                dialogJson: InputDialogDialogJson,
              }),
            ],
          },
        ],
      }),
    ],
  },
};
