<template>
  <el-form-item v-bind="atomConfig">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="list-item"
      flex="cross:center main:justify"
    >
      <div class="list-item-name ellipsis-1">参数名称：{{ item.propertyDispname }}</div>
      <div>
        <el-button type="text" @click="handlerItemEdit(index)">编辑</el-button>
        <el-divider direction="vertical" />
        <el-button type="text" @click="handlerItemDel(index)">删除</el-button>
      </div>
    </div>
    <el-button type="text" icon="el-icon-plus" @click="handlerClick">添加参数</el-button>
    <CoustomFormDialog
      ref="iotParameDialog"
      :dialog-json="dialogJson"
      :init-data="list[currentIndex] || {}"
      :visible.sync="dialogVisible"
      @onSubmit="handlerSubmit"
      @onDialogClose="handlerDialogClose"
    />
  </el-form-item>
</template>
<script>
import atomComponentsMixin from '@/mixin/atomComponents.js';
import CoustomFormDialog from '@/components/CoustomFormDialog';
import { DialogJson } from './constants';
export default {
  name: 'IotParame',
  components: {
    CoustomFormDialog,
  },
  mixins: [atomComponentsMixin],
  props: {},
  data() {
    return {
      dialogVisible: false,
      dialogJson: DialogJson,
      list: [],
      currentIndex: -1,
    };
  },
  computed: {},
  created() {},
  mounted() {
    if (this.initFormData && Object.keys(this.initFormData.length !== 0)) {
      const { field, basis } = this.atomConfig.echoConfig;
      const echoData = this.initFormData[field] || [];
      this.list = echoData.filter(item => item[basis.field] === basis.value);
      this.$emit('onFieldChange', {
        prop: this.atomConfig.prop,
        value: this.list,
      });
    }
  },
  methods: {
    handlerItemEdit(index) {
      this.currentIndex = index;
      this.dialogJson.type = 'Edit';
      this.$nextTick(() => {
        this.dialogVisible = true;
      });
    },

    handlerItemDel(index) {
      this.list.splice(index, 1);
    },

    handlerClick() {
      this.dialogVisible = true;
    },

    handlerSubmit(formData) {
      if (this.currentIndex === -1) {
        this.list.push(formData);
      } else {
        this.list[this.currentIndex] = formData;
      }
      this.$emit('onFieldChange', {
        prop: this.atomConfig.prop,
        value: this.list,
      });
      this.$refs.iotParameDialog.resetLoading();
      this.handlerDialogClose();
    },

    handlerDialogClose() {
      this.currentIndex = -1;
      this.dialogVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
.list-item {
  height: 36px;
  margin-bottom: 12px;
  &-name {
    width: 84%;
    height: 100%;
    background-color: #ecf5ff;
    padding: 8px;
    line-height: 20px;
    font-size: 12px;
  }
}
</style>
