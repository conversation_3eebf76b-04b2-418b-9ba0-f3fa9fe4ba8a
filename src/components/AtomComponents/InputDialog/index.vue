<template>
  <el-form-item :key="atomConfig.title" v-bind="atomConfig">
    <div class="input-dialog">
      <el-input :value="value" v-bind="atomConfig.options" @input="handleInput" />
      <div class="input-dialog-mask" @click="handleClick" />
      <CoustomTableDialog
        :ref="atomConfig.title"
        :key="atomConfig.title"
        :visible.sync="dialogVisible"
        :config="atomConfig.dialogJson"
        :init-data="{ defaultSelectId: value }"
        @onSubmit="handlerSubmit"
      />
    </div>
  </el-form-item>
</template>
<script>
import CoustomTableDialog from '@/components/CoustomTableDialog';
import atomComponentsMixin from '@/mixin/atomComponents.js';
export default {
  name: 'InputDialog',
  components: {
    CoustomTableDialog,
  },
  mixins: [atomComponentsMixin],
  props: {},
  data() {
    return {
      dialogVisible: false,
      initData: {},
    };
  },
  computed: {},
  beforeDestroy() {
    this.initData = {};
  },

  mounted() {
    this.initData = {
      defaultSelectId: this.value,
    };
  },
  methods: {
    handlerSubmit(item) {
      const { associationProp } = this.atomConfig;
      if (associationProp) {
        this.$emit('onFieldChange', {
          prop: associationProp,
          value: item[associationProp],
        });
      }
      this.$emit('input', item.className);
    },

    handleClick() {
      this.dialogVisible = true;
    },
  },
};
</script>
<style scoped lang="scss">
.input-dialog {
  width: 100%;
  height: 100%;
  position: relative;

  &-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}
</style>
