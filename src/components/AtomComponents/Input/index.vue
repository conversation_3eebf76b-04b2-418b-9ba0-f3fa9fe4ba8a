<template>
  <el-form-item v-bind="atomConfig" :label="label">
    <el-input
      :value="value"
      v-bind="atomConfig.options"
      :placeholder="placeholder"
      @keyup.enter.native="handleEnter(atomConfig)"
      @input="handleInput"
    >
      <template v-if="atomConfig.options.slotAppendName" slot="append">{{
        atomConfig.options.slotAppendName
      }}</template>
    </el-input>
  </el-form-item>
</template>
<script>
import atomComponentsMixin from '@/mixin/atomComponents.js';
export default {
  name: 'Input',
  mixins: [atomComponentsMixin],
  props: {},
  data() {
    return {};
  },
  computed: {
    label() {
      const { labelConfig } = this.atomConfig;
      if (labelConfig) {
        return labelConfig.labelMap[this.formData[labelConfig.associationProp]] || this.atomConfig.label;
      }
      return this.atomConfig.label;
    },
    placeholder() {
      const { labelConfig } = this.atomConfig;
      if (labelConfig) {
        const placeholder = labelConfig.labelMap[this.formData[labelConfig.associationProp]];
        return placeholder ? '请输入' + placeholder : this.atomConfig.options.placeholder;
      }
      return this.atomConfig.options.placeholder;
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleEnter(item) {},
  },
};
</script>
<style scoped lang="scss"></style>
