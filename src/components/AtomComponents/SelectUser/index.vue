<template>
  <el-form-item v-bind="atomConfig">
    <div class="select-user">
      <div class="input-mask" @click="handleClick"></div>
      <el-input
        :value="value"
        v-bind="atomConfig.options"
        @keyup.enter.native="handleEnter(atomConfig)"
        @input="handleInput"
      >
        <template v-if="atomConfig.options.slotAppendName" slot="append">{{
          atomConfig.options.slotAppendName
        }}</template>
      </el-input>
    </div>
    <user-dialog
      :visible.sync="visible"
      :selectUsers="[]"
      :isMultiple="isMultiple"
      @submit="handleUser"
    ></user-dialog>
  </el-form-item>
</template>
<script>
import atomComponentsMixin from '@/mixin/atomComponents.js';
import UserDialog from '@/views/workflow/work/components/userDialog.vue';
export default {
  name: 'SelectUser',
  mixins: [atomComponentsMixin],
  components: {
    UserDialog,
  },
  props: {},
  data() {
    return {
      visible: false,
    };
  },
  computed: {
    isMultiple() {
      return this.atomConfig.userDialogOptions?.isMultiple || false;
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleEnter(item) {},
    handleClick() {
      console.log('click');
      this.visible = true;
    },
    handleUser(selectUser) {
      const { prop, associationProp } = this.atomConfig;
      const userId = this.isMultiple ? selectUser : selectUser[0].userId;
      const nickName = this.isMultiple ? selectUser : selectUser[0].nickName;
      // TODO: 多选和单选的值不一样, 多选后续需要兼容一下
      this.$emit('onFieldChange', {
        prop: prop,
        value: nickName,
      });

      if (associationProp) {
        this.$emit('onFieldChange', {
          prop: associationProp,
          value: userId
        });
      }
    },
  },
};
</script>
<style scoped lang="scss">
.select-user {
  width: 100%;
  height: 100%;
  position: relative;
  .input-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    opacity: 0;
    z-index: 1;
  }
}
</style>
