<template>
  <el-form-item v-bind="atomConfig">
    <div class="input-boole mb-16">
      <span>0 - </span>
      <el-form-item class="child-inline" prop="booleanFalse" :rules="rules">
        <el-input
          v-model="booleanFalse"
          maxlength="10"
          placeholder="如：关"
          @change="handleItemInput('0')"
        />
      </el-form-item>
    </div>
    <div class="input-boole">
      <span>1 - </span>
      <el-form-item class="child-inline" prop="booleanTrue" :rules="rules">
        <el-input
          v-model="booleanTrue"
          placeholder="如：开"
          maxlength="10"
          @change="handleItemInput('1')"
        />
      </el-form-item>
    </div>
  </el-form-item>
</template>
<script>
import atomComponentsMixin from '@/mixin/atomComponents.js';

export default {
  name: 'InputBoole',
  mixins: [atomComponentsMixin],
  props: {},
  data() {
    const rules = [
      {
        required: true,
        validator: (rule, value, callback) => {
          const { field } = rule;
          if (!this[field]) {
            callback(new Error('请输入布尔值描述'));
          }
          if (this.booleanFalse && this.booleanTrue && this.booleanTrue === this.booleanFalse) {
            callback(new Error('布尔值描述不能相同'));
          }
          callback();
        },
        trigger: ['blur'],
      },
    ];
    return {
      rules,
      booleanFalse: '',
      booleanTrue: '',
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.booleanFalse = this.initFormData['0'];
    this.booleanTrue = this.initFormData['1'];
  },
  methods: {
    handleItemInput(prop) {
      this.$emit('onFieldChange', {
        prop,
        value: prop === '0' ? this.booleanFalse : this.booleanTrue,
      });
    },
  },
};
</script>
<style scoped lang="scss">
.input-boole {
  display: flex;
  align-items: flex-start;

  .child-inline {
    width: 407px;
    margin-left: 6px;
  }

  &-sign {
    margin: 0 12px;
  }

  ::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }

  ::v-deep .el-form-item__error {
    position: relative;
  }
}

.mb-16 {
  margin-bottom: 16px;
}
</style>
