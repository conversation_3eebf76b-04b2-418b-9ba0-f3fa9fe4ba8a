<template>
  <component
    :is="currentComp"
    v-bind="$props"
    v-on="$listeners"
    @input="handleInput"
    @onFieldChange="handleFieldChange"
  />
</template>
<script>
import { PropertyType } from '@/constants/atomComponents';
import atomComponentsMixin from '@/mixin/atomComponents.js';
import Input from './Input';
import Select from './Select';
import Radio from './Radio';
import DatePicker from './DatePicker';
import InputDialog from './InputDialog';
import InputRange from './InputRange';
import InputEnum from './InputEnum';
import InputBoole from './InputBoole';
import IotParame from './IotParame';
import SelectDept from './SelectDept';
import SelectUser from './SelectUser';
import Switch from './Switch';
import TreeSelect from './TreeSelect';
export default {
  components: {
    Input,
  },
  mixins: [atomComponentsMixin],
  props: {},
  data() {
    return {
      componentsOption: {
        [PropertyType.INPUT]: Input,
        [PropertyType.SELECT]: Select,
        [PropertyType.RADIO]: Radio,
        [PropertyType.DATE_PICKER]: DatePicker,
        [PropertyType.INPUT_DIALOG]: InputDialog, // 点击弹出列表弹窗
        [PropertyType.INPUT_RANGE]: InputRange, // 范围
        [PropertyType.INPUT_ENUM]: InputEnum, // 枚举
        [PropertyType.INPUT_BOOLE]: InputBoole, // 布尔
        [PropertyType.IOT_PARAME]: IotParame, // 物联设备参数
        [PropertyType.SELECT_DEPT]: SelectDept, // 选择参建单位
        [PropertyType.SELECT_USER]: SelectUser, // 选择用户
        [PropertyType.SWITCH]: Switch, // 开关
        [PropertyType.TREE_SELECT]: TreeSelect, // 树形选择
      },
    };
  },
  computed: {
    currentComp() {
      return this.componentsOption[this.atomConfig.type];
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleFieldChange(values) {
      this.$emit('onFieldChange', values);
    },
  },
};
</script>
<style scoped lang="scss"></style>
