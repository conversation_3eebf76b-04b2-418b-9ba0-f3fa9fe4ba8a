<template>
  <div class="common-layout">
    <el-row :gutter="gutter">
      <el-col v-for="(item, index) in cols" :key="index" v-bind="item.colProps">
        <div
          class="common-layout__col"
          :class="[
            item.shadow ? 'common-layout__col--shadow' : '',
            `common-layout__col--${item.padding || 'none'}`,
            item.border && item.border.top ? 'common-layout__col--border-top' : '',
            item.border && item.border.right ? 'common-layout__col--border-right' : '',
            item.border && item.border.bottom ? 'common-layout__col--border-bottom' : '',
            item.border && item.border.left ? 'common-layout__col--border-left' : '',
          ]"
        >
          <slot :name="item.slot || `col-${index}`" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'CommonLayout',
  props: {
    // 列配置
    cols: {
      type: Array,
      default: () => [
        {
          colProps: { span: 24 },
          slot: 'default',
          border: { top: false, right: false, bottom: false, left: false },
        },
      ],
    },
    // 栅格间隔
    gutter: {
      type: Number,
      default: 20,
    },
  },
};
</script>

<style lang="scss" scoped>
.common-layout {
  height: 100%;

  .border-right {
    border-right: 1px solid #e5e5e5;
  }

  ::v-deep .el-row {
    height: 100%;
  }

  ::v-deep .el-col {
    height: 100%;
    padding: 0 20px;
  }

  &__col {
    height: 100%;
    background: #fff;
    border-radius: 4px;

    // 边框效果
    &--border-top {
      border-top: 1px solid #e5e5e5;
    }

    &--border-right {
      border-right: 1px solid #e5e5e5;
    }

    &--border-bottom {
      border-bottom: 1px solid #e5e5e5;
    }

    &--border-left {
      border-left: 1px solid #e5e5e5;
    }

    // 阴影效果
    &--shadow {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    // 内边距尺寸
    &--default {
      padding: 20px;
    }

    &--small {
      padding: 12px;
    }

    &--large {
      padding: 24px;
    }

    &--none {
      padding: 0;
    }
  }
}
</style>
