# CommonLayout 通用布局组件

基于 Element UI 的 Row 和 Col 组件封装的通用布局组件，用于快速实现页面的栅格布局。支持多列布局、自定义间距、阴影效果和不同尺寸的内边距。

## 功能特性

- 支持灵活的多列布局配置
- 可自定义列间距
- 提供阴影效果选项
- 支持多种内边距尺寸
- 使用插槽自由定制内容

## 基础用法

最简单的单列布局：

```vue
<template>
  <common-layout>
    <template #default> 内容区域 </template>
  </common-layout>
</template>
```

## 多列布局

配置多列布局示例：

```vue
<template>
  <common-layout
    :cols="[
      { colProps: { span: 6 }, slot: 'left' },
      { colProps: { span: 18 }, slot: 'right', shadow: true },
    ]"
    :gutter="20"
  >
    <template #left> 左侧内容 </template>
    <template #right> 右侧内容 </template>
  </common-layout>
</template>
```

## 不同内边距

支持多种内边距尺寸：

```vue
<template>
  <common-layout
    :cols="[
      { colProps: { span: 8 }, padding: 'small' },
      { colProps: { span: 8 }, padding: 'default' },
      { colProps: { span: 8 }, padding: 'large' },
    ]"
  >
    <template #col-0>小内边距</template>
    <template #col-1>默认内边距</template>
    <template #col-2>大内边距</template>
  </common-layout>
</template>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| cols | 列配置数组 | Array | [{ colProps: { span: 24 }, slot: 'default' }] |
| gutter | 栅格间隔 | Number | 20 |

### cols 配置项

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| colProps | Element Col 的 props | Object | - |
| slot | 插槽名称 | String | col-${index} |
| shadow | 是否显示阴影 | Boolean | false |
| padding | 内边距尺寸，可选值：'none'、'small'、'default'、'large' | String | 'default' |

## Slots

| 插槽名称 | 说明 |
|---------|------|
| default | 默认插槽，当只有一列时使用 |
| col-${index} | 动态插槽名，index 为列的索引，从 0 开始 |
| ${slotName} | 自定义插槽名，通过 cols 中的 slot 属性指定 |

## 样式说明

组件提供了以下预设样式：

- 内边距尺寸：
  - none: 无内边距
  - small: 12px
  - default: 20px
  - large: 24px

- 阴影效果：
  当 `shadow` 设置为 `true` 时，会添加阴影样式

## 使用示例

### 常见的左右布局

```vue
<template>
  <common-layout
    :cols="[
      {
        colProps: { span: 6 },
        slot: 'sidebar',
        shadow: true,
      },
      {
        colProps: { span: 18 },
        slot: 'content',
        padding: 'large',
      },
    ]"
  >
    <template #sidebar> 侧边栏内容 </template>
    <template #content> 主要内容区域 </template>
  </common-layout>
</template>
```

### 三列等宽布局

```vue
<template>
  <common-layout
    :cols="[{ colProps: { span: 8 } }, { colProps: { span: 8 } }, { colProps: { span: 8 } }]"
    :gutter="10"
  >
    <template #col-0>左侧内容</template>
    <template #col-1>中间内容</template>
    <template #col-2>右侧内容</template>
  </common-layout>
</template>
```

## 注意事项

1. colProps 支持 Element Col 组件的所有属性，包括响应式属性
2. 未指定 slot 名称时，将使用 `col-${index}` 作为默认插槽名
3. 内边距样式可以通过 padding 属性快速配置，也可以通过自定义类名覆盖
4. 组件默认具有白色背景和圆角边框样式