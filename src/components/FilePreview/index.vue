<template>
  <div class="file-preview">
    <!-- 预览弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :title="title"
      width="80%"
      :append-to-body="true"
      :fullscreen="isFullscreen"
      :before-close="handleClose"
      custom-class="preview-dialog"
    >
      <div class="preview-container" v-if="dialogVisible">
        <!-- 图片预览 -->
        <image-preview
          v-if="isImage"
          :url="fileUrl"
          :preview-list="previewList"
          @close="handleClose"
        />

        <!-- PDF预览 -->
        <pdf-preview
          v-else-if="isPdf"
          :url="fileUrl"
          @close="handleClose"
        />

        <!-- Office预览 -->
        <office-preview
          v-else-if="isOffice"
          :url="fileUrl"
          :file-type="fileType"
          @close="handleClose"
        />

        <!-- 视频预览 -->
        <video-preview
          v-else-if="isVideo"
          :url="fileUrl"
          @close="handleClose"
        />

        <!-- 不支持的文件类型 -->
        <div v-else class="unsupported">
          <i class="el-icon-warning"></i>
          <p>暂不支持该文件类型的预览</p>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          icon="el-icon-full-screen"
          @click="toggleFullscreen"
        >
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImagePreview from './components/ImagePreview.vue'
import PdfPreview from './components/PdfPreview.vue'
import OfficePreview from './components/OfficePreview.vue'
import VideoPreview from './components/VideoPreview.vue'

// 支持的文件类型
const FILE_TYPES = {
  // 图片
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
  // PDF
  PDF: ['pdf'],
  // Office
  OFFICE: {
    WORD: ['doc', 'docx'],
    EXCEL: ['xls', 'xlsx'],
    PPT: ['ppt', 'pptx']
  },
  // 视频
  VIDEO: ['mp4', 'webm', 'ogg']
}

export default {
  name: 'FilePreview',

  components: {
    ImagePreview,
    PdfPreview,
    OfficePreview,
    VideoPreview
  },

  props: {
    // 是否显示预览弹窗
    visible: {
      type: Boolean,
      default: false
    },
    // 文件URL
    url: {
      type: String,
      required: true
    },
    // 文件名
    fileName: {
      type: String,
      default: ''
    },
    // 预览列表(用于图片预览时切换)
    previewList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      dialogVisible: false,
      isFullscreen: false,
      fileUrl: '',
      fileType: '',
      title: ''
    }
  },

  computed: {
    // 是否为图片
    isImage() {
      return FILE_TYPES.IMAGE.includes(this.fileType)
    },
    // 是否为PDF
    isPdf() {
      return FILE_TYPES.PDF.includes(this.fileType)
    },
    // 是否为Office文档
    isOffice() {
      return Object.values(FILE_TYPES.OFFICE).some(types => types.includes(this.fileType))
    },
    // 是否为视频
    isVideo() {
      return FILE_TYPES.VIDEO.includes(this.fileType)
    }
  },

  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.init()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },

  methods: {
    // 初始化
    init() {
      this.fileUrl = this.url
      this.fileType = this.getFileType()
      this.title = this.fileName || '文件预览'


      console.log('初始化', this.fileUrl, this.fileType, this.title);
    },

    // 获取文件类型
    getFileType() {
      const fileName = this.fileName || this.url
      return fileName.split('.').pop().toLowerCase()
    },

    // 切换全屏
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
    },

    // 关闭预览
    handleClose() {
      this.dialogVisible = false
      this.isFullscreen = false
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.file-preview {
  .preview-dialog {
    .preview-container {
      min-height: 400px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .unsupported {
      text-align: center;
      color: #909399;

      i {
        font-size: 48px;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
