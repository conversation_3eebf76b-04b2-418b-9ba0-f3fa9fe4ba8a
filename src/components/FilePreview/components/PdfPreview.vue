<template>
  <div class="pdf-preview">
    <iframe
      :src="pdfUrl"
      frameborder="0"
      width="100%"
      height="100%"
    ></iframe>
  </div>
</template>

<script>
export default {
  name: 'PdfPreview',

  props: {
    url: {
      type: String,
      required: true
    }
  },

  computed: {
    pdfUrl() {
      // 使用 PDF.js 预览
      return `/static/pdfjs/web/viewer.html?file=${encodeURIComponent(this.url)}`
    }
  }
}
</script>

<style lang="scss" scoped>
.pdf-preview {
  width: 100%;
  height: 600px;
}
</style>
