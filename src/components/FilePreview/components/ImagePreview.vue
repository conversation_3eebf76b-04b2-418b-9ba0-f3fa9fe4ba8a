<template>
  <div class="image-preview">
    <div class="image-preview__toolbar" v-if="showToolbar">
      <el-button-group>
        <el-button size="mini" icon="el-icon-zoom-in" @click="handleZoomIn" />
        <el-button size="mini" icon="el-icon-zoom-out" @click="handleZoomOut" />
        <el-button size="mini" icon="el-icon-refresh-left" @click="handleRotateLeft" />
        <el-button size="mini" icon="el-icon-refresh-right" @click="handleRotateRight" />
        <el-button size="mini" icon="el-icon-refresh" @click="handleReset" />
      </el-button-group>
    </div>

    <div
      class="image-preview__container"
      @mousewheel="handleMouseWheel"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp"
    >
      <el-image
        ref="imageRef"
        :src="url"
        :preview-src-list="previewSrcList"
        :style="imageStyle"
        fit="contain"
        @load="handleImageLoad"
      >
        <div slot="error" class="image-preview__error">
          <i class="el-icon-picture-outline"></i>
          <p>图片加载失败</p>
        </div>
      </el-image>
    </div>

    <!-- 缩略图列表 -->
    <div class="image-preview__thumbs" v-if="showThumbs && previewList.length > 1">
      <div
        v-for="(item, index) in previewList"
        :key="index"
        class="thumb-item"
        :class="{ active: index === currentIndex }"
        @click="handleThumbClick(index)"
      >
        <el-image :src="item" fit="cover" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImagePreview',

  props: {
    url: {
      type: String,
      required: true
    },
    previewList: {
      type: Array,
      default: () => []
    },
    showToolbar: {
      type: Boolean,
      default: true
    },
    showThumbs: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      scale: 1,
      rotate: 0,
      offsetX: 0,
      offsetY: 0,
      isDragging: false,
      startX: 0,
      startY: 0,
      currentIndex: 0,
      imageLoaded: false
    }
  },

  computed: {
    previewSrcList() {
      return this.previewList.length ? this.previewList : [this.url]
    },

    imageStyle() {
      return {
        transform: `translate(${this.offsetX}px, ${this.offsetY}px) scale(${this.scale}) rotate(${this.rotate}deg)`,
        cursor: this.isDragging ? 'grabbing' : 'grab',
        transition: this.isDragging ? 'none' : 'transform 0.3s'
      }
    }
  },

  watch: {
    url() {
      this.handleReset()
    }
  },

  methods: {
    // 图片加载完成
    handleImageLoad() {
      this.imageLoaded = true
    },

    // 放大
    handleZoomIn() {
      this.scale = Math.min(this.scale * 1.2, 5)
    },

    // 缩小
    handleZoomOut() {
      this.scale = Math.max(this.scale / 1.2, 0.1)
    },

    // 向左旋转
    handleRotateLeft() {
      this.rotate = (this.rotate - 90) % 360
    },

    // 向右旋转
    handleRotateRight() {
      this.rotate = (this.rotate + 90) % 360
    },

    // 重置
    handleReset() {
      this.scale = 1
      this.rotate = 0
      this.offsetX = 0
      this.offsetY = 0
    },

    // 鼠标滚轮缩放
    handleMouseWheel(e) {
      if (!this.imageLoaded) return
      const delta = e.wheelDelta ? e.wheelDelta : -e.detail
      if (delta > 0) {
        this.handleZoomIn()
      } else {
        this.handleZoomOut()
      }
      e.preventDefault()
    },

    // 开始拖动
    handleMouseDown(e) {
      if (!this.imageLoaded) return
      this.isDragging = true
      this.startX = e.clientX - this.offsetX
      this.startY = e.clientY - this.offsetY
    },

    // 拖动中
    handleMouseMove(e) {
      if (!this.isDragging) return
      this.offsetX = e.clientX - this.startX
      this.offsetY = e.clientY - this.startY
      e.preventDefault()
    },

    // 结束拖动
    handleMouseUp() {
      this.isDragging = false
    },

    // 点击缩略图
    handleThumbClick(index) {
      this.currentIndex = index
      this.$emit('change', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &__toolbar {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    background: rgba(0, 0, 0, 0.5);
    padding: 5px;
    border-radius: 4px;
  }

  &__container {
    width: 100%;
    height: calc(100% - 100px);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .el-image {
      max-width: 100%;
      max-height: 100%;
    }
  }

  &__error {
    text-align: center;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 10px;
    }
  }

  &__thumbs {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    padding: 10px;
    display: flex;
    gap: 10px;
    overflow-x: auto;
    background: rgba(0, 0, 0, 0.5);

    .thumb-item {
      flex: 0 0 80px;
      height: 80px;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all 0.3s;

      &.active {
        border-color: var(--el-color-primary);
      }

      .el-image {
        width: 100%;
        height: 100%;
      }

      &:hover {
        border-color: var(--el-color-primary, #409EFF);
        opacity: 0.8;
      }
    }
  }
}
</style>
