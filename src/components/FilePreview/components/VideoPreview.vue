<template>
  <div class="video-preview" ref="container"></div>
</template>

<script>
import Player from 'xgplayer'

export default {
  name: 'VideoPreview',

  props: {
    url: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      player: null
    }
  },

  computed: {
    videoType() {
      const ext = this.url.split('.').pop().toLowerCase()
      const types = {
        mp4: 'video/mp4',
        webm: 'video/webm',
        ogg: 'video/ogg'
      }
      return types[ext] || 'video/mp4'
    }
  },

  mounted() {
    this.initPlayer()
  },

  methods: {
    initPlayer() {
      this.player = new Player({
        id: this.$refs.container,
        url: this.url,
        fluid: true,
        width: '100%',
        height: '100%',
        autoplay: false,
        volume: 0.6,
        playbackRate: [0.5, 0.75, 1, 1.5, 2],
        pip: true, // 画中画
        screenShot: true, // 截图
        hotkey: true, // 快捷键
        download: true, // 下载
        playsinline: true,
        cssFullscreen: true, // CSS 全屏
        progressDot: [ // 关键帧打点
          {
            time: 5,
            text: '打点1'
          }
        ],
        thumbnail: { // 预览缩略图
          pic_num: 10,
          width: 160,
          height: 90
        }
      })
    }
  },

  beforeDestroy() {
    if (this.player) {
      this.player.destroy()
    }
  }
}
</script>

<style lang="scss" scoped>
.video-preview {
  width: 100%;
  height: 600px;
  background: #000;
}
</style>
