<template>
  <div class="office-preview">
    <!-- Word预览 -->
    <vue-office-docx
      v-if="isWordFile"
      :src="url"
      class="preview-container"
      @rendered="handleRendered"
      @error="handleError"
    />

    <!-- Excel预览 -->
    <vue-office-excel
      v-else-if="isExcelFile"
      :src="url"
      class="preview-container"
      @rendered="handleRendered"
      @error="handleError"
    />

    <!-- PPT预览 -->
    <vue-office-pptx
      v-else-if="isPPTFile"
      :src="url"
      class="preview-container"
      @rendered="handleRendered"
      @error="handleError"
    />

    <!-- 错误提示 -->
    <div v-if="showError" class="error">
      <i class="el-icon-warning"></i>
      <p>{{ errorMessage }}</p>
    </div>

    <!-- 加载中 -->
    <div v-if="loading" class="loading" v-loading="loading" element-loading-text="加载中...">
    </div>
  </div>
</template>

<script>
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
import VueOfficePptx from '@vue-office/pptx';
import '@vue-office/docx/lib/index.css';

export default {
  name: 'OfficePreview',
  components: {
    VueOfficeDocx,
    VueOfficeExcel,
    VueOfficePptx
  },

  props: {
    url: {
      type: String,
      required: true
    },
    fileType: {
      type: String,
      required: true
    },
    fileName: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      errorMessage: '不支持该文件格式的预览',
      loading: true,
      showError: false
    }
  },

  computed: {
    isWordFile() {
      return ['doc', 'docx'].includes(this.fileType.toLowerCase());
    },
    isExcelFile() {
      return ['xls', 'xlsx'].includes(this.fileType.toLowerCase());
    },
    isPPTFile() {
      return ['ppt', 'pptx'].includes(this.fileType.toLowerCase());
    }
  },

  methods: {
    handleRendered() {
      this.loading = false;
      this.showError = false;
    },

    handleError(error) {
      console.error('文件预览失败:', error);
      this.loading = false;
      this.showError = true;
      this.errorMessage = '文件预览失败，请稍后重试';
    }
  }
}
</script>

<style lang="scss" scoped>
.office-preview {
  position: relative;
  width: 100%;
  height: 600px;
  background: #fff;
  overflow: auto;

  .preview-container {
    width: 100%;
    height: 100%;
  }

  .error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #909399;
    padding: 20px;

    i {
      font-size: 32px;
      margin-bottom: 10px;
    }
  }

  .loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
