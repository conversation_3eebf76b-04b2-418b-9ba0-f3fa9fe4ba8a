<template>
  <el-dialog
    :visible="visible"
    v-bind="dialogJson.options"
    :title="`${titleOption[dialogJson.type]}${dialogJson.title}`"
    @close="handleClose"
  >
    <div :key="key">
      <slot name="head">
        <template v-if="dialogJson.headOptions">
          <el-radio-group
            v-model="headValue"
            style="margin-bottom: 20px"
            @change="handleHeardRadio"
          >
            <el-radio-button
              v-for="item in dialogJson.headOptions"
              :key="item.value"
              :label="item.value"
            >{{ item.label }}</el-radio-button>
          </el-radio-group>
        </template>
      </slot>

      <CoustomForm
        ref="coustomFormDialog"
        :form-json="formJson"
        :init-data="initFormData"
      />

      <div slot="footer" class="dialog-footer" flex="main:right">
        <el-button
          v-for="(item, index) in dialogJson.buttonList"
          :key="index"
          :loading="loadingBtnType === item.type"
          v-bind="item.options"
          @click="handleBtn(item)"
        >{{ item.label }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { associationData } from '@/utils/coustom.js';
import CoustomForm from '../CoustomForm';
export default {
  name: 'CoustomFormDialog',
  components: {
    CoustomForm,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dialogJson: {
      type: Object,
      default: () => {},
    },
    initData: {
      type: Object,
      default: () => {},
    },
    onSubmit: {
      type: Function,
      default: function() {},
    },
  },
  data() {
    return {
      headValue: this.dialogJson.defaultHeadOptions,
      loadingBtnType: '',
      titleOption: {
        Add: '新增',
        Edit: '编辑',
        Info: '详情',
      },
      key: new Date().getTime(),
    };
  },
  computed: {
    formJson() {
      console.log('object :>> this.dialogJson.formJson', this.dialogJson.formJson);
      return !this.headValue
        ? this.dialogJson.formJson
        : this.dialogJson.formJson[this.headValue] || {};
    },
    initFormData() {
      if (this.dialogJson.type === 'Add') {
        return {};
      }
      const { formItemJson = [] } = this.formJson || {};
      let initData = {};
      formItemJson.forEach(item => {
        // 基本字段回显
        if (this.initData[item.prop]) {
          initData[item.prop] = this.initData[item.prop];
        }

        // 根据回显配置项回显
        if (item.echoConfig && this.initData[item.echoConfig?.field]) {
          initData[item.echoConfig.field] = this.initData[item.echoConfig.field];
        }

        // 关联字段回显
        if (item.association) {
          const { childItems = [], jsonField } = associationData(item, this.initData) || {};

          if (childItems && childItems.length !== 0) {
            childItems.forEach(childItem => {
              initData[childItem.prop] = this.initData[childItem.prop];
            });
          }

          if (this.initData[jsonField]) {
            let jsonFields = {};
            try {
              jsonFields = JSON.parse(this.initData[jsonField]);
            } catch (error) {}
            initData = Object.assign(initData, jsonFields);
          }
        }
      });
      return initData;
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.key = new Date().getTime();
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleHeardRadio() {},

    resetLoading() {
      this.loadingBtnType = '';
    },

    handleClose() {
      this.$emit('onDialogClose');
      this.$emit('update:visible', false);
      // this.$refs.coustomFormDialog.resetForm();
    },
    async handleBtn(item) {
      const { type } = item;
      if (type === 'Cancel') {
        this.$emit('update:visible', false);
      }

      if (type === 'Ok') {
        const result = await this.$refs.coustomFormDialog.validateForm();
        if (result) {
          this.loadingBtnType = type;
          this.$emit('onSubmit', result, this.headValue);
        }
      }
    },
  },
};
</script>
<style scoped lang="scss"></style>
