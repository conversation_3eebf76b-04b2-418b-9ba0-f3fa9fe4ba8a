// 获取最新 commit 提交信息
// git 获取提交信息参考：https://www.cnblogs.com/ruiy/p/15904295.html

// import { execa, execaCommandSync } from "execa";

const execa = require("execa") ;


function getGitInfo() {
  const shortCommid = execa.commandSync('git rev-parse --short HEAD');

  const gitComMsg = `git log --pretty=format:“%s” ${shortCommid.stdout} -1`

  const { stdout, stderr } = execa.commandSync(gitComMsg);


  return {
    commitId: shortCommid.stdout,
    commitMsg: stdout
  }
}

exports.getGitInfo = getGitInfo;
