# 镜像名，如果换项目需要更换此名称
imagesName="vue-dist-docker"

echo "查看正在运行的容器"
docker ps -a | awk '/'$imagesName'/ {print $1,$2}'

docker stop $(docker ps -a | awk '/'$imagesName'/ {print $1}')

if [ $? -eq 0 ]; then
    echo "存在指定容器，删除"
    docker rm $(docker ps -a | awk '/'$imagesName'/ {print $1}')
else
     echo "未找到指定容器"
fi

echo "构建新的 images……"
docker build -t $imagesName .

echo "启动容器，端口 8289 "
docker run -p 8289:8289 --name vueDome1 -d ${imagesName}:latest

echo "容器信息"
docker ps -a | awk '/vue-dist-docker/ {print $1,$2}'

echo "清除虚悬镜像（由于新旧镜像同名，旧镜像名称被取消，从而出现仓库名、标签均为 <none> 的镜像）"
# 绕过系统提示，只考虑 24 小时前创建的镜像
docker image prune -f --filter "until=24h"



