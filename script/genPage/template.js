const { strCase } = require('./utils');

// template.js
module.exports = {
  tablePageTemplate: (config = {}) => {
    const { title, id } = config;
    const titleCease = strCase(title);
    const idCease = strCase(id);
    return `<template>
    <div class="app-container">
      <CoustomTable
        :pageButtonsJson="pageButtonsJson"
        :queryFormJson="queryFormJson"
        :tableData="tableData"
        :tableJson="tableJson"
        :total="total"
        :paginations="defaultQueryParams"
        @onButton="handlerButton"
        @onPageButton="handlerPageButton"
        @onSearch="handlerSearch"
        @onTableRowClick="handlerTableRowClick"
        @onPagination="handlerPagination"
      ></CoustomTable>

      <CoustomFormDialog
        :dialogJson="dialogJson"
        :initData="initFormData"
        :visible.sync="dialogVisible"
        @onSubmit="handlerSubmit"
        @onDialogClose="handlerDialogClose"
        ref="formDialog"
      ></CoustomFormDialog>
    </div>
  </template>
  <script>
  import CoustomTable from "@/components/CoustomTable";
  import CoustomFormDialog from "@/components/CoustomFormDialog";
  import { TableJson, QueryFormJson, PageButtonsJson, DialogJson } from "./constants";
  import {
    getDevApiIot${titleCease}List,
    postDevApiIot${titleCease},
    getDevApiIot${titleCease}${idCease},
    deleteDevApiIot${titleCease}${idCease},
    putDevApiIot${titleCease}
  } from "@/api/iot/${title}-controller.js";
  import pageMixin from "@/mixin/page";
  export default {
    name: '${titleCease}',
    mixins: [pageMixin],
    components: {
      CoustomTable,
      CoustomFormDialog
    },
    dicts: [
      'iot_class_type',
      'iot_access_type'
    ],
    provide() {
      return {
        dictData: this.dict
      }
    },
    data() {
      return {
        queryFormJson: QueryFormJson,
        pageButtonsJson: PageButtonsJson,
        tableJson: TableJson,
        dialogJson: DialogJson,
        associationFields: ['packageName'],
      }
    },
    computed: {
      packageName() {
        const { query } = this.$route;
        return query.packageName
      }
    },
    created() {
    },
    mounted() {
      this.getList({ packageName: this.packageName}, getDevApiIot${titleCease}List);
    },

    methods: {
      async handlerSubmit(formData) {
        this.handlerSave({
          formData,
          queryParams: { packageName: this.packageName },
          apis: {
            addApi: postDevApiIot${titleCease},
            editApi: putDevApiIot${titleCease}
          },
          editParams: ['${id}']
        });
      },

      handlerTableRowClick(item) {
        this.goToPage({
          path: '/carbon-iot/packages/classes/detail',
          query: {
            className: item.className
          }
        })
      },

      //新增按钮事件
      handlerPageButton(item) {
        this.pageButton({item})
      },

      async getInfo(id) {
        this.getInfoData({id, api: getDevApiIot${titleCease}${idCease}});
      },

      deleteItem(id) {
        this.deleteTableItem({id, api: deleteDevApiIot${titleCease}${idCease}});
      },

      handlerButton(btnItem) {
        this.tableButton({btnItem, idKey: '${id}'});
      },
    },
  }
  </script>
  <style scoped lang='scss'></style>`
  },

  constantsTemplate: () => {
    return `import { formItemJsonTemplate } from "@/constants/jsonTemplate.js";

const fields = {
  moduleDispname: {
    label: "模块名称",
    prop: "moduleDispname",
  },
  moduleName: {
    label: "模块标识符",
    prop: "moduleName",
  },
  remark: {
    label: "模块标描述",
    prop: "remark",
  },
}

export const DialogJson = {
  type: 'Add',
  title: "模块",
  options: {
    width: "600px",
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false,
  },
  buttonList: [
    {
      label: "确定",
      type: 'Ok',
      permi: '',
      options: {
        type: "primary",
      }
    },
    {
      label: "取消",
      type: 'Cancel',
      permi: '',
      options: {
        type: "",
      }
    },
  ],
  formJson: {
    // 表单设置
    formOption: {
      inline: false,
      labelWidth: "130px",
      size: 'small',
    },
    // 默认表单值
    defaultFormData: {},
    // 表单项
    formItemJson: [
      formItemJsonTemplate({
        label: fields.moduleDispname.label,
        prop: fields.moduleDispname.prop,
        rules: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const reg = /^(?!_)(?!.*?_$)[a-zA-Z0-9_\u4e00-\u9fa5]+$/;
              if (!reg.test(value)) {
                callback(
                  new Error(
                    "请输入以中文、字母(不区分大小写)、数字、下划线(_)随意组合的字符串"
                  )
                );
              }
              callback();
            },
            trigger: ["blur"],
          },
        ]
      }),
      formItemJsonTemplate({
        label: fields.moduleName.label,
        prop: fields.moduleName.prop,
        rules: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const reg = /^[a-zA-Z]{1}\w*$/;
              if (!reg.test(value)) {
                callback(
                  new Error(
                    "请输入以字母(不区分大小写)、数字、下划线(_)随意组合的字符串"
                  )
                );
              }
              callback();
            },
            trigger: ["blur"],
          },
        ]
      }),
      formItemJsonTemplate({
        label: fields.remark.label,
        prop: fields.remark.prop,
        requiredRule: {
          required: false,
        },
        options: {
          type: 'textarea',
          maxlength: '4096',
          showWordLimit: true,
          rows: 4
        }
      }),
    ]
  }

}`
  }
}


