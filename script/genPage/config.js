// 导入模板
const { tablePageTemplate, constantsTemplate } = require('./template');
module.exports =  {
  // 是否命令行创建
  isEnter: false,
  // 文件名
  name: 'genPageTable',
  // 默认 view 目录下生成，如果需要指定父目录则写入 fatherFileName
  path: 'dome', 
  // 子文件配置
  child: [
    {
      name: 'index.vue',
      template: (params = {}) => { 
        return tablePageTemplate({
          ...params,
          // 文件标题，替换模板
          title: 'pageTable',
        })
      },
      templateConfig: {
       
      },
    },
    {
      name: 'constants.js',
      template:  (params = {}) => { 
        return constantsTemplate ({
          ...params,
          // 文件标题，替换模板
          title: 'pageTable',
        })
      },
      templateConfig: {
        // 文件标题，替换模板
        title: '',
      },
    },
  ]
}