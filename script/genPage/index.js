// index.js
const chalk = require('chalk')
const path = require('path')
const fs = require('fs')
const resolve = (...file) => path.resolve(__dirname, ...file)
const log = message => console.log(chalk.green(`${message}`))
const successLog = message => console.log(chalk.blue(`${message}`))
const errorLog = error => console.log(chalk.red(`${error}`))
// 模板配置
const config = require('./config');

// 生成文件
const generateFile = (path, data) => {
    if (fs.existsSync(path)) {
        errorLog(`${path}文件已存在`)
        return
    }
    fs.writeFileSync(path, data, 'utf8', err => {
        if (err) {
            errorLog(err.message)
            reject(err)
        } else {
            resolve(true)
        }
    })
}

function dotExistDirectoryCreate(directory) {
    return new Promise((resolve) => {
        mkdirs(directory, function() {
            resolve(true)
        })
    })
}
// 递归创建目录
function mkdirs(directory, callback) {
    var exists = fs.existsSync(directory)
    if (exists) {
        callback()
    } else {
        mkdirs(path.dirname(directory), function() {
            fs.mkdirSync(directory)
            callback()
        })
    }
}


async function genPage(enterName) {
     // 组件名称
    const pageName = !enterName ? config.name : enterName;
    // Vue页面组件路径
    const pathStr = !config.path ? '../../src/views' : `../../src/views/${config.path}`;
    const pagePath = resolve(pathStr, pageName)

    // 判断组件文件夹是否存在
    const hasComponentExists = fs.existsSync(pagePath)
    if (hasComponentExists) {
        errorLog(`${pageName}页面已存在`)
        if (pageName) {
            process.stdin.emit('end')
        }
        return
    } else {
        log(`正在生成${pageName}目录 ${pagePath}`)
        await dotExistDirectoryCreate(pagePath)
    }
    try {
        // // 获取组件名
        // if (enterName.includes('/')) {
        //     const inputArr = enterName.split('/')
        //     pageName = inputArr[inputArr.length - 1]
        // } else {
        //     pageName = enterName
        // }

        config.child.forEach((item) => {
            const filePath = resolve(pagePath, item.name)
            log(`正在生成子文件${item.name} ${filePath}`)
            generateFile(filePath, item.template({name: pageName}))
        })

        successLog('生成成功')
    } catch (e) {
        errorLog(e.message)
    }
    
}

if (!config.isEnter) {
    genPage();
} else {
    log('请输入要生成的页面组件名称、会生成在 views/目录下')
    process.stdin.on('data', async chunk => {
        // 组件名称
        const enterName =  String(chunk).trim().toString();
        genPage(enterName);
        process.stdin.emit('end')
    })

    process.stdin.on('end', () => {
        log('exit')
        process.exit()
    })
}
