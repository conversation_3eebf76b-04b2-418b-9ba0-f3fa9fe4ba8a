module.exports = {
  wxServer: {
    // WX_COMPANY_ID: "wwed09bdbf8499c0a3", // 企业ID
    // WX_APP_ID: "1000003", // 应用ID
    // WX_APP_SECRET: "mPAusNP9lD1M-g_NEM2uDghAkSoHBJ3qvfpkUl6JEaQ", // 应用 Secret
  },
  swaggerServe: {
    // path: 'http://*************:9090',
    path: 'http://*************/dev-iotmodel-api',
    staticPath: '/v3/api-docs',
    outputDir: '/src/api'
  },
  // uploadServe: {
  //   protocol: "http",
  //   host: ["*************", "*************"],
  //   staticPath: {
  //     dev: 'riskH5dev',
  //     pro: 'riskH5Pro',
  //   },
  //   serverOption: {
  //     port: "22", // 端口一般默认22
  //     username: "tomcat", // 用户名
  //     password: "Tomcat#1s+7%4886", // 密码
  //     pathNmae: '/opt/risk/pc/html/', // 上传到服务器的位置
  //   },
  // },
  uploadServe: {
    protocol: "http",
    host: ["************", "************"], // TOOD: 11-18到期
    // host: ["**************", "**************"],
    staticPath: {
      dev: 'carbon-flowable',
      pro: 'carbon-flowable',
    },
    serverOption: {
      port: "22", // 端口一般默认22
      username: "root", // 用户名
      password: "QdF@#1s+7%4886", // 密码
      pathNmae: '/home/<USER>/nginx/html/dist/dev-carbon-vue/', // 上传到服务器的位置
      // password: "QdF@#1s+7%4886", // 密码
      // pathNmae: '/data/risksystem/hdyuan/frontapp/html/', // 上传到服务器的位置
    },
  },
};
