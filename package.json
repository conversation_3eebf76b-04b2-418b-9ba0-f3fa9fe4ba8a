{"name": "carbon-flowable", "version": "0.8.3", "description": "carbon-flowable后台管理系统", "author": "KonBAI", "license": "MIT", "scripts": {"serve": "vue-cli-service  --open serve", "build:prod": "vue-cli-service build", "preview": "node build/index.js --preview", "lint": "eslint --ext .js --ext .jsx --ext .vue src/", "lint:fix": "eslint --quiet --fix --ext .js --ext .jsx --ext  .vue src/", "serve:live": "VUE_APP_PACK_ENV=live vue-cli-service serve --port 9090", "serve:dll": "npm run dll && vue-cli-service serve", "serve:local": "VUE_APP_PACK_ENV=local vue-cli-service serve --open", "serve:mock": "VUE_APP_PACK_ENV=mock vue-cli-service serve --open", "build": "sh script/edit-version.sh && VUE_APP_PACK_ENV=dev vue-cli-service build", "build:mock:docker": "sh script/edit-version.sh && VUE_APP_PACK_ENV=mock vue-cli-service build && sh script/docker.sh", "build:docker": "sh script/edit-version.sh && vue-cli-service build && sh script/docker.sh", "build:jenkins": "sh script/jenkins.sh", "genApi": "dyp-genSwagger", "genPage": "node node_modules/@dypnb/dev-tools/dist/genPage/index.esm.js", "javaTojs": "dyp-javaTojs", "dll": "webpack -p --progress --config ./webpack.dll.conf.js", "publish:dev": "sh script/edit-version.sh && VUE_APP_PACK_ENV=dev vue-cli-service build &&  VUE_APP_PACK_ENV=dev  dyp-publish", "publish:prod:1": "sh script/edit-version.sh && VUE_APP_PACK_ENV=pro VUE_APP_LINE_VERSION=1 vue-cli-service build &&  VUE_APP_PACK_ENV=pro  VUE_APP_LINE_VERSION=1 dyp-publish", "analyzer": "NPM_CONFIG_ANALYZER=true VUE_APP_PACK_ENV=dev vue-cli-service build", "clearCache": "rm -rf ./node_modules/.cache/", "format": "prettier --write \"src/**/*.js\" \"src/**/*.vue\""}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "gitHooks": {"pre-commit": "lint-staged --allow-empty"}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/KonBAI-Q/carbon-flowable.git"}, "dependencies": {"@babel/parser": "7.7.4", "@riophae/vue-treeselect": "0.4.0", "@visactor/vtable": "^1.18.4", "@visactor/vtable-editors": "^1.18.4", "@visactor/vtable-gantt": "^1.18.4", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pptx": "^1.0.1", "@vue/composition-api": "^1.7.2", "axios": "0.24.0", "bpmn-js-bpmnlint": "^0.21.0", "bpmn-js-token-simulation": "0.31.1", "bpmnlint": "^9.2.0", "bpmnlint-loader": "^0.1.6", "clipboard": "2.0.8", "core-js": "^3.25.3", "dayjs": "^1.11.13", "dhtmlx-gantt": "^9.0.10", "diagram-js": "^14.1.0", "echarts": "5.4.0", "element-ui": "2.15.12", "file-drops": "^0.5.0", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "10.5.0", "html2pdf.js": "^0.10.2", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "min-dash": "^4.2.1", "nprogress": "0.2.0", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "vform-builds": "2.2.9", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-demi": "^0.14.10", "vue-esign": "^1.1.4", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xgplayer": "^3.0.0-alpha.146", "xlsx": "^0.18.5", "xml-js": "^1.6.11", "yorkie": "^2.0.0"}, "devDependencies": {"@babel/eslint-parser": "^7.23.3", "@dypnb/dev-tools": "^1.0.22", "@linzhinan/vue-code-link": "^1.0.36", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "bpmn-js": "14.2.0", "bpmn-js-properties-panel": "0.37.2", "camunda-bpmn-moddle": "4.4.1", "chalk": "4.1.0", "clean-webpack-plugin": "^1.0.1", "code-inspector-plugin": "^0.18.2", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "execa": "^5.1.1", "hard-source-webpack-plugin": "^0.13.1", "husky": "^8.0.3", "lint-staged": "^15.1.0", "moment": "^2.29.4", "ora": "^5.2.0", "prettier": "^3.0.3", "progress-bar-webpack-plugin": "^2.1.0", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "scp2": "^0.5.0", "script-ext-html-webpack-plugin": "2.1.5", "speed-measure-webpack-plugin": "^1.5.0", "svg-sprite-loader": "5.1.1", "thread-loader": "^3.0.4", "vue-template-compiler": "2.6.12", "webpack": "^4.46.0", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^3.3.12", "webpack-dashboard": "^3.3.8"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}