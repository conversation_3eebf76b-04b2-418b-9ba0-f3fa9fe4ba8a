package com.carbon.plan.domain;

import com.carbon.common.core.annotation.Excel;
import com.carbon.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 计划模板对象 cbn_plan_template
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PlanTemplate", description = "PlanTemplate")
public class PlanTemplate extends BaseEntity {

    @ApiModelProperty("${comment}")
    private Long templateId;

    @Excel(name = "模板名称")
    @ApiModelProperty("模板名称")
    private String templateName;

    @Excel(name = "项目id")
    @ApiModelProperty("项目id")
    private Long projectId;

    @Excel(name = "启用状态(0-禁用,1-启用)")
    @ApiModelProperty("启用状态(0-禁用,1-启用)")
    private Integer enableStatus;

    @Excel(name = "启用状态(0-未发布,1-已发布)")
    @ApiModelProperty("启用状态(0-未发布,1-已发布)")
    private Integer publishStatus;

    @ApiModelProperty("启用状态(0-未发布,1-已发布)")
    private String delFlag;


}



package com.carbon.plan.domain;

import com.carbon.common.core.annotation.Excel;
import com.carbon.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 计划模板-字段对象 cbn_plan_template_field
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PlanTemplateField", description = "PlanTemplateField")
public class PlanTemplateField extends BaseEntity {

    @ApiModelProperty("${comment}")
    private Long fieldId;

    @Excel(name = "模板ID")
    @ApiModelProperty("模板ID")
    private Long templateId;

    @Excel(name = "字段名称")
    @ApiModelProperty("字段名称")
    private String fieldName;

    @Excel(name = "字段显示名称")
    @ApiModelProperty("字段显示名称")
    private String fieldLabel;

    @Excel(name = " COMMENT '字段类型(varchar,int,decimal,text,datetime,date,bigint等)'")
    @ApiModelProperty(" COMMENT '字段类型(varchar,int,decimal,text,datetime,date,bigint等)'")
    private String fieldType;

    @Excel(name = "显示顺序")
    @ApiModelProperty("显示顺序")
    private Long displayOrder;

    @Excel(name = "字段描述")
    @ApiModelProperty("字段描述")
    private String fieldDesc;

    @Excel(name = "显示状态(0-禁用,1-启用)")
    @ApiModelProperty("显示状态(0-禁用,1-启用)")
    private Integer displayStatus;

    @Excel(name = "字段拥有者", readConverterExp = "s=ystem，系统，user：用户")
    @ApiModelProperty("字段拥有者")
    private String fieldOwner;

    @Excel(name = "读写标识：READ_WRITE：读写；READ_ONLY：只读")
    @ApiModelProperty("读写标识：READ_WRITE：读写；READ_ONLY：只读")
    private String rwFlag;

    @ApiModelProperty("读写标识：READ_WRITE：读写；READ_ONLY：只读")
    private String delFlag;


}
