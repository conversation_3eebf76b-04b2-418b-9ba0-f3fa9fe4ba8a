/**
 * 由Java类 PlanTemplateField 自动转换的字段定义
 */
export const fields = {
  fieldId: {
    label: '${comment}',
    prop: 'fieldId'
  },
  templateId: {
    label: '模板ID',
    prop: 'templateId'
  },
  fieldName: {
    label: '字段名称',
    prop: 'fieldName'
  },
  fieldLabel: {
    label: '字段显示名称',
    prop: 'fieldLabel'
  },
  displayOrder: {
    label: '显示顺序',
    prop: 'displayOrder'
  },
  fieldDesc: {
    label: '字段描述',
    prop: 'fieldDesc'
  },
  fieldOwner: {
    label: '字段拥有者',
    prop: 'fieldOwner'
  },
  rwFlag: {
    label: '读写标识：READ_WRITE：读写；READ_ONLY：只读',
    prop: 'rwFlag'
  },
  delFlag: {
    label: '读写标识：READ_WRITE：读写；READ_ONLY：只读',
    prop: 'delFlag'
  },
  fieldType: {
    label: 'fieldType',
    prop: 'fieldType'
  },
  displayStatus: {
    label: 'displayStatus',
    prop: 'displayStatus'
  },
};

/**
 * 使用示例：
 * 
 * // 创建表单项
 * const formItem = {
 *   label: fields.fieldId.label,
 *   prop: fields.fieldId.prop,
 *   type: 'Input'
 * };
 */

/**
 * 由Java类 PlanTemplateField 自动生成的查询表单配置
 */
export const QueryFormJson = {
  formOption: {
    inline: true,
    labelWidth: '120px',
    labelPosition: 'right',
    size: 'small',
    formFlex: {
      gutter: 20,
      span: 8
    }
  },
  formItemJson: [
    {
      label: 'PlanTemplateField信息',
      showTitle: false,
      children: [
        {
          label: fields.fieldId.label,
          prop: fields.fieldId.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.fieldId.label
          }
        },
        {
          label: fields.templateId.label,
          prop: fields.templateId.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.templateId.label
          }
        },
        {
          label: fields.fieldName.label,
          prop: fields.fieldName.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.fieldName.label
          }
        },
        {
          label: fields.fieldLabel.label,
          prop: fields.fieldLabel.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.fieldLabel.label
          }
        },
        {
          label: fields.displayOrder.label,
          prop: fields.displayOrder.prop,
          type: 'Input',
          isEnter: true,
          options: {
            clearable: true,
            placeholder: '请输入' + fields.displayOrder.label
          }
        }
      ]
    }
  ],
  buttonJson: {
    type: 'Search',
    list: [
      {
        label: '搜索',
        type: 'Query',
        permi: 'planTemplateField:manage:query',
        options: {
          icon: 'el-icon-search',
          size: 'mini',
          type: 'primary'
        }
      },
      {
        label: '重置',
        type: 'Reset',
        permi: 'planTemplateField:manage:query',
        options: {
          icon: 'el-icon-refresh',
          size: 'mini'
        }
      }
    ]
  }
};


/**
 * 由Java类 PlanTemplateField 自动生成的页面按钮配置
 */
export const PageButtonsJson = {
  rowJson: {
    gutter: 10,
  },
  buttonList: [
    {
      span: 1.5,
      label: '新增',
      permi: 'planTemplateField:manage:add',
      type: 'Add',
      options: [
        {
          icon: 'el-icon-plus',
          plain: true,
          size: 'mini',
          type: 'primary',
        },
      ],
    },
  ],
};


/**
 * 由Java类 PlanTemplateField 自动生成的表格配置
 */
export const TableJson = {
  columnJson: {
    showSelect: false,
    showIndex: true,
    selectIdKey: 'fieldId',
    data: [
      {
        type: 'index',
        width: '55',
        align: 'left',
        headerAlign: 'left',
        label: '序号'
      },
      {
        align: 'left',
        headerAlign: 'left',
        width: '180',
        label: fields.fieldId.label,
        prop: fields.fieldId.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.templateId.label,
        prop: fields.templateId.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fieldName.label,
        prop: fields.fieldName.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fieldLabel.label,
        prop: fields.fieldLabel.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.displayOrder.label,
        prop: fields.displayOrder.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fieldDesc.label,
        prop: fields.fieldDesc.prop,
        showOverflowTooltip: true
      },
      {
        align: 'left',
        headerAlign: 'left',
        label: fields.fieldOwner.label,
        prop: fields.fieldOwner.prop,
        showOverflowTooltip: true
      },
      {
        align: 'center',
        headerAlign: 'center',
        width: '130',
        label: '操作',
        type: 'func',
        fixed: 'right',
        buttonList: [
          {
            label: '编辑',
            type: 'Edit',
            permi: 'planTemplateField:manage:edit',
            options: {
              icon: 'el-icon-edit',
              size: 'mini',
              type: 'text'
            }
          },
          {
            label: '删除',
            type: 'Delete',
            permi: 'planTemplateField:manage:remove',
            options: {
              icon: 'el-icon-delete',
              size: 'mini',
              type: 'text'
            }
          }
        ]
      }
    ]
  }
};


/**
 * 由Java类 PlanTemplateField 自动生成的对话框配置
 */
export const DialogJson = {
  type: 'Add',
  title: 'PlanTemplateField',
  options: {
    width: '70%',
    appendToBody: true,
    destroyOnClose: true,
    closeOnClickModal: false
  },
  buttonList: [
    {
      label: '确定',
      type: 'Ok',
      permi: '',
      options: {
        type: 'primary'
      }
    },
    {
      label: '取消',
      type: 'Cancel',
      permi: '',
      options: {
        type: ''
      }
    }
  ],
  formJson: {
    formOption: {
      labelPosition: 'right',
      inline: false,
      labelWidth: '160px',
      size: 'small',
      formFlex: {
        gutter: 20,
        span: 8
      }
    },
    defaultFormData: {
    },
    formItemJson: [
      {
        label: '基本信息',
        showTitle: true,
        children: [
          {
            label: fields.fieldId.label,
            prop: fields.fieldId.prop,
            type: 'InputNumber',
            rules: [
              {
                required: true,
                message: '请输入' + fields.fieldId.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.fieldId.label
            }
          },
          {
            label: fields.templateId.label,
            prop: fields.templateId.prop,
            type: 'InputNumber',
            rules: [
              {
                required: true,
                message: '请输入' + fields.templateId.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.templateId.label
            }
          },
          {
            label: fields.fieldName.label,
            prop: fields.fieldName.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.fieldName.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.fieldName.label
            }
          },
          {
            label: fields.fieldLabel.label,
            prop: fields.fieldLabel.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.fieldLabel.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.fieldLabel.label
            }
          },
          {
            label: fields.displayOrder.label,
            prop: fields.displayOrder.prop,
            type: 'InputNumber',
            rules: [
              {
                required: true,
                message: '请输入' + fields.displayOrder.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.displayOrder.label
            }
          },
          {
            label: fields.fieldDesc.label,
            prop: fields.fieldDesc.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.fieldDesc.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.fieldDesc.label
            }
          },
          {
            label: fields.fieldOwner.label,
            prop: fields.fieldOwner.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.fieldOwner.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.fieldOwner.label
            }
          },
          {
            label: fields.rwFlag.label,
            prop: fields.rwFlag.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.rwFlag.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.rwFlag.label
            }
          },
          {
            label: fields.delFlag.label,
            prop: fields.delFlag.prop,
            type: 'Input',
            rules: [
              {
                required: true,
                message: '请输入' + fields.delFlag.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.delFlag.label
            }
          },
          {
            label: fields.fieldType.label,
            prop: fields.fieldType.prop,
            type: 'Select',
            rules: [
              {
                required: true,
                message: '请选择' + fields.fieldType.label,
                trigger: 'change'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请选择' + fields.fieldType.label
            },
            dictType: 'field_type',
            option: []
          },
          {
            label: fields.displayStatus.label,
            prop: fields.displayStatus.prop,
            type: 'InputNumber',
            rules: [
              {
                required: true,
                message: '请输入' + fields.displayStatus.label,
                trigger: 'blur'
              }
            ],
            options: {
              clearable: true,
              placeholder: '请输入' + fields.displayStatus.label
            }
          }
        ]
      }
    ]
  }
};
