const result = {
  total: 6,
  rows: [
    {
      createBy: 'admin',
      createTime: '2025-06-28 22:59:17',
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
      taskId: 1,
      planId: 2,
      serialNo: null,
      taskStatus: '0',
      taskCode: 'AQGL-55',
      taskName: '基坑集水井-qwe',
      taskContent: null,
      responsiblePerson: null,
      plannedStartDate: null,
      plannedEndDate: null,
      plannedDuration: null,
      actualStartDate: null,
      actualEndDate: null,
      actualDuration: null,
      parentTaskId: 0,
      ancestors: '0',
      taskLevel: null,
      predecessorTaskId: null,
      predecessorType: null,
      offsetType: null,
      offsetDays: null,
      isMilestone: 0,
      relatedPlanId: null,
      relatedTaskId: null,
      extendedFieldValue: null,
    },
    {
      createBy: 'admin',
      createTime: '2025-06-28 22:59:17',
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
      taskId: 2,
      planId: 2,
      serialNo: null,
      taskStatus: '0',
      taskCode: 'THS-12',
      taskName: '子单位工程哦',
      taskContent: null,
      responsiblePerson: null,
      plannedStartDate: null,
      plannedEndDate: null,
      plannedDuration: null,
      actualStartDate: null,
      actualEndDate: null,
      actualDuration: null,
      parentTaskId: 1,
      ancestors: '0,1',
      taskLevel: null,
      predecessorTaskId: null,
      predecessorType: null,
      offsetType: null,
      offsetDays: null,
      isMilestone: 0,
      relatedPlanId: null,
      relatedTaskId: null,
      extendedFieldValue: null,
    },
    {
      createBy: 'admin',
      createTime: '2025-06-28 22:59:17',
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
      taskId: 3,
      planId: 2,
      serialNo: null,
      taskStatus: '0',
      taskCode: 'qwe',
      taskName: 'qwe',
      taskContent: null,
      responsiblePerson: null,
      plannedStartDate: null,
      plannedEndDate: null,
      plannedDuration: null,
      actualStartDate: null,
      actualEndDate: null,
      actualDuration: null,
      parentTaskId: 2,
      ancestors: '0,1,2',
      taskLevel: null,
      predecessorTaskId: null,
      predecessorType: null,
      offsetType: null,
      offsetDays: null,
      isMilestone: 0,
      relatedPlanId: null,
      relatedTaskId: null,
      extendedFieldValue: null,
    },
    {
      createBy: 'admin',
      createTime: '2025-06-28 22:59:17',
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
      taskId: 4,
      planId: 2,
      serialNo: null,
      taskStatus: '0',
      taskCode: 'SAaa-11',
      taskName: '123123',
      taskContent: null,
      responsiblePerson: null,
      plannedStartDate: null,
      plannedEndDate: null,
      plannedDuration: null,
      actualStartDate: null,
      actualEndDate: null,
      actualDuration: null,
      parentTaskId: 1,
      ancestors: '0,1',
      taskLevel: null,
      predecessorTaskId: null,
      predecessorType: null,
      offsetType: null,
      offsetDays: null,
      isMilestone: 0,
      relatedPlanId: null,
      relatedTaskId: null,
      extendedFieldValue: null,
    },
    {
      createBy: 'admin',
      createTime: '2025-06-28 22:59:17',
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
      taskId: 5,
      planId: 2,
      serialNo: null,
      taskStatus: '0',
      taskCode: 'qwe-12',
      taskName: 'qwe',
      taskContent: null,
      responsiblePerson: null,
      plannedStartDate: null,
      plannedEndDate: null,
      plannedDuration: null,
      actualStartDate: null,
      actualEndDate: null,
      actualDuration: null,
      parentTaskId: 1,
      ancestors: '0,1',
      taskLevel: null,
      predecessorTaskId: null,
      predecessorType: null,
      offsetType: null,
      offsetDays: null,
      isMilestone: 0,
      relatedPlanId: null,
      relatedTaskId: null,
      extendedFieldValue: null,
    },
    {
      createBy: 'admin',
      createTime: '2025-06-28 22:59:17',
      updateBy: '',
      updateTime: null,
      remark: null,
      deptId: null,
      delFlag: '0',
      taskId: 6,
      planId: 2,
      serialNo: null,
      taskStatus: '0',
      taskCode: 'aS-2',
      taskName: '12',
      taskContent: null,
      responsiblePerson: null,
      plannedStartDate: null,
      plannedEndDate: null,
      plannedDuration: null,
      actualStartDate: null,
      actualEndDate: null,
      actualDuration: null,
      parentTaskId: 1,
      ancestors: '0,1',
      taskLevel: null,
      predecessorTaskId: null,
      predecessorType: null,
      offsetType: null,
      offsetDays: null,
      isMilestone: 0,
      relatedPlanId: null,
      relatedTaskId: null,
      extendedFieldValue: null,
    },
  ],
  code: 200,
  msg: '查询成功',
};

/**
 * 将扁平的任务数据转换为树状结构
 * @param {Array} data - 原始任务数据数组
 * @returns {Array} 树状结构的任务数据
 */
function convertToTreeStructure(data) {
  // 创建一个Map来存储所有节点，以taskId为key
  const nodeMap = new Map();

  // 第一步：将所有节点添加到Map中，并初始化children数组
  data.forEach(item => {
    nodeMap.set(item.taskId, {
      ...item,
      children: []
    });
  });

  // 第二步：构建树状结构
  const rootNodes = [];

  data.forEach(item => {
    const node = nodeMap.get(item.taskId);

    if (item.parentTaskId === 0) {
      // 根节点
      rootNodes.push(node);
    } else {
      // 子节点，添加到父节点的children中
      const parentNode = nodeMap.get(item.parentTaskId);
      if (parentNode) {
        parentNode.children.push(node);
      }
    }
  });

  return rootNodes;
}

/**
 * 使用ancestors字段构建树状结构的替代方法
 * @param {Array} data - 原始任务数据数组
 * @returns {Array} 树状结构的任务数据
 */
function convertToTreeStructureByAncestors(data) {
  // 按ancestors长度排序，确保父节点在子节点之前处理
  const sortedData = [...data].sort((a, b) => {
    const aDepth = a.ancestors.split(',').length;
    const bDepth = b.ancestors.split(',').length;
    return aDepth - bDepth;
  });

  const nodeMap = new Map();
  const rootNodes = [];

  sortedData.forEach(item => {
    const node = {
      ...item,
      children: []
    };

    nodeMap.set(item.taskId, node);

    if (item.parentTaskId === 0) {
      // 根节点
      rootNodes.push(node);
    } else {
      // 子节点，添加到父节点
      const parentNode = nodeMap.get(item.parentTaskId);
      if (parentNode) {
        parentNode.children.push(node);
      }
    }
  });

  return rootNodes;
}

// 使用示例
const treeData = convertToTreeStructure(result.rows);
console.log(JSON.stringify(treeData, null, 2));

// 或者使用ancestors方法
const treeDataByAncestors = convertToTreeStructureByAncestors(result.rows);
console.log(JSON.stringify(treeDataByAncestors, null, 2));
