const { tablePageTemplate, constantsTemplate } = require('./script/genPage/template.js');
const packageName = require('./package.json').name;

module.exports = {
  // 静态资源上传服务器配置
  uploadServeConfig: {
    // 项目基本信息
    projectInfo: {
      name: packageName, // 项目名称
    },
    // 本地文件打包后文件夹
    locaPath: '/dist/',
    protocol: 'http',
    staticPath: {
      dev: 'carbon-flowable',
      pro: 'carbon-flowable',
    },
    serverOption: {
      host: ['************', '************'],
      port: '22', // 端口一般默认22
      username: 'root', // 用户名
      password: 'QdF@#1s+7%4886', // 密码
      pathNmae: '/home/<USER>/nginx/html/dist/dev-carbon-vue/', // 上传到服务器的位置
    },
  },
  // 微信消息通知配置
  wxServerConfig: {
    // WX_COMPANY_ID: "wwed09bdbf8499c0a3", // 企业ID
    // WX_APP_ID: "1000003", // 应用ID
    // WX_APP_SECRET: "mPAusNP9lD1M-g_NEM2uDghAkSoHBJ3qvfpkUl6JEaQ", // 应用 Secret
  },
  // swagger 生成 vue 配置
  swaggerConfig: {
    path: 'http://************/dev-api/bpm',
    staticPath: '/v2/api-docs',
    outputDir: '/src/api/v2'
  },
  // 生成 Vue 页面配置
  genPageConfig: {
    // 是否命令行创建
    isEnter: false,
    // 文件名
    name: 'classesTopologyMap',
    // 默认 view 目录下生成，如果需要指定父目录则写入 fatherFileName
    path: 'iot',
    // 子文件配置
    child: [
      {
        name: 'index.vue',
        template: (params = {}) => {
          return tablePageTemplate({
            ...params,
            // 文件标题，替换模板
            title: 'pageTable',
            id: 'pageId',
          });
        },
        templateConfig: {},
      },
      {
        name: 'constants.js',
        template: (params = {}) => {
          return constantsTemplate({
            ...params,
            // 文件标题，替换模板
            title: 'pageTable',
          });
        },
        templateConfig: {
          // 文件标题，替换模板
          title: '',
        },
      },
    ],
  },
};
